<template>
  <div class="cursor-like-editor">
    <div class="editor-container">
      <div class="editor-header">
        <div class="title-section">
          <div class="title-icon">
            <i class="el-icon-edit"></i>
          </div>
          <div class="title-content">
            <h3>测试用例编辑器</h3>
            <p>编辑和优化您的API测试用例</p>
          </div>
        </div>
      </div>

      <div class="form-sections">
        <!-- 基本信息区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <i class="el-icon-link"></i>
            </div>
            <h4>基本信息</h4>
          </div>
          
          <div class="section-content">
            <!-- API路径字段 -->
            <div class="field-group">
              <label class="field-label">
                <span class="label-text">API路径</span>
                <span class="label-badge">必填</span>
              </label>
              <div class="field-wrapper">
                <div class="input-container">
                  <div class="input-prefix">
                    <i class="el-icon-position"></i>
                  </div>
                  <el-input
                    v-model="testCase.api_path"
                    placeholder="例如: /api/session/create"
                    @focus="onFieldFocus('api_path')"
                    @blur="onFieldBlur('api_path')"
                    class="custom-input"
                    :class="{ 'has-suggestion': hasSuggestion('api_path') }"
                  />
                </div>
                
                <!-- Cursor风格的内联建议 -->
                <div 
                  v-if="showInlineSuggestion('api_path')" 
                  class="cursor-suggestion"
                >
                  <div class="suggestion-content">
                    <span class="current">{{ testCase.api_path }}</span>
                    <span class="ghost-text">{{ getGhostText('api_path') }}</span>
                  </div>
                  <div class="suggestion-controls">
                    <div class="suggestion-hint">
                      <span class="reason">{{ getSuggestion('api_path').reason }}</span>
                    </div>
                    <div class="controls">
                      <kbd>Tab</kbd> 接受 • <kbd>Esc</kbd> 拒绝
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- HTTP方法字段 -->
            <div class="field-group">
              <label class="field-label">
                <span class="label-text">HTTP方法</span>
                <span class="label-badge">必填</span>
              </label>
              <div class="field-wrapper">
                <div class="select-container">
                  <div class="input-prefix">
                    <i class="el-icon-connection"></i>
                  </div>
                  <el-select
                    v-model="testCase.method"
                    placeholder="选择HTTP方法"
                    @focus="onFieldFocus('method')"
                    @blur="onFieldBlur('method')"
                    class="custom-select"
                    :class="{ 'has-suggestion': hasSuggestion('method') }"
                  >
                    <el-option label="GET" value="GET">
                      <span class="method-option get">GET</span>
                      <span class="method-desc">获取数据</span>
                    </el-option>
                    <el-option label="POST" value="POST">
                      <span class="method-option post">POST</span>
                      <span class="method-desc">创建数据</span>
                    </el-option>
                    <el-option label="PUT" value="PUT">
                      <span class="method-option put">PUT</span>
                      <span class="method-desc">更新数据</span>
                    </el-option>
                    <el-option label="DELETE" value="DELETE">
                      <span class="method-option delete">DELETE</span>
                      <span class="method-desc">删除数据</span>
                    </el-option>
                  </el-select>
                </div>
                
                <div 
                  v-if="showInlineSuggestion('method')" 
                  class="cursor-suggestion method-suggestion"
                >
                  <div class="suggestion-content">
                    建议使用: <strong>{{ getSuggestion('method').suggested_value }}</strong>
                  </div>
                  <div class="suggestion-controls">
                    <span class="reason">{{ getSuggestion('method').reason }}</span>
                    <div class="controls">
                      <el-button @click="acceptSuggestion('method')" size="small" type="success">接受</el-button>
                      <el-button @click="rejectSuggestion('method')" size="small" plain>拒绝</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 请求配置区域 -->
        <div class="form-section">
          <div class="section-header">
            <div class="section-icon">
              <i class="el-icon-setting"></i>
            </div>
            <h4>请求配置</h4>
          </div>
          
          <div class="section-content">
            <!-- 请求头字段 -->
            <div class="field-group">
              <label class="field-label">
                <span class="label-text">请求头</span>
                <span class="label-optional">可选</span>
                <span class="format-hint">JSON格式</span>
              </label>
              <div class="field-wrapper">
                <div class="textarea-container">
                  <div class="textarea-header">
                    <div class="syntax-label">
                      <i class="el-icon-document"></i>
                      <span>Headers</span>
                    </div>
                    <div class="format-buttons">
                      <el-button size="mini" type="text" @click="formatJsonField('headers')">
                        <i class="el-icon-magic-stick"></i>
                        格式化
                      </el-button>
                    </div>
                  </div>
                  <el-input
                    v-model="testCase.headers"
                    type="textarea"
                    :rows="4"
                    placeholder='&#123;&#10;  "Authorization": "Bearer your-token",&#10;  "Content-Type": "application/json"&#10;&#125;'
                    @focus="onFieldFocus('headers')"
                    @blur="onFieldBlur('headers')"
                    class="custom-textarea"
                    :class="{ 'has-suggestion': hasSuggestion('Authorization') }"
                  />
                </div>
                
                <div 
                  v-if="showInlineSuggestion('Authorization')" 
                  class="cursor-suggestion json-suggestion"
                >
                  <div class="json-preview">
                    <div class="before-after">
                      <div class="before">
                        <h5>当前</h5>
                        <pre>{{ formatJson(testCase.headers) || '(空)' }}</pre>
                      </div>
                      <div class="after">
                        <h5>建议</h5>
                        <pre>{{ formatAuthorizationSuggestion() }}</pre>
                      </div>
                    </div>
                  </div>
                  <div class="suggestion-controls">
                    <span class="reason">{{ getSuggestion('Authorization').reason }}</span>
                    <div class="controls">
                      <el-button @click="acceptSuggestion('Authorization')" size="small" type="success">接受</el-button>
                      <el-button @click="rejectSuggestion('Authorization')" size="small" plain>拒绝</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 请求体字段 -->
            <div class="field-group">
              <label class="field-label">
                <span class="label-text">请求体</span>
                <span class="label-optional">可选</span>
                <span class="format-hint">JSON格式</span>
              </label>
              <div class="field-wrapper">
                <div class="textarea-container">
                  <div class="textarea-header">
                    <div class="syntax-label">
                      <i class="el-icon-document-copy"></i>
                      <span>Request Body</span>
                    </div>
                    <div class="format-buttons">
                      <el-button size="mini" type="text" @click="formatJsonField('body')">
                        <i class="el-icon-magic-stick"></i>
                        格式化
                      </el-button>
                    </div>
                  </div>
                  <el-input
                    v-model="testCase.body"
                    type="textarea"
                    :rows="8"
                    placeholder='&#123;&#10;  "user_id": "test_user",&#10;  "session_data": &#123;&#10;    "key": "value"&#10;  &#125;&#10;&#125;'
                    @focus="onFieldFocus('body')"
                    @blur="onFieldBlur('body')"
                    class="custom-textarea"
                    :class="{ 'has-suggestion': hasSuggestion('body') }"
                  />
                </div>
                
                <div 
                  v-if="showInlineSuggestion('body')" 
                  class="cursor-suggestion json-suggestion"
                >
                  <div class="json-preview">
                    <div class="before-after">
                      <div class="before">
                        <h5>当前</h5>
                        <pre>{{ formatJson(testCase.body) || '(空)' }}</pre>
                      </div>
                      <div class="after">
                        <h5>建议</h5>
                        <pre>{{ formatJson(getSuggestion('body').suggested_value) }}</pre>
                      </div>
                    </div>
                  </div>
                  <div class="suggestion-controls">
                    <span class="reason">{{ getSuggestion('body').reason }}</span>
                    <div class="controls">
                      <el-button @click="acceptSuggestion('body')" size="small" type="success">接受</el-button>
                      <el-button @click="rejectSuggestion('body')" size="small" plain>拒绝</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- AI分析面板 -->
    <div class="ai-panel">
      <el-card>
        <template #header>
          <div class="panel-header">
            <span>🤖 AI助手</span>
            <el-badge :value="suggestions.length" v-if="suggestions.length > 0" />
          </div>
        </template>
        
        <div v-if="!hasAnalyzed" class="analysis-trigger">
          <p>让AI分析测试用例并提供优化建议</p>
          <el-button 
            @click="startAnalysis"
            type="primary"
            :loading="analyzing"
            block
          >
            {{ analyzing ? '分析中...' : '开始分析' }}
          </el-button>
        </div>
        
        <div v-else class="analysis-result">
          <el-alert
            :title="`发现 ${suggestions.length} 个优化建议`"
            type="success"
            show-icon
            :closable="false"
          />
          
          <!-- 建议列表 -->
          <div class="suggestions-list">
            <div 
              v-for="suggestion in suggestions" 
              :key="suggestion.suggestion_id"
              class="suggestion-item"
              :class="{ 'suggestion-applied': suggestion.status === 'applied', 'suggestion-rejected': suggestion.status === 'rejected' }"
            >
              <div class="suggestion-header">
                <div class="suggestion-title">
                  <el-tag :type="getSuggestionTagType(suggestion.type)" size="small">
                    {{ suggestion.type_display || suggestion.type }}
                  </el-tag>
                  <span class="field-name">{{ suggestion.field_name }}</span>
                </div>
                <div class="confidence-score">
                  <el-tooltip content="AI置信度" placement="top">
                    <span class="confidence">{{ Math.round(suggestion.confidence * 100) }}%</span>
                  </el-tooltip>
                </div>
              </div>
              
              <div class="suggestion-content">
                <div class="value-comparison">
                  <div class="current-value">
                    <label>当前值：</label>
                    <div class="value-display">
                      {{ formatDisplayValue(suggestion.current_value) }}
                    </div>
                  </div>
                  <div class="arrow">→</div>
                  <div class="suggested-value">
                    <label>建议值：</label>
                    <div class="value-display suggested">
                      {{ formatDisplayValue(suggestion.suggested_value) }}
                    </div>
                  </div>
                </div>
                
                <div class="suggestion-reason">
                  <el-icon><InfoFilled /></el-icon>
                  <span>{{ suggestion.reason }}</span>
                </div>
              </div>
              
              <div class="suggestion-actions" v-if="suggestion.status === 'pending'">
                <el-button 
                  @click="acceptSingleSuggestion(suggestion)"
                  type="success"
                  size="small"
                  :icon="Check"
                >
                  接受
                </el-button>
                <el-button 
                  @click="rejectSingleSuggestion(suggestion)"
                  type="danger"
                  size="small"
                  plain
                  :icon="Close"
                >
                  拒绝
                </el-button>
              </div>
              
              <div class="suggestion-status" v-else>
                <el-tag 
                  :type="suggestion.status === 'applied' ? 'success' : 'danger'" 
                  size="small"
                >
                  {{ suggestion.status === 'applied' ? '已应用' : '已拒绝' }}
                </el-tag>
              </div>
            </div>
          </div>
          
          <div class="batch-actions" v-if="hasPendingSuggestions">
            <el-divider />
            <el-button 
              @click="acceptAll"
              type="success"
              size="small"
              :disabled="!hasPendingSuggestions"
            >
              全部接受
            </el-button>
            <el-button 
              @click="rejectAll"
              type="danger"
              size="small"
              plain
              :disabled="!hasPendingSuggestions"
            >
              全部拒绝
            </el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Check, Close, InfoFilled } from '@element-plus/icons-vue'
import { analyzeFailureAndSuggest, applySuggestions, rejectSuggestions } from '@/api/testcaseSuggestions'
import { 
  mockAnalysisResponse, 
  mockApplyResponse, 
  mockRejectResponse, 
  mockDelay, 
  useMockData 
} from '@/api/mockData'

const props = defineProps({
  testCaseId: {
    type: [String, Number],
    required: true
  },
  initialTestCase: {
    type: Object,
    default: () => ({
      api_path: '',
      method: 'GET',
      body: '',
      headers: ''
    })
  }
})

const emit = defineEmits(['test-case-updated'])

// 响应式数据
const testCase = ref({
  api_path: props.initialTestCase.api_path || '/api/session/create1231243',
  method: props.initialTestCase.method || 'GET', 
  body: formatInitialBody(props.initialTestCase.body),
  headers: formatInitialHeaders(props.initialTestCase.headers)
})

// 格式化初始headers数据
function formatInitialHeaders(headers) {
  if (!headers) return '{}'
  
  // 如果是对象，转换为JSON字符串
  if (typeof headers === 'object' && headers !== null) {
    return JSON.stringify(headers, null, 2)
  }
  
  // 如果是字符串但显示为[object Object]，设置为空对象
  if (headers === '[object Object]') {
    return '{}'
  }
  
  return headers
}

// 格式化初始body数据
function formatInitialBody(body) {
  if (!body) return ''
  
  // 如果是对象，转换为JSON字符串
  if (typeof body === 'object' && body !== null) {
    return JSON.stringify(body, null, 2)
  }
  
  // 如果是字符串但显示为[object Object]，设置为空
  if (body === '[object Object]') {
    return ''
  }
  
  return body
}
const suggestions = ref([])
const focusedField = ref('')
const analyzing = ref(false)
const hasAnalyzed = ref(false)

// 计算属性
const hasPendingSuggestions = computed(() => {
  return suggestions.value.some(s => s.status === 'pending')
})

// 方法
const hasSuggestion = (fieldName) => {
  return suggestions.value.some(s => s.field_name === fieldName && s.status === 'pending')
}

const getSuggestion = (fieldName) => {
  return suggestions.value.find(s => s.field_name === fieldName && s.status === 'pending')
}

const showInlineSuggestion = (fieldName) => {
  return focusedField.value === fieldName && hasSuggestion(fieldName)
}

const getGhostText = (fieldName) => {
  const suggestion = getSuggestion(fieldName)
  if (!suggestion) return ''
  
  const current = testCase.value[fieldName] || ''
  const suggested = suggestion.suggested_value
  
  if (suggested.startsWith(current)) {
    return suggested.substring(current.length)
  }
  return suggested
}

const formatJson = (jsonStr) => {
  if (!jsonStr) return ''
  try {
    return JSON.stringify(JSON.parse(jsonStr), null, 2)
  } catch {
    return jsonStr
  }
}

// 格式化Authorization建议
const formatAuthorizationSuggestion = () => {
  const suggestion = getSuggestion('Authorization')
  if (!suggestion) return ''
  
  try {
    const headers = getHeadersObject()
    headers.Authorization = suggestion.suggested_value
    return JSON.stringify(headers, null, 2)
  } catch (e) {
    return suggestion.suggested_value
  }
}

// 格式化显示值（处理长文本）
const formatDisplayValue = (value) => {
  if (!value) return '(空)'
  
  // 如果是JWT Token或其他长字符串，截断显示
  if (typeof value === 'string' && value.length > 100) {
    return value.substring(0, 50) + '...' + value.substring(value.length - 20)
  }
  
  // 如果是JSON，尝试格式化
  if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
    try {
      const parsed = JSON.parse(value)
      return JSON.stringify(parsed, null, 2)
    } catch {
      return value
    }
  }
  
  return value
}

// 获取建议类型标签颜色
const getSuggestionTagType = (type) => {
  const typeMap = {
    'headers': 'warning',
    'body': 'primary',
    'method': 'success',
    'path': 'info',
    'assertions': 'danger'
  }
  return typeMap[type] || 'info'
}

// 统一的headers处理函数
const getHeadersObject = () => {
  let headers = {}
  if (testCase.value.headers) {
    if (typeof testCase.value.headers === 'string') {
      try {
        headers = JSON.parse(testCase.value.headers)
      } catch (e) {
        console.warn('无法解析headers JSON，使用空对象:', e)
        headers = {}
      }
    } else if (typeof testCase.value.headers === 'object') {
      headers = { ...testCase.value.headers }
    }
  }
  return headers
}

const setHeadersObject = (headers) => {
  testCase.value.headers = JSON.stringify(headers, null, 2)
}

// 接受单个建议
const acceptSingleSuggestion = async (suggestion) => {
  try {
    console.log('开始应用建议:', suggestion)
    
    // 更新对应字段值（需要映射中文字段名到英文字段名）
    if (suggestion.field_name === 'Authorization') {
      // 对于Authorization字段，需要更新到headers中
      const headers = getHeadersObject()
      headers.Authorization = suggestion.suggested_value
      setHeadersObject(headers)
    } else if (suggestion.field_name === 'case_request_headers' || suggestion.field_name === 'headers' || suggestion.field_name === '请求头') {
      // 请求头字段映射到headers
      testCase.value.headers = suggestion.suggested_value
    } else if (suggestion.field_name === 'case_request_body' || suggestion.field_name === 'body' || suggestion.field_name === '请求体') {
      // 对于body字段，确保是字符串格式
      if (typeof suggestion.suggested_value === 'object') {
        testCase.value.body = JSON.stringify(suggestion.suggested_value, null, 2)
      } else {
        testCase.value.body = suggestion.suggested_value
      }
    } else if (suggestion.field_name === 'case_api_path' || suggestion.field_name === 'case_path' || suggestion.field_name === 'api_path' || suggestion.field_name === 'API路径') {
      // API路径字段（支持中英文字段名）
      testCase.value.api_path = suggestion.suggested_value
    } else if (suggestion.field_name === 'case_method' || suggestion.field_name === 'method' || suggestion.field_name === 'HTTP方法') {
      // HTTP方法字段（支持中英文字段名）
      testCase.value.method = suggestion.suggested_value
    } else {
      // 其他字段直接更新
      testCase.value[suggestion.field_name] = suggestion.suggested_value
    }
    
    // 强制触发响应式更新
    await nextTick()
    
    console.log('字段更新完成:', {
      field: suggestion.field_name,
      oldValue: suggestion.current_value,
      newValue: suggestion.suggested_value,
      currentTestCase: { ...testCase.value }
    })
    
    // 验证更新是否生效
    console.log('当前testCase值:', testCase.value)
    
    // 如果启用模拟数据
    if (useMockData) {
      await mockDelay(500)
      suggestion.status = 'applied'
      ElMessage.success('建议已应用 (模拟数据)')
      emit('test-case-updated', testCase.value)
      return
    }
    
    const response = await applySuggestions({
      suggestion_ids: [suggestion.suggestion_id],
      apply_reason: '用户手动接受建议',
      auto_save: true
    })

    if (response.data && response.data.success) {
      suggestion.status = 'applied'
      ElMessage.success('建议已应用')
      emit('test-case-updated', testCase.value)
    } else {
      ElMessage.error(response.data?.message || '应用建议失败')
    }
  } catch (error) {
    console.error('应用建议失败:', error)
    ElMessage.error('应用失败：' + (error.response?.data?.message || error.message || '网络错误'))
  }
}

// 拒绝单个建议
const rejectSingleSuggestion = async (suggestion) => {
  try {
    // 如果启用模拟数据
    if (useMockData) {
      await mockDelay(300)
      suggestion.status = 'rejected'
      ElMessage.info('建议已拒绝 (模拟数据)')
      return
    }
    
    const response = await rejectSuggestions({
      suggestion_ids: [suggestion.suggestion_id],
      reject_reason: '用户手动拒绝'
    })

    if (response.data && response.data.success) {
      suggestion.status = 'rejected'
      ElMessage.info('建议已拒绝')
    } else {
      ElMessage.error(response.data?.message || '拒绝建议失败')
    }
  } catch (error) {
    console.error('拒绝建议失败:', error)
    ElMessage.error('拒绝失败：' + (error.response?.data?.message || error.message || '网络错误'))
  }
}

// 格式化JSON字段
const formatJsonField = (fieldName) => {
  try {
    const value = testCase.value[fieldName]
    if (value && typeof value === 'string') {
      const parsed = JSON.parse(value)
      testCase.value[fieldName] = JSON.stringify(parsed, null, 2)
      ElMessage.success('JSON格式化成功')
    }
  } catch (error) {
    ElMessage.error('JSON格式不正确，无法格式化')
  }
}

// 事件处理
const onFieldFocus = (fieldName) => {
  focusedField.value = fieldName
}

const onFieldBlur = (fieldName) => {
  setTimeout(() => {
    if (focusedField.value === fieldName) {
      focusedField.value = ''
    }
  }, 150)
}

// AI分析
const startAnalysis = async () => {
  try {
    analyzing.value = true
    
    // 如果启用模拟数据
    if (useMockData) {
      console.log('🧪 使用模拟数据进行测试')
      await mockDelay(2000) // 模拟网络延迟
      
      const response = mockAnalysisResponse
      suggestions.value = response.data.data.suggestions
      hasAnalyzed.value = true
      ElMessage.success(`分析完成！生成了 ${suggestions.value.length} 条建议 (模拟数据)`)
      return
    }
    
    const response = await analyzeFailureAndSuggest({
      test_case_id: props.testCaseId,
      analysis_options: {
        include_performance: true,
        include_compatibility: true,
        confidence_threshold: 0.7
      }
    })

    // 处理后端响应数据
    if (response.data && response.data.success) {
      suggestions.value = response.data.data?.suggestions || response.data.suggestions || []
      hasAnalyzed.value = true
      ElMessage.success(`分析完成！生成了 ${suggestions.value.length} 条建议`)
    } else {
      ElMessage.error(response.data?.message || response.data?.error || '分析失败')
    }
  } catch (error) {
    console.error('AI分析失败:', error)
    ElMessage.error('分析失败：' + (error.response?.data?.message || error.message || '网络错误'))
  } finally {
    analyzing.value = false
  }
}

// 接受建议
const acceptSuggestion = async (fieldName) => {
  const suggestion = getSuggestion(fieldName)
  if (!suggestion) return

  try {
    console.log('接受建议 (内联):', suggestion)
    
    // 先更新本地数据（需要映射中文字段名到英文字段名）
    if (suggestion.field_name === 'Authorization') {
      // 对于Authorization字段，需要更新到headers中
      const headers = getHeadersObject()
      headers.Authorization = suggestion.suggested_value
      setHeadersObject(headers)
    } else if (suggestion.field_name === 'case_request_headers' || suggestion.field_name === 'headers' || suggestion.field_name === '请求头') {
      // 请求头字段映射到headers
      testCase.value.headers = suggestion.suggested_value
    } else if (suggestion.field_name === 'case_request_body' || suggestion.field_name === 'body' || suggestion.field_name === '请求体') {
      // 对于body字段，确保是字符串格式
      if (typeof suggestion.suggested_value === 'object') {
        testCase.value.body = JSON.stringify(suggestion.suggested_value, null, 2)
      } else {
        testCase.value.body = suggestion.suggested_value
      }
    } else if (suggestion.field_name === 'case_api_path' || suggestion.field_name === 'case_path' || suggestion.field_name === 'api_path' || suggestion.field_name === 'API路径') {
      // API路径字段（支持中英文字段名）
      testCase.value.api_path = suggestion.suggested_value
    } else if (suggestion.field_name === 'case_method' || suggestion.field_name === 'method' || suggestion.field_name === 'HTTP方法') {
      // HTTP方法字段（支持中英文字段名）
      testCase.value.method = suggestion.suggested_value
    } else {
      // 其他字段直接更新
      testCase.value[suggestion.field_name] = suggestion.suggested_value
    }
    
    // 强制触发响应式更新
    await nextTick()
    
    console.log('字段更新完成 (内联):', {
      field: suggestion.field_name,
      oldValue: suggestion.current_value,
      newValue: suggestion.suggested_value,
      currentTestCase: { ...testCase.value }
    })
    
    // 如果启用模拟数据
    if (useMockData) {
      await mockDelay(500)
      suggestion.status = 'applied'
      focusedField.value = ''
      ElMessage.success('建议已应用 (模拟数据)')
      emit('test-case-updated', testCase.value)
      return
    }
    
    const response = await applySuggestions({
      suggestion_ids: [suggestion.suggestion_id],
      apply_reason: '用户手动接受建议',
      auto_save: true
    })

    if (response.data && response.data.success) {
      suggestion.status = 'applied'
      focusedField.value = ''
      
      ElMessage.success('建议已应用')
      emit('test-case-updated', testCase.value)
    } else {
      // 如果应用失败，回滚本地修改
      if (suggestion.field_name === 'Authorization') {
        // 回滚headers中的Authorization
        const headers = getHeadersObject()
        if (suggestion.current_value) {
          headers.Authorization = suggestion.current_value
        } else {
          delete headers.Authorization
        }
        setHeadersObject(headers)
      } else if (suggestion.field_name === 'case_request_headers' || suggestion.field_name === 'headers' || suggestion.field_name === '请求头') {
        testCase.value.headers = suggestion.current_value || ''
      } else if (suggestion.field_name === 'case_api_path' || suggestion.field_name === 'case_path' || suggestion.field_name === 'api_path' || suggestion.field_name === 'API路径') {
        testCase.value.api_path = suggestion.current_value || ''
      } else if (suggestion.field_name === 'case_method' || suggestion.field_name === 'method' || suggestion.field_name === 'HTTP方法') {
        testCase.value.method = suggestion.current_value || 'GET'
      } else if (suggestion.field_name === 'case_request_body' || suggestion.field_name === 'body' || suggestion.field_name === '请求体') {
        testCase.value.body = suggestion.current_value || ''
      } else {
        testCase.value[suggestion.field_name] = suggestion.current_value || ''
      }
      ElMessage.error(response.data?.message || '应用建议失败')
    }
  } catch (error) {
    // 如果出错，回滚本地修改
    if (suggestion.field_name === 'Authorization') {
      // 回滚headers中的Authorization
      const headers = getHeadersObject()
      if (suggestion.current_value) {
        headers.Authorization = suggestion.current_value
      } else {
        delete headers.Authorization
      }
      setHeadersObject(headers)
    } else if (suggestion.field_name === 'case_request_headers' || suggestion.field_name === 'headers' || suggestion.field_name === '请求头') {
      testCase.value.headers = suggestion.current_value || ''
    } else if (suggestion.field_name === 'case_api_path' || suggestion.field_name === 'case_path' || suggestion.field_name === 'api_path' || suggestion.field_name === 'API路径') {
      testCase.value.api_path = suggestion.current_value || ''
    } else if (suggestion.field_name === 'case_method' || suggestion.field_name === 'method' || suggestion.field_name === 'HTTP方法') {
      testCase.value.method = suggestion.current_value || 'GET'
    } else if (suggestion.field_name === 'case_request_body' || suggestion.field_name === 'body' || suggestion.field_name === '请求体') {
      testCase.value.body = suggestion.current_value || ''
    } else {
      testCase.value[suggestion.field_name] = suggestion.current_value || ''
    }
    console.error('应用建议失败:', error)
    ElMessage.error('应用失败：' + (error.response?.data?.message || error.message || '网络错误'))
  }
}

// 拒绝建议
const rejectSuggestion = async (fieldName) => {
  const suggestion = getSuggestion(fieldName)
  if (!suggestion) return

  try {
    // 如果启用模拟数据
    if (useMockData) {
      await mockDelay(300)
      suggestion.status = 'rejected'
      focusedField.value = ''
      ElMessage.info('建议已拒绝 (模拟数据)')
      return
    }
    
    const response = await rejectSuggestions({
      suggestion_ids: [suggestion.suggestion_id],
      reject_reason: '用户手动拒绝'
    })

    if (response.data && response.data.success) {
      suggestion.status = 'rejected'
      focusedField.value = ''
      
      ElMessage.info('建议已拒绝')
    } else {
      ElMessage.error(response.data?.message || '拒绝建议失败')
    }
  } catch (error) {
    console.error('拒绝建议失败:', error)
    ElMessage.error('拒绝失败：' + (error.response?.data?.message || error.message || '网络错误'))
  }
}

// 批量操作
const acceptAll = async () => {
  const pending = suggestions.value.filter(s => s.status === 'pending')
  if (pending.length === 0) return
  
  try {
    // 备份原始值
    const originalValues = {}
    pending.forEach(s => {
      if (s.field_name === 'Authorization') {
        // 特殊处理Authorization字段
        originalValues['headers'] = testCase.value.headers
        
        const headers = getHeadersObject()
        headers.Authorization = s.suggested_value
        setHeadersObject(headers)
      } else if (s.field_name === 'case_request_headers' || s.field_name === 'headers' || s.field_name === '请求头') {
        // 请求头字段映射到headers
        originalValues['headers'] = testCase.value.headers
        testCase.value.headers = s.suggested_value
      } else if (s.field_name === 'case_request_body' || s.field_name === 'body' || s.field_name === '请求体') {
        // 特殊处理body字段
        originalValues['body'] = testCase.value.body
        
        if (typeof s.suggested_value === 'object') {
          testCase.value.body = JSON.stringify(s.suggested_value, null, 2)
        } else {
          testCase.value.body = s.suggested_value
        }
      } else if (s.field_name === 'case_api_path' || s.field_name === 'case_path' || s.field_name === 'api_path' || s.field_name === 'API路径') {
        // API路径字段（支持中英文字段名）
        originalValues['api_path'] = testCase.value.api_path
        testCase.value.api_path = s.suggested_value
      } else if (s.field_name === 'case_method' || s.field_name === 'method' || s.field_name === 'HTTP方法') {
        // HTTP方法字段（支持中英文字段名）
        originalValues['method'] = testCase.value.method
        testCase.value.method = s.suggested_value
      } else {
        originalValues[s.field_name] = testCase.value[s.field_name]
        testCase.value[s.field_name] = s.suggested_value
      }
    })

    const response = await applySuggestions({
      suggestion_ids: pending.map(s => s.suggestion_id),
      apply_reason: '用户批量接受所有建议',
      auto_save: true
    })

    if (response.data && response.data.success) {
      pending.forEach(s => s.status = 'applied')
      ElMessage.success(`已应用 ${pending.length} 条建议`)
      emit('test-case-updated', testCase.value)
    } else {
      // 如果失败，回滚所有修改
      pending.forEach(s => {
        if (s.field_name === 'Authorization') {
          testCase.value.headers = originalValues['headers']
        } else if (s.field_name === 'case_request_headers' || s.field_name === 'headers' || s.field_name === '请求头') {
          testCase.value.headers = originalValues['headers']
        } else if (s.field_name === 'case_api_path' || s.field_name === 'case_path' || s.field_name === 'api_path' || s.field_name === 'API路径') {
          testCase.value.api_path = originalValues['api_path']
        } else if (s.field_name === 'case_method' || s.field_name === 'method' || s.field_name === 'HTTP方法') {
          testCase.value.method = originalValues['method']
        } else if (s.field_name === 'case_request_body' || s.field_name === 'body' || s.field_name === '请求体') {
          testCase.value.body = originalValues['body']
        } else {
          testCase.value[s.field_name] = originalValues[s.field_name]
        }
      })
      ElMessage.error(response.data?.message || '批量应用失败')
    }
  } catch (error) {
    // 如果出错，回滚所有修改
    pending.forEach(s => {
      if (s.field_name === 'Authorization') {
        testCase.value.headers = originalValues['headers']
      } else if (s.field_name === 'case_request_headers' || s.field_name === 'headers' || s.field_name === '请求头') {
        testCase.value.headers = originalValues['headers']
      } else if (s.field_name === 'case_api_path' || s.field_name === 'case_path' || s.field_name === 'api_path' || s.field_name === 'API路径') {
        testCase.value.api_path = originalValues['api_path']
      } else if (s.field_name === 'case_method' || s.field_name === 'method' || s.field_name === 'HTTP方法') {
        testCase.value.method = originalValues['method']
      } else if (s.field_name === 'case_request_body' || s.field_name === 'body' || s.field_name === '请求体') {
        testCase.value.body = originalValues['body']
      } else {
        testCase.value[s.field_name] = originalValues[s.field_name]
      }
    })
    console.error('批量应用失败:', error)
    ElMessage.error('批量应用失败：' + (error.response?.data?.message || error.message || '网络错误'))
  }
}

const rejectAll = async () => {
  const pending = suggestions.value.filter(s => s.status === 'pending')
  if (pending.length === 0) return
  
  try {
    const response = await rejectSuggestions({
      suggestion_ids: pending.map(s => s.suggestion_id),
      reject_reason: '用户批量拒绝所有建议'
    })

    if (response.data && response.data.success) {
      pending.forEach(s => s.status = 'rejected')
      ElMessage.info(`已拒绝 ${pending.length} 条建议`)
    } else {
      ElMessage.error(response.data?.message || '批量拒绝失败')
    }
  } catch (error) {
    console.error('批量拒绝失败:', error)
    ElMessage.error('批量拒绝失败：' + (error.response?.data?.message || error.message || '网络错误'))
  }
}

// 键盘快捷键
const handleKeydown = (event) => {
  if (focusedField.value && hasSuggestion(focusedField.value)) {
    if (event.key === 'Tab' && !event.shiftKey) {
      event.preventDefault()
      acceptSuggestion(focusedField.value)
    } else if (event.key === 'Escape') {
      event.preventDefault()
      rejectSuggestion(focusedField.value)
    }
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.cursor-like-editor {
  display: flex;
  gap: 24px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.editor-container {
  flex: 2;
  background: #fff;
  border-radius: 12px;
  padding: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

/* 编辑器头部 */
.editor-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 24px;
  color: white;
}

.title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.title-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.title-content h3 {
  margin: 0 0 4px 0;
  font-size: 20px;
  font-weight: 600;
}

.title-content p {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

/* 表单区域 */
.form-sections {
  padding: 24px;
}

.form-section {
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.section-header {
  background: #fff;
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-bottom: 1px solid #e9ecef;
}

.section-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #409eff, #66b1ff);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.section-content {
  padding: 20px;
}

/* 字段组 */
.field-group {
  margin-bottom: 24px;
}

.field-group:last-child {
  margin-bottom: 0;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  font-weight: 600;
}

.label-text {
  color: #303133;
  font-size: 14px;
}

.label-badge {
  background: #f56c6c;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.label-optional {
  background: #909399;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
}

.format-hint {
  color: #909399;
  font-size: 12px;
  font-weight: normal;
  font-style: italic;
}

/* 输入容器 */
.input-container,
.select-container {
  position: relative;
  display: flex;
  align-items: center;
}

.input-prefix {
  position: absolute;
  left: 12px;
  z-index: 2;
  color: #909399;
  font-size: 16px;
  pointer-events: none;
}

.custom-input,
.custom-select {
  padding-left: 40px !important;
  border-radius: 8px !important;
  border: 2px solid #dcdfe6 !important;
  transition: all 0.3s ease !important;
  background: white !important;
}

.custom-input:focus,
.custom-select:focus {
  border-color: #409eff !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1) !important;
}

.custom-input.has-suggestion,
.custom-select.has-suggestion {
  border-color: #409eff !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2) !important;
}

/* 文本区域容器 */
.textarea-container {
  border-radius: 8px;
  border: 2px solid #dcdfe6;
  overflow: hidden;
  background: white;
  transition: all 0.3s ease;
}

.textarea-container:focus-within {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

.textarea-container.has-suggestion {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.textarea-header {
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.syntax-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #606266;
  font-weight: 600;
}

.format-buttons {
  display: flex;
  gap: 4px;
}

.custom-textarea {
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  background: white !important;
  resize: vertical !important;
  font-family: 'Monaco', 'Menlo', 'Consolas', monospace !important;
  font-size: 13px !important;
  line-height: 1.6 !important;
}

.custom-textarea:focus {
  border: none !important;
  box-shadow: none !important;
}

/* HTTP方法选项样式 */
.method-option {
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 600;
  font-size: 12px;
  color: white;
  margin-right: 8px;
}

.method-option.get {
  background: #67c23a;
}

.method-option.post {
  background: #409eff;
}

.method-option.put {
  background: #e6a23c;
}

.method-option.delete {
  background: #f56c6c;
}

.method-desc {
  color: #909399;
  font-size: 12px;
}

.ai-panel {
  flex: 1;
  min-width: 280px;
}

.field-wrapper {
  position: relative;
}

.cursor-input.has-suggestion,
.cursor-select.has-suggestion,
.cursor-textarea.has-suggestion {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.cursor-suggestion {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 1px solid #409eff;
  border-top: none;
  border-radius: 0 0 6px 6px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  z-index: 1000;
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.suggestion-content {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 13px;
}

.ghost-text {
  color: #909399;
  background: rgba(144, 147, 153, 0.1);
  padding: 1px 3px;
  border-radius: 2px;
}

.suggestion-controls {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reason {
  font-size: 12px;
  color: #606266;
  flex: 1;
}

.controls {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #909399;
}

.controls kbd {
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 11px;
  font-family: inherit;
}

.json-suggestion {
  max-width: 600px;
}

.json-preview {
  padding: 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.before-after {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.before h5, .after h5 {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: #606266;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.before pre {
  background: #fef0f0;
  border-left: 3px solid #f56c6c;
}

.after pre {
  background: #f0f9ff;
  border-left: 3px solid #67c23a;
}

.before pre, .after pre {
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.analysis-trigger {
  text-align: center;
  padding: 20px 0;
}

.analysis-trigger p {
  margin-bottom: 16px;
  color: #606266;
  font-size: 14px;
}

.analysis-result {
  padding: 16px 0;
}

.batch-actions {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}

/* 建议列表样式 */
.suggestions-list {
  margin-top: 16px;
  max-height: 500px;
  overflow-y: auto;
}

.suggestion-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  transition: all 0.3s ease;
}

.suggestion-item:hover {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  border-color: #409eff;
}

.suggestion-item.suggestion-applied {
  background: #f0f9ff;
  border-color: #67c23a;
}

.suggestion-item.suggestion-rejected {
  background: #fef0f0;
  border-color: #f56c6c;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.suggestion-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.confidence-score {
  display: flex;
  align-items: center;
}

.confidence {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.suggestion-content {
  margin-bottom: 12px;
}

.value-comparison {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  margin-bottom: 12px;
  padding: 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.current-value,
.suggested-value {
  flex: 1;
  min-width: 0;
}

.current-value label,
.suggested-value label {
  display: block;
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  font-weight: 600;
}

.value-display {
  background: #f5f7fa;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 8px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #606266;
  word-break: break-all;
  max-height: 120px;
  overflow-y: auto;
}

.value-display.suggested {
  background: #f0f9ff;
  border-color: #409eff;
  color: #409eff;
}

.arrow {
  display: flex;
  align-items: center;
  color: #409eff;
  font-weight: bold;
  font-size: 16px;
  margin-top: 20px;
}

.suggestion-reason {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 12px;
  background: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
  font-size: 13px;
  line-height: 1.5;
}

.suggestion-reason .el-icon {
  color: #fa8c16;
  margin-top: 2px;
}

.suggestion-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.suggestion-status {
  text-align: right;
}

/* 滚动条样式 */
.suggestions-list::-webkit-scrollbar {
  width: 6px;
}

.suggestions-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.suggestions-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.suggestions-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cursor-like-editor {
    flex-direction: column;
    padding: 16px;
    gap: 16px;
  }
  
  .editor-header {
    padding: 20px;
  }
  
  .title-section {
    gap: 12px;
  }
  
  .title-icon {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }
  
  .title-content h3 {
    font-size: 18px;
  }
  
  .form-sections {
    padding: 16px;
  }
  
  .form-section {
    margin-bottom: 16px;
  }
  
  .section-header {
    padding: 12px 16px;
  }
  
  .section-content {
    padding: 16px;
  }
  
  .field-group {
    margin-bottom: 20px;
  }
  
  .json-preview .before-after {
    grid-template-columns: 1fr;
  }
  
  .value-comparison {
    flex-direction: column;
    gap: 8px;
  }
  
  .arrow {
    margin-top: 8px;
    transform: rotate(90deg);
    align-self: center;
  }
  
  .suggestions-list {
    max-height: 300px;
  }
}

@media (max-width: 480px) {
  .cursor-like-editor {
    padding: 12px;
  }
  
  .editor-header {
    padding: 16px;
  }
  
  .title-section {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .form-sections {
    padding: 12px;
  }
  
  .section-content {
    padding: 12px;
  }
  
  .field-label {
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .textarea-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style> 