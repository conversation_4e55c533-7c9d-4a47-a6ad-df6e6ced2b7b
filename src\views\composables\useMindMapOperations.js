import { ref, computed } from 'vue';
import request from '@/utils/request';

export function useMindMapOperations(graph, selectedNode, mindMapStore) {
  const isExecuting = ref(false);
  const executionProgress = ref(0);
  
  // 执行单个节点
  const executeNode = async (nodeId) => {
    if (!nodeId || !mindMapStore) {
      throw new Error('Invalid parameters');
    }
    
    try {
      isExecuting.value = true;
      
      // 通过状态管理器执行节点
      const result = await mindMapStore.executeNode(nodeId);
      
      return result;
    } finally {
      isExecuting.value = false;
    }
  };

  // 批量执行所有节点
  const executeAllNodes = async () => {
    if (!graph.value || !mindMapStore) {
      throw new Error('Graph or store not available');
    }
    
    try {
      isExecuting.value = true;
      executionProgress.value = 0;
      
      const data = graph.value.save();
      const executableNodes = getExecutableNodes(data);
      
      if (executableNodes.length === 0) {
        return [];
      }
      
      const results = [];
      
      for (let i = 0; i < executableNodes.length; i++) {
        const node = executableNodes[i];
        
        try {
          // 检查执行条件
          if (shouldExecuteNode(node, results)) {
            const result = await mindMapStore.executeNode(node.id);
            results.push({
              nodeId: node.id,
              success: result.success,
              ...result
            });
          } else {
            // 跳过执行
            results.push({
              nodeId: node.id,
              success: false,
              skipped: true,
              message: '不满足执行条件'
            });
          }
        } catch (error) {
          results.push({
            nodeId: node.id,
            success: false,
            error: error.message
          });
        }
        
        // 更新进度
        executionProgress.value = ((i + 1) / executableNodes.length) * 100;
      }
      
      return results;
    } finally {
      isExecuting.value = false;
      executionProgress.value = 0;
    }
  };

  // 保存脑图
  const saveMap = async (projectId) => {
    if (!graph.value || !projectId) {
      throw new Error('Invalid parameters for saving');
    }
    
    try {
      const data = graph.value.save();
      
      // 格式化数据
      const formattedData = formatDataForSave(data);
      
      const saveData = {
        name: data.label || '未命名脑图',
        description: data.data?.notes || '',
        project_id: projectId,
        data: formattedData,
        layout: {
          type: 'indented',
          direction: 'LR',
          indent: 200
        },
        settings: {
          auto_execute: false,
          show_status: true,
          theme: 'default'
        }
      };
      
      const response = await request.post('/api/mindmap/save', saveData);
      
      if (response.data.code !== 200) {
        throw new Error(response.data.message || '保存失败');
      }
      
      return response.data.data;
    } catch (error) {
      console.error('保存脑图失败:', error);
      throw error;
    }
  };

  // 导出脑图
  const exportMap = () => {
    if (!graph.value) {
      throw new Error('Graph not available');
    }
    
    try {
      const data = graph.value.save();
      const formattedData = formatDataForExport(data);
      
      const exportData = {
        version: '1.0',
        type: 'mindmap',
        data: formattedData,
        metadata: {
          exportTime: new Date().toISOString(),
          nodeCount: countNodes(data),
          creator: 'API Test MindMap'
        }
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
        type: 'application/json' 
      });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `mindmap_${Date.now()}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      return true;
    } catch (error) {
      console.error('导出脑图失败:', error);
      throw error;
    }
  };

  // 添加节点
  const addNode = (parentId) => {
    if (!parentId || !mindMapStore) {
      throw new Error('Invalid parameters');
    }
    
    return mindMapStore.addNode(parentId, {
      label: '新建节点',
      data: {
        type: 'case',
        condition: 'none',
        priority: 'medium'
      }
    });
  };

  // 添加父节点
  const addParentNode = (childId) => {
    if (!childId || !mindMapStore) {
      throw new Error('Invalid parameters');
    }
    
    return mindMapStore.addParentNode(childId, {
      label: '新建父节点',
      data: {
        type: 'case',
        condition: 'none',
        priority: 'medium'
      }
    });
  };

  // 删除节点
  const removeNode = (nodeId) => {
    if (!nodeId || !mindMapStore) {
      throw new Error('Invalid parameters');
    }
    
    return mindMapStore.removeNode(nodeId);
  };

  // 复制节点
  const copyNode = (nodeId) => {
    if (!nodeId || !mindMapStore) {
      throw new Error('Invalid parameters');
    }
    
    return mindMapStore.copyNode(nodeId);
  };

  // 粘贴节点
  const pasteNode = (parentId) => {
    if (!parentId || !mindMapStore) {
      throw new Error('Invalid parameters');
    }
    
    return mindMapStore.pasteNode(parentId);
  };

  // 撤销操作
  const undo = () => {
    if (!mindMapStore) {
      return false;
    }
    
    return mindMapStore.undo();
  };

  // 重做操作
  const redo = () => {
    if (!mindMapStore) {
      return false;
    }
    
    return mindMapStore.redo();
  };

  // 辅助函数：获取可执行的节点
  const getExecutableNodes = (data) => {
    const nodes = [];
    
    const collectNodes = (node) => {
      if (node.data?.testCase) {
        nodes.push(node);
      }
      
      if (node.children) {
        node.children.forEach(collectNodes);
      }
    };
    
    collectNodes(data);
    return nodes;
  };

  // 辅助函数：检查是否应该执行节点
  const shouldExecuteNode = (node, previousResults) => {
    const condition = node.data?.condition || 'none';
    
    switch (condition) {
      case 'none':
        return true;
        
      case 'parent_success':
        // 查找父节点的执行结果
        const parentResult = findParentResult(node, previousResults);
        return parentResult?.success === true;
        
      case 'parent_fail':
        // 查找父节点的执行结果
        const parentFailResult = findParentResult(node, previousResults);
        return parentFailResult?.success === false;
        
      case 'custom':
        // 自定义条件执行
        return evaluateCustomCondition(node, previousResults);
        
      default:
        return true;
    }
  };

  // 辅助函数：查找父节点执行结果
  const findParentResult = (node, results) => {
    // 这里需要实现查找父节点结果的逻辑
    // 简化实现，实际需要根据树结构查找
    return results.find(r => r.nodeId !== node.id);
  };

  // 辅助函数：评估自定义条件
  const evaluateCustomCondition = (node, results) => {
    try {
      const customCondition = node.data?.customCondition;
      if (!customCondition) {
        return true;
      }
      
      // 创建执行上下文
      const context = {
        results,
        node,
        variables: extractVariables(results)
      };
      
      // 简单的条件评估（实际项目中应该使用更安全的方式）
      const func = new Function('context', `
        const { results, node, variables } = context;
        return ${customCondition};
      `);
      
      return func(context);
    } catch (error) {
      console.error('评估自定义条件失败:', error);
      return false;
    }
  };

  // 辅助函数：提取变量
  const extractVariables = (results) => {
    const variables = {};
    
    results.forEach(result => {
      if (result.extractions) {
        Object.assign(variables, result.extractions);
      }
    });
    
    return variables;
  };

  // 辅助函数：格式化保存数据
  const formatDataForSave = (data) => {
    const formatNode = (node) => {
      return {
        id: node.id,
        label: node.label,
        data: {
          ...node.data,
          position: {
            x: node.x || 0,
            y: node.y || 0
          }
        },
        style: node.style || {},
        children: node.children ? node.children.map(formatNode) : []
      };
    };
    
    return formatNode(data);
  };

  // 辅助函数：格式化导出数据
  const formatDataForExport = (data) => {
    return formatDataForSave(data);
  };

  // 辅助函数：统计节点数量
  const countNodes = (data) => {
    let count = 1; // 当前节点
    
    if (data.children) {
      count += data.children.reduce((sum, child) => sum + countNodes(child), 0);
    }
    
    return count;
  };

  // 计算属性
  const canExecute = computed(() => {
    return !isExecuting.value && graph.value && selectedNode.value;
  });

  const canExecuteAll = computed(() => {
    return !isExecuting.value && graph.value;
  });

  return {
    // 状态
    isExecuting,
    executionProgress,
    
    // 执行操作
    executeNode,
    executeAllNodes,
    
    // 文件操作
    saveMap,
    exportMap,
    
    // 节点操作
    addNode,
    addParentNode,
    removeNode,
    copyNode,
    pasteNode,
    
    // 历史操作
    undo,
    redo,
    
    // 计算属性
    canExecute,
    canExecuteAll
  };
} 