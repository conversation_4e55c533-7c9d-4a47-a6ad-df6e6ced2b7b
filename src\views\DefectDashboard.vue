<template>
  <Home>
    <div class="defect-dashboard-container" v-loading="loading" element-loading-text="正在加载统计数据...">
      <div class="page-header">
        <h2>缺陷统计图表</h2>
        <div class="header-actions">
          <el-select v-model="selectedProjectId" placeholder="选择项目" style="width: 200px" clearable>
            <el-option label="全部项目" :value="null" />
            <el-option 
              v-for="project in projectList"
              :key="project.id"
              :label="project.name"
              :value="project.id"
            />
          </el-select>
          <el-select v-model="timeRange" placeholder="时间范围" style="width: 120px">
            <el-option label="本周" value="week" />
            <el-option label="本月" value="month" />
            <el-option label="本季度" value="quarter" />
            <el-option label="本年" value="year" />
            <el-option label="全部" value="all" />
          </el-select>
          <el-button type="primary" @click="refreshData">
            <el-icon><Refresh /></el-icon>刷新数据
          </el-button>
          <el-button @click="exportReport">
            <el-icon><Download /></el-icon>导出报告
          </el-button>
        </div>
      </div>

      <!-- 统计卡片 -->
      <el-row :gutter="20" class="stat-cards">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card total-card">
            <div class="stat-icon">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.total }}</div>
              <div class="stat-label">总缺陷数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card open-card">
            <div class="stat-icon">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.open }}</div>
              <div class="stat-label">未解决缺陷</div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card resolved-card">
            <div class="stat-icon">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.resolved }}</div>
              <div class="stat-label">已解决缺陷</div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="stat-card urgent-card">
            <div class="stat-icon">
              <el-icon><AlarmClock /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-value">{{ stats.urgent }}</div>
              <div class="stat-label">紧急缺陷</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 图表区域 -->
      <el-row :gutter="20" class="chart-row">
        <el-col :xs="24" :md="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>缺陷状态分布</span>
              </div>
            </template>
            <div class="chart-container">
              <!-- 使用v-if确保组件在数据加载后渲染 -->
              <div v-if="chartData.statusData.length > 0" class="pie-chart">
                <div class="pie-chart-wrapper">
                  <!-- SVG饼图 -->
                  <div class="pie-svg-container">
                    <svg class="pie-svg" viewBox="0 0 200 200" width="200" height="200">
                      <!-- 饼图扇形 -->
                      <g v-for="(segment, index) in pieSegments" :key="index">
                        <path
                          :d="segment.path"
                          :fill="segment.color"
                          :class="['pie-segment', { 'pie-segment-hover': hoveredIndex === index }]"
                          @mouseenter="hoveredIndex = index"
                          @mouseleave="hoveredIndex = -1"
                          :style="{ 
                            transform: hoveredIndex === index ? 'scale(1.05)' : 'scale(1)',
                            transformOrigin: '100px 100px'
                          }"
                        />
                        <!-- 扇形标签 -->
                        <text
                          v-if="segment.percentage > 8"
                          :x="segment.labelX"
                          :y="segment.labelY"
                          class="pie-label"
                          text-anchor="middle"
                          dominant-baseline="middle"
                        >
                          {{ segment.percentage.toFixed(1) }}%
                        </text>
                      </g>
                      <!-- 中心圆 -->
                      <circle cx="100" cy="100" r="35" fill="white" stroke="#f0f0f0" stroke-width="2"/>
                      <text x="100" y="95" text-anchor="middle" class="pie-center-text">总计</text>
                      <text x="100" y="110" text-anchor="middle" class="pie-center-value">{{ totalDefects }}</text>
                    </svg>
                  </div>
                  
                  <!-- 图例 -->
                  <div class="chart-legend">
                    <div 
                      v-for="(item, index) in chartData.statusData" 
                      :key="index" 
                      class="legend-item"
                      :class="{ 'legend-item-hover': hoveredIndex === index }"
                      @mouseenter="hoveredIndex = index"
                      @mouseleave="hoveredIndex = -1"
                    >
                      <span class="legend-color" :style="{ backgroundColor: item.color }"></span>
                      <div class="legend-content">
                        <div class="legend-name">{{ item.name }}</div>
                        <div class="legend-stats">
                          <span class="legend-value">{{ item && typeof item.value === 'number' ? item.value : 0 }}</span>
                          <span class="legend-percentage">({{ getPercentage(item && typeof item.value === 'number' ? item.value : 0) }}%)</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 空数据状态 -->
              <div v-else class="empty-chart">
                <el-empty description="暂无状态分布数据" :image-size="100" />
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :xs="24" :md="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>缺陷优先级分布</span>
              </div>
            </template>
            <div class="chart-container">
              <div v-if="chartData.priorityData.length > 0" class="bar-chart">
                <!-- 实际项目中这里应该使用echarts等图表库 -->
                <div class="mock-bar-chart">
                  <div v-for="(item, index) in chartData.priorityData" :key="index" 
                       class="bar-item" 
                       :style="{ 
                         height: (item.value / getMaxPriorityValue() * 100) + '%',
                         backgroundColor: item.color 
                       }">
                    <div class="bar-label">{{ item && item.name ? item.name : '' }}</div>
                    <div class="bar-value">{{ item && typeof item.value === 'number' ? item.value : 0 }}</div>
                  </div>
                </div>
              </div>
              <!-- 空数据状态 -->
              <div v-else class="empty-chart">
                <el-empty description="暂无优先级分布数据" :image-size="100" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-row :gutter="20" class="chart-row">
        <el-col :xs="24" :md="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>缺陷趋势</span>
                <el-radio-group v-model="trendType" size="small">
                  <el-radio-button label="created">新增</el-radio-button>
                  <el-radio-button label="resolved">解决</el-radio-button>
                </el-radio-group>
              </div>
            </template>
            <div class="chart-container">
              <div v-if="chartData.trendData.length > 0" class="line-chart">
                <!-- 实际项目中这里应该使用echarts等图表库 -->
                <div class="mock-line-chart">
                  <div class="chart-main">
                    <div class="chart-axis-y">
                      <div v-for="n in 5" :key="n" class="axis-label">{{ Math.round((maxTrendValue / 5) * (5 - n)) }}</div>
                    </div>
                    <div class="chart-content">
                      <svg class="line-svg" viewBox="0 0 300 150">
                        <!-- 网格线 -->
                        <defs>
                          <pattern id="grid" width="60" height="30" patternUnits="userSpaceOnUse">
                            <path d="M 60 0 L 0 0 0 30" fill="none" stroke="#f0f0f0" stroke-width="1"/>
                          </pattern>
                        </defs>
                        <rect width="300" height="150" fill="url(#grid)" />
                        
                        <!-- 趋势线 -->
                        <polyline
                          :points="getLinePoints(chartData.trendData)"
                          fill="none"
                          stroke="#409EFF"
                          stroke-width="2"
                        />
                        <!-- 数据点 -->
                        <circle
                          v-for="(point, index) in getPoints(chartData.trendData)"
                          :key="index"
                          :cx="point.x"
                          :cy="point.y"
                          r="3"
                          fill="#409EFF"
                          stroke="white"
                          stroke-width="1"
                          class="trend-point"
                        >
                          <title>{{ chartData.trendData[index] ? `${formatDate(chartData.trendData[index].date)}: ${chartData.trendData[index][trendType]}` : '' }}</title>
                        </circle>
                      </svg>
                    </div>
                  </div>
                  <div class="chart-axis-x">
                    <div 
                      v-for="(item, index) in getFilteredDates(chartData.trendData)" 
                      :key="`date-${index}`" 
                      class="axis-label"
                      :style="{ 
                        left: `${getDateLabelPosition(item, chartData.trendData)}%`,
                        position: 'absolute',
                        transform: getDateLabelTransform(item, chartData.trendData, index, getFilteredDates(chartData.trendData).length),
                        minWidth: '40px',
                        zIndex: 10
                      }"
                      :title="item.date"
                    >
                      {{ formatDate(item.date) }}
                    </div>
                  </div>
                </div>
              </div>
              <!-- 空数据状态 -->
              <div v-else class="empty-chart">
                <el-empty description="暂无趋势数据" :image-size="100" />
              </div>
            </div>
            

          </el-card>
        </el-col>
        <el-col :xs="24" :md="12">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>缺陷分配</span>
              </div>
            </template>
            <div class="chart-container">
              <el-table
                v-if="chartData.assigneeData.length > 0"
                :data="chartData.assigneeData"
                style="width: 100%"
                :show-header="false"
                :border="false"
              >
                <el-table-column prop="name" width="120">
                  <template #default="scope">
                    <div class="assignee-item">
                      <el-avatar :size="32">{{ scope.row && scope.row.name ? scope.row.name.substring(0, 1) : '?' }}</el-avatar>
                      <span>{{ scope.row && scope.row.name ? scope.row.name : '未知' }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column>
                  <template #default="scope">
                    <div class="progress-wrapper">
                      <el-progress 
                        :percentage="(scope.row.value / getMaxAssigneeValue()) * 100" 
                        :color="scope.row.color"
                        :format="() => scope.row && typeof scope.row.value === 'number' ? scope.row.value : 0"
                        :stroke-width="20"
                      />
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <!-- 空数据状态 -->
              <div v-else class="empty-chart">
                <el-empty description="暂无负责人分配数据" :image-size="100" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 项目分布图表 - 只在查看全部项目时显示 -->
      <el-row v-if="!selectedProjectId" :gutter="20" class="chart-row">
        <el-col :span="24">
          <el-card class="chart-card">
            <template #header>
              <div class="card-header">
                <span>项目缺陷分布</span>
                <el-select v-model="projectMetric" placeholder="统计指标" style="width: 120px" size="small">
                  <el-option label="缺陷总数" value="total" />
                  <el-option label="未解决" value="open" />
                  <el-option label="已解决" value="resolved" />
                  <el-option label="解决率" value="rate" />
                </el-select>
              </div>
            </template>
            <div class="chart-container project-distribution">
              <div v-if="chartData.projectData.length > 0" class="project-cards-container">
                <div 
                  v-for="(project, index) in chartData.projectData" 
                  :key="project.name"
                  class="project-card"
                  :class="{ 'project-card-hover': hoveredProjectIndex === index }"
                  @mouseenter="hoveredProjectIndex = index"
                  @mouseleave="hoveredProjectIndex = -1"
                >
                  <div class="project-header">
                    <div class="project-info">
                      <h4 class="project-name" :title="project.name">{{ project.name }}</h4>
                      <div class="project-stats">
                        <span class="stat-item total">
                          <span class="stat-label">总计</span>
                          <span class="stat-value">{{ project.total || 0 }}</span>
                        </span>
                        <span class="stat-item open">
                          <span class="stat-label">未解决</span>
                          <span class="stat-value">{{ project.open || 0 }}</span>
                        </span>
                        <span class="stat-item resolved">
                          <span class="stat-label">已解决</span>
                          <span class="stat-value">{{ project.resolved || 0 }}</span>
                        </span>
                      </div>
                    </div>
                    <div class="project-rate">
                      <div class="rate-circle" :style="{ '--rate': getRatePercentage(project) }">
                        <span class="rate-text">{{ getRateText(project) }}%</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="project-progress">
                    <div class="progress-bar">
                      <div class="progress-track">
                        <div 
                          class="progress-fill resolved" 
                          :style="{ width: `${getRatePercentage(project)}%` }"
                        ></div>
                      </div>
                    </div>
                    <div class="progress-labels">
                      <span class="progress-label">解决进度</span>
                      <span class="progress-value">{{ project.resolved || 0 }}/{{ project.total || 0 }}</span>
                    </div>
                  </div>
                  
                  <div class="project-metrics">
                    <div class="metric-item">
                      <div class="metric-icon new">
                        <el-icon><Warning /></el-icon>
                      </div>
                      <div class="metric-info">
                        <span class="metric-label">新建缺陷</span>
                        <span class="metric-value">{{ project.newCount || 0 }}</span>
                      </div>
                    </div>
                    <div class="metric-item">
                      <div class="metric-icon processing">
                        <el-icon><Document /></el-icon>
                      </div>
                      <div class="metric-info">
                        <span class="metric-label">处理中</span>
                        <span class="metric-value">{{ project.processingCount || 0 }}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div class="project-additional-info">
                    <div class="info-row">
                      <span class="info-label">已关闭：</span>
                      <span class="info-value">{{ project.closed || 0 }}</span>
                    </div>
                    <div class="info-row">
                      <span class="info-label">缺陷率：</span>
                      <span class="info-value rate">{{ formatDefectRate(project.defectRate) }}%</span>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 空数据状态 -->
              <div v-else class="empty-chart">
                <el-empty description="暂无项目分布数据" :image-size="100" />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </Home>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Warning, CircleCheck, AlarmClock, Refresh, Download } from '@element-plus/icons-vue'
import Home from '@/components/HomePage.vue'
import {
  getDefectOverview,
  getStatusDistribution,
  getPriorityDistribution,
  getDefectTrend,
  getAssigneeStatistics,
  getProjectDistribution
} from '@/api/statistics'
import { getProjectList } from '@/api/project'

// 时间范围选择
const timeRange = ref('month')

// 项目选择
const selectedProjectId = ref(null)
const projectList = ref([])

// 加载状态
const loading = ref(false)

// 统计数据
const stats = reactive({
  total: 0,
  open: 0,
  resolved: 0,
  urgent: 0
})

// 图表数据
const chartData = reactive({
  statusData: [],
  priorityData: [],
  trendData: [],
  assigneeData: [],
  projectData: []
})

// 趋势图类型
const trendType = ref('created')

// 项目缺陷统计指标
const projectMetric = ref('total')

// 饼图悬停状态
const hoveredIndex = ref(-1)

// 项目卡片悬停状态
const hoveredProjectIndex = ref(-1)

// 计算最大趋势值，用于图表缩放
const maxTrendValue = computed(() => {
  if (!chartData.trendData || !chartData.trendData.length) return 10
  const values = chartData.trendData
    .map(item => item && typeof item[trendType.value] === 'number' ? item[trendType.value] : 0)
    .filter(v => v > 0)
  return values.length > 0 ? Math.max(...values) * 1.2 : 10
})

// 计算缺陷总数
const totalDefects = computed(() => {
  if (!chartData.statusData || !Array.isArray(chartData.statusData)) return 0
  return chartData.statusData.reduce((sum, item) => {
    return sum + (item && typeof item.value === 'number' ? item.value : 0)
  }, 0)
})

// 计算饼图扇形数据
const pieSegments = computed(() => {
  if (!chartData.statusData || !chartData.statusData.length) return []
  
  const total = totalDefects.value
  if (total === 0) return []
  
  let currentAngle = 0
  const segments = []
  
  chartData.statusData.forEach((item, index) => {
    if (!item || typeof item.value !== 'number' || item.value <= 0) return
    
    const percentage = (item.value / total) * 100
    const angle = (item.value / total) * 360
    
    // 计算扇形路径
    const startAngle = (currentAngle - 90) * (Math.PI / 180) // 从12点方向开始
    const endAngle = (currentAngle + angle - 90) * (Math.PI / 180)
    
    const x1 = 100 + 65 * Math.cos(startAngle)
    const y1 = 100 + 65 * Math.sin(startAngle)
    const x2 = 100 + 65 * Math.cos(endAngle)
    const y2 = 100 + 65 * Math.sin(endAngle)
    
    const largeArcFlag = angle > 180 ? 1 : 0
    
    const pathData = [
      'M', 100, 100, // 移动到中心点
      'L', x1, y1,   // 画线到起始点
      'A', 65, 65, 0, largeArcFlag, 1, x2, y2, // 画弧
      'Z' // 闭合路径
    ].join(' ')
    
    // 计算标签位置（扇形中心）
    const labelAngle = (currentAngle + angle / 2 - 90) * (Math.PI / 180)
    const labelRadius = 45
    const labelX = 100 + labelRadius * Math.cos(labelAngle)
    const labelY = 100 + labelRadius * Math.sin(labelAngle)
    
    segments.push({
      path: pathData,
      color: item.color,
      percentage,
      labelX,
      labelY,
      name: item.name,
      value: item.value
    })
    
    currentAngle += angle
  })
  
  return segments
})

// 计算项目解决率百分比
const getRatePercentage = (project) => {
  if (!project || !project.total || project.total === 0) return 0
  return Math.round((project.resolved / project.total) * 100)
}

// 获取解决率文本
const getRateText = (project) => {
  return getRatePercentage(project).toString()
}

// 格式化缺陷率
const formatDefectRate = (rate) => {
  if (!rate || rate === 0) return '0.0'
  return (rate * 100).toFixed(1)
}

// 计算百分比
const getPercentage = (value) => {
  const total = totalDefects.value
  return total > 0 ? ((value / total) * 100).toFixed(1) : '0.0'
}

// 获取优先级数据的最大值（安全版本）
const getMaxPriorityValue = () => {
  if (!chartData.priorityData || chartData.priorityData.length === 0) return 1
  const values = chartData.priorityData.map(i => i.value).filter(v => v != null && !isNaN(v))
  return values.length > 0 ? Math.max(...values) : 1
}

// 获取负责人数据的最大值（安全版本）
const getMaxAssigneeValue = () => {
  if (!chartData.assigneeData || chartData.assigneeData.length === 0) return 1
  const values = chartData.assigneeData.map(i => i.value).filter(v => v != null && !isNaN(v))
  return values.length > 0 ? Math.max(...values) : 1
}

// 生成折线图点
const getLinePoints = (data) => {
  if (!data || !data.length) return ''
  const width = 300
  const height = 150
  const max = maxTrendValue.value || 10
  const xStep = data.length > 1 ? width / (data.length - 1) : 0
  
  return data.map((item, index) => {
    if (!item || typeof item[trendType.value] !== 'number') {
      const x = data.length === 1 ? width / 2 : index * xStep
      return `${x},${height - 10}` // 默认在底部稍微上方
    }
    const x = data.length === 1 ? width / 2 : index * xStep
    const value = Math.max(0, item[trendType.value]) // 确保非负值
    const y = height - (value / max * (height - 20)) - 10 // 留出边距
    return `${x},${Math.max(10, y)}` // 确保不超出顶部
  }).join(' ')
}

// 生成折线图上的点
const getPoints = (data) => {
  if (!data || !data.length) return []
  const width = 300
  const height = 150
  const max = maxTrendValue.value || 10
  const xStep = data.length > 1 ? width / (data.length - 1) : 0
  
  return data.map((item, index) => {
    if (!item || typeof item[trendType.value] !== 'number') {
      const x = data.length === 1 ? width / 2 : index * xStep
      return { x, y: height - 10 }
    }
    const x = data.length === 1 ? width / 2 : index * xStep
    const value = Math.max(0, item[trendType.value])
    const y = height - (value / max * (height - 20)) - 10
    return { x, y: Math.max(10, y) }
  })
}

// 获取项目列表
const fetchProjectList = async () => {
  try {
    const res = await getProjectList()
    console.log('项目列表响应:', res.data)
    if (res.data.code === 200) {
      // 根据API响应结构，项目列表在data.projects中
      projectList.value = res.data.data.projects || []
      console.log('获取到的项目列表:', projectList.value)
    }
  } catch (error) {
    console.error('获取项目列表失败:', error)
    ElMessage.error('获取项目列表失败')
  }
}

// 获取概览统计数据
const fetchOverviewData = async () => {
  try {
    const params = { 
      timeRange: timeRange.value,
      projectId: selectedProjectId.value
    }
    const res = await getDefectOverview(params)

    
    if (res.data.code === 200) {
      const data = res.data.data
      if (data && typeof data === 'object') {
        // 根据API规范调整字段映射
        stats.total = data.total || 0
        stats.open = (data.newCount || 0) + (data.processingCount || 0)  // 新建+处理中=未解决
        stats.resolved = data.resolvedCount || 0
        stats.urgent = data.todayNew || 0  // 使用今日新增作为紧急数据
      } else {
        console.warn('概览数据格式异常:', data)
        // 重置为默认值
        stats.total = 0
        stats.open = 0
        stats.resolved = 0
        stats.urgent = 0
      }
    } else {
      console.warn('获取概览数据失败，响应码:', res.data.code)
      // 重置为默认值
      stats.total = 0
      stats.open = 0
      stats.resolved = 0
      stats.urgent = 0
    }
  } catch (error) {
    console.error('获取概览数据失败:', error)
    ElMessage.error('获取概览数据失败')
    // 重置为默认值
    stats.total = 0
    stats.open = 0
    stats.resolved = 0
    stats.urgent = 0
  }
}

// 获取状态分布数据
const fetchStatusDistribution = async () => {
  try {
    const params = { 
      timeRange: timeRange.value,
      projectId: selectedProjectId.value
    }
    const res = await getStatusDistribution(params)

    
    if (res.data.code === 200) {
      const data = res.data.data
      // 确保数据是数组格式
      if (Array.isArray(data)) {
        chartData.statusData = data.map(item => ({
          name: item.name || '未知状态',  // API规范中直接是name字段
          value: item.value || 0,        // API规范中直接是value字段
          color: item.color || getStatusColor(item.code)  // 优先使用API返回的颜色
        }))
      } else {
        console.warn('状态分布数据不是数组格式:', data)
        chartData.statusData = []
      }
    } else {
      console.warn('获取状态分布数据失败，响应码:', res.data.code)
      chartData.statusData = []
    }
  } catch (error) {
    console.error('获取状态分布数据失败:', error)
    ElMessage.error('获取状态分布数据失败')
    chartData.statusData = []
  }
}

// 获取优先级分布数据
const fetchPriorityDistribution = async () => {
  try {
    const params = { 
      timeRange: timeRange.value,
      projectId: selectedProjectId.value
    }
    const res = await getPriorityDistribution(params)

    
    if (res.data.code === 200) {
      const data = res.data.data
      // 确保数据是数组格式
      if (Array.isArray(data)) {
        chartData.priorityData = data.map(item => ({
          name: item.name || '未知优先级',  // API规范中直接是name字段
          value: item.value || 0,          // API规范中直接是value字段
          color: item.color || getPriorityColor(item.code)  // 优先使用API返回的颜色
        }))
      } else {
        console.warn('优先级分布数据不是数组格式:', data)
        chartData.priorityData = []
      }
    } else {
      console.warn('获取优先级分布数据失败，响应码:', res.data.code)
      chartData.priorityData = []
    }
  } catch (error) {
    console.error('获取优先级分布数据失败:', error)
    ElMessage.error('获取优先级分布数据失败')
    chartData.priorityData = []
  }
}

// 获取趋势数据
const fetchTrendData = async () => {
  try {
    const params = { 
      type: 'daily',  // 根据API规范使用type而不是granularity
      projectId: selectedProjectId.value,
      startDate: getStartDate(),
      endDate: getEndDate()
    }
    const res = await getDefectTrend(params)
    console.log('趋势数据响应:', res.data)
    
    if (res.data.code === 200) {
      const data = res.data.data
      console.log('趋势数据内容:', data)
      
      // 根据API规范，数据结构包含categories和series
      if (data && data.categories && data.series) {
        const categories = data.categories
        const createdSeries = data.series.find(s => s.name === '新增缺陷') || { data: [] }
        const resolvedSeries = data.series.find(s => s.name === '解决缺陷') || { data: [] }
        
        let trendData = categories.map((date, index) => ({
          date: date || '',
          created: createdSeries.data[index] || 0,
          resolved: resolvedSeries.data[index] || 0
        }))
        
        // 如果数据点太多，进行智能采样
        if (trendData.length > 30) {
          const step = Math.ceil(trendData.length / 15) // 最多保留15个数据点
          const sampledData = trendData.filter((_, index) => index % step === 0)
          
          // 确保包含最后一天的数据（通常是今天或最新数据）
          const lastItem = trendData[trendData.length - 1]
          if (!sampledData.some(item => item.date === lastItem.date)) {
            sampledData.push(lastItem)
          }
          
          trendData = sampledData
          console.log('数据点过多，已进行采样，原始:', categories.length, '采样后:', trendData.length)
        }
        
        chartData.trendData = trendData
        console.log('处理后的趋势数据:', chartData.trendData)
        const filteredDates = getFilteredDates(chartData.trendData)
        console.log('过滤后的日期标签:', filteredDates)
        console.log('时间范围检查:', {
          第一天: chartData.trendData[0]?.date,
          最后一天: chartData.trendData[chartData.trendData.length - 1]?.date,
          今天: new Date().toISOString().split('T')[0],
          标签第一天: filteredDates[0]?.date,
          标签最后一天: filteredDates[filteredDates.length - 1]?.date
        })
              } else if (Array.isArray(data)) {
          // 如果后端直接返回数组格式，进行兼容处理
          console.log('后端返回数组格式，进行兼容处理')
          chartData.trendData = data.map(item => ({
            date: item.date || item.time || '',
            created: item.created || item.createdCount || 0,
            resolved: item.resolved || item.resolvedCount || 0
          }))
          
          console.log('兼容处理后的趋势数据:', chartData.trendData)
          console.log('过滤后的日期标签:', getFilteredDates(chartData.trendData))
      } else {
        console.warn('趋势数据格式不符合预期:', data)
        // 生成模拟数据用于展示
        chartData.trendData = generateMockTrendData()
      }
    } else {
      console.warn('获取趋势数据失败，响应码:', res.data.code)
      chartData.trendData = generateMockTrendData()
    }
  } catch (error) {
    console.error('获取趋势数据失败:', error)
    ElMessage.error('获取趋势数据失败')
    chartData.trendData = generateMockTrendData()
  }
}

// 生成模拟趋势数据
const generateMockTrendData = () => {
  const data = []
  const today = new Date()
  const startDate = new Date(getStartDate())
  const endDate = new Date(getEndDate())
  
  console.log('生成模拟数据时间范围:', {
    timeRange: timeRange.value,
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0]
  })
  
  // 从开始日期到今天，每天生成一个数据点
  let currentDate = new Date(startDate)
  while (currentDate <= endDate) {
    const dateStr = currentDate.toISOString().split('T')[0]
    data.push({
      date: dateStr,
      created: Math.floor(Math.random() * 10) + 1,
      resolved: Math.floor(Math.random() * 8) + 1
    })
    
    // 下一天
    currentDate.setDate(currentDate.getDate() + 1)
  }
  
  // 如果数据点太多（超过30个），进行智能采样
  let finalData = data
  if (data.length > 30) {
    const step = Math.ceil(data.length / 15) // 最多保留15个数据点
    finalData = data.filter((_, index) => index % step === 0)
    
    // 确保包含最后一天（今天）
    if (!finalData.some(item => item.date === data[data.length - 1].date)) {
      finalData.push(data[data.length - 1])
    }
    
    console.log(`数据点过多，已进行采样: 原始${data.length}个 -> 采样后${finalData.length}个`)
  }
  
  console.log('生成模拟数据:', finalData)
  return finalData
}

// 获取负责人统计数据
const fetchAssigneeData = async () => {
  try {
    const params = { 
      projectId: selectedProjectId.value,
      startDate: getStartDate(),
      endDate: getEndDate(),
      limit: 10
    }
    const res = await getAssigneeStatistics(params)

    
    if (res.data.code === 200) {
      const data = res.data.data
      // 确保数据是数组格式
      if (Array.isArray(data)) {
        chartData.assigneeData = data.map((item, index) => ({
          name: item.username || '未知用户',  // API规范中是username字段
          value: item.total || 0,            // API规范中是total字段
          color: getAssigneeColor(index)
        }))
      } else {
        console.warn('负责人统计数据不是数组格式:', data)
        chartData.assigneeData = []
      }
    } else {
      console.warn('获取负责人统计数据失败，响应码:', res.data.code)
      chartData.assigneeData = []
    }
  } catch (error) {
    console.error('获取负责人统计数据失败:', error)
    ElMessage.error('获取负责人统计数据失败')
    chartData.assigneeData = []
  }
}

// 获取项目分布数据
const fetchProjectData = async () => {
  try {
    const params = { 
      startDate: getStartDate(),
      endDate: getEndDate(),
      limit: 10
    }
    const res = await getProjectDistribution(params)
    console.log('项目分布数据响应:', res.data)
    
    if (res.data.code === 200) {
      const data = res.data.data
      // 确保数据是数组格式
      if (Array.isArray(data)) {
        chartData.projectData = data.map(item => ({
          name: item.projectName || '未知项目',
          total: item.total || 0,                                    // API规范中是total字段
          open: (item.newCount || 0) + (item.processingCount || 0),  // 新建+处理中=未解决
          resolved: item.resolvedCount || 0,
          closed: item.closedCount || 0,                             // 已关闭数量
          defectRate: item.defectRate || 0,                          // 缺陷率
          newCount: item.newCount || 0,                              // 新建数量
          processingCount: item.processingCount || 0                 // 处理中数量
        }))
        console.log('处理后的项目分布数据:', chartData.projectData)
      } else {
        console.warn('项目分布数据不是数组格式:', data)
        chartData.projectData = []
      }
    } else {
      console.warn('获取项目分布数据失败，响应码:', res.data.code)
      chartData.projectData = []
    }
  } catch (error) {
    console.error('获取项目分布数据失败:', error)
    ElMessage.error('获取项目分布数据失败')
    chartData.projectData = []
  }
}

// 获取状态对应的颜色
const getStatusColor = (statusCode) => {
  const colors = {
    'new': '#409EFF',
    'processing': '#E6A23C', 
    'resolved': '#67C23A',
    'closed': '#909399',
    'rejected': '#F56C6C'
  }
  return colors[statusCode] || '#909399'
}

// 获取优先级对应的颜色
const getPriorityColor = (priorityLevel) => {
  const colors = {
    0: '#F56C6C',    // 紧急 - 红色
    1: '#E6A23C',    // 高 - 橙色
    2: '#67C23A',    // 中 - 绿色
    3: '#909399'     // 低 - 灰色
  }
  return colors[priorityLevel] || '#909399'
}

// 获取负责人对应的颜色
const getAssigneeColor = (index) => {
  const colors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
  return colors[index % colors.length]
}

// 刷新所有数据
const refreshData = async () => {
  loading.value = true
  try {
    console.log('开始刷新数据，参数:', {
      timeRange: timeRange.value,
      projectId: selectedProjectId.value,
      projectName: getSelectedProjectName(),
      startDate: getStartDate(),
      endDate: getEndDate()
    })
    
    const fetchTasks = [
      fetchOverviewData(),
      fetchStatusDistribution(),
      fetchPriorityDistribution(),
      fetchTrendData(),
      fetchAssigneeData()
    ]
    
    // 只有在查看全部项目时才获取项目分布数据
    if (!selectedProjectId.value) {
      fetchTasks.push(fetchProjectData())
    }
    
    const results = await Promise.allSettled(fetchTasks)
    
    // 检查是否有失败的请求
    const failedRequests = results.filter(result => result.status === 'rejected')
    if (failedRequests.length > 0) {
      console.warn('部分数据获取失败:', failedRequests)
      ElMessage.warning(`数据刷新完成，但有${failedRequests.length}个请求失败`)
    } else {
      ElMessage.success(`已刷新${getSelectedProjectName()}${getTimeRangeText()}的数据`)
    }
  } catch (error) {
    console.error('刷新数据失败:', error)
    ElMessage.error('刷新数据失败')
  } finally {
    loading.value = false
  }
}

// 获取时间范围文本
const getTimeRangeText = () => {
  const texts = {
    'week': '本周',
    'month': '本月',
    'quarter': '本季度',
    'year': '本年',
    'all': '全部'
  }
  return texts[timeRange.value] || '本月'
}

// 获取当前选择的项目名称
const getSelectedProjectName = () => {
  if (!selectedProjectId.value) return '全部项目'
  const project = projectList.value.find(p => p.id === selectedProjectId.value)
  return project ? project.name : '未知项目'
}

// 根据时间范围获取开始日期
const getStartDate = () => {
  const now = new Date()
  switch (timeRange.value) {
    case 'week':
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      return weekAgo.toISOString().split('T')[0]
    case 'month':
      const monthAgo = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
      return monthAgo.toISOString().split('T')[0]
    case 'quarter':
      const quarterAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate())
      return quarterAgo.toISOString().split('T')[0]
    case 'year':
      const yearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
      return yearAgo.toISOString().split('T')[0]
    default:
      return '2024-01-01'
  }
}

// 获取结束日期
const getEndDate = () => {
  return new Date().toISOString().split('T')[0]
}

// 过滤日期标签，避免重叠显示
const getFilteredDates = (data) => {
  if (!data || !data.length) return []
  
  // 根据数据量和时间范围动态调整显示的标签数量
  let maxLabels
  if (data.length <= 7) {
    maxLabels = data.length // 7天内全部显示
  } else if (data.length <= 15) {
    maxLabels = 7 // 15天内显示7个标签
  } else if (data.length <= 30) {
    maxLabels = 6 // 30天内显示6个标签
  } else {
    maxLabels = 5 // 超过30天显示5个标签
  }
  
  if (data.length <= maxLabels) {
    return data
  }
  
  const step = Math.floor(data.length / (maxLabels - 1))
  const filtered = []
  
  // 始终包含第一个
  filtered.push(data[0])
  
  // 添加中间的点
  for (let i = step; i < data.length - step; i += step) {
    filtered.push(data[i])
  }
  
  // 始终包含最后一个（确保包含今天的数据）
  const lastItem = data[data.length - 1]
  if (data.length > 1 && !filtered.some(item => item.date === lastItem.date)) {
    filtered.push(lastItem)
  }
  
  return filtered
}

// 格式化日期显示
const formatDate = (dateStr) => {
  if (!dateStr) return ''
  
  try {
    // 处理多种日期格式
    let date
    if (dateStr.includes('T')) {
      // ISO格式：2025-01-15T00:00:00
      date = new Date(dateStr)
    } else if (dateStr.includes('-') && dateStr.length === 10) {
      // 标准格式：2025-01-15
      date = new Date(dateStr + 'T00:00:00')
    } else {
      date = new Date(dateStr)
    }
    
    if (isNaN(date.getTime())) {
      // 如果日期无效，尝试提取数字部分
      const match = dateStr.match(/(\d{1,2})-(\d{1,2})/)
      if (match) {
        return `${match[1].padStart(2, '0')}-${match[2].padStart(2, '0')}`
      }
      return dateStr.slice(-5)
    }
    
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    const day = date.getDate().toString().padStart(2, '0')
    return `${month}-${day}`
  } catch (error) {
    console.warn('日期格式化失败:', dateStr, error)
    return dateStr.slice(-5) // 如果解析失败，返回最后5个字符
  }
}

// 计算日期标签在X轴上的位置（与SVG数据点位置一致）
const getDateLabelPosition = (targetItem, allData) => {
  if (!allData || !allData.length) return 0
  
  // 找到目标项在完整数据中的索引
  const targetIndex = allData.findIndex(item => 
    item && targetItem && item.date === targetItem.date
  )
  
  if (targetIndex === -1) return 0
  
  // 使用与SVG数据点相同的计算逻辑
  const width = 300 // SVG宽度
  const xStep = allData.length > 1 ? width / (allData.length - 1) : 0
  
  let x
  if (allData.length === 1) {
    x = width / 2 // 如果只有一个数据点，放在中间
  } else {
    x = targetIndex * xStep
  }
  
  // 转换为百分比（相对于chart-content的宽度）
  return (x / width) * 100
}

// 计算日期标签的transform属性，处理边缘标签的对齐
const getDateLabelTransform = (targetItem, allData, labelIndex, totalLabels) => {
  const position = getDateLabelPosition(targetItem, allData)
  
  // 第一个标签：左对齐，避免超出左边界
  if (labelIndex === 0) {
    return 'translateX(0)'
  }
  
  // 最后一个标签：右对齐，避免超出右边界
  if (labelIndex === totalLabels - 1) {
    return 'translateX(-100%)'
  }
  
  // 中间的标签：居中对齐
  return 'translateX(-50%)'
}

// 导出报告
const exportReport = () => {
  ElMessage.info('正在导出报告，请稍候...')
  // 这里可以调用导出API
  setTimeout(() => {
    ElMessage.success('报告导出成功')
  }, 1500)
}

// 监听时间范围变化
watch(timeRange, () => {
  refreshData()
})

// 监听项目选择变化
watch(selectedProjectId, () => {
  refreshData()
})

onMounted(async () => {
  await fetchProjectList()
  refreshData()
})

// 组件卸载时清理数据
onUnmounted(() => {
  // 重置所有响应式数据
  chartData.statusData = []
  chartData.priorityData = []
  chartData.trendData = []
  chartData.assigneeData = []
  chartData.projectData = []
  
  stats.total = 0
  stats.open = 0
  stats.resolved = 0
  stats.urgent = 0
  
  hoveredIndex.value = -1
  hoveredProjectIndex.value = -1
  loading.value = false
})
</script>

<style scoped>
.defect-dashboard-container {
  padding: 20px;
  height: auto;
  overflow-y: auto;
  max-height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 统计卡片 */
.stat-cards {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 20px;
  height: 100%;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-icon {
  font-size: 48px;
  margin-right: 20px;
}

.total-card .stat-icon {
  color: #409EFF;
}

.open-card .stat-icon {
  color: #E6A23C;
}

.resolved-card .stat-icon {
  color: #67C23A;
}

.urgent-card .stat-icon {
  color: #F56C6C;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 28px;
  font-weight: bold;
  line-height: 1.2;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

/* 图表卡片 */
.chart-row {
  margin-bottom: 20px;
}

.chart-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 饼图样式 */
.pie-chart {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-chart-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.pie-svg-container {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pie-svg {
  filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
}

.pie-segment {
  cursor: pointer;
  transition: all 0.3s ease;
  stroke: white;
  stroke-width: 2;
}

.pie-segment:hover {
  filter: brightness(1.1);
}

.pie-segment-hover {
  filter: brightness(1.1) drop-shadow(0 4px 12px rgba(0, 0, 0, 0.2));
}

.pie-label {
  font-size: 11px;
  font-weight: 600;
  fill: white;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  pointer-events: none;
}

.pie-center-text {
  font-size: 12px;
  fill: #909399;
  font-weight: 500;
}

.pie-center-value {
  font-size: 16px;
  fill: #303133;
  font-weight: 600;
}

/* 空数据状态 */
.empty-chart {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

/* 其他图表样式 */
.bar-chart, .line-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.chart-legend {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: 12px;
  padding-left: 20px;
}

.legend-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: #fafafa;
  border: 1px solid transparent;
}

.legend-item:hover,
.legend-item-hover {
  background-color: #f0f9ff;
  border-color: #e1f5fe;
  transform: translateX(3px);
}

.legend-color {
  width: 14px;
  height: 14px;
  border-radius: 3px;
  margin-right: 10px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  flex-shrink: 0;
}

.legend-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.legend-name {
  font-size: 13px;
  font-weight: 500;
  color: #303133;
  line-height: 1.2;
}

.legend-stats {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-value {
  font-size: 14px;
  font-weight: 600;
  color: #409eff;
}

.legend-percentage {
  font-size: 12px;
  color: #909399;
}

.mock-bar-chart {
  width: 80%;
  height: 80%;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding: 0 20px;
}

.bar-item {
  width: 40px;
  border-radius: 4px 4px 0 0;
  position: relative;
  transition: height 0.5s;
}

.bar-label {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  white-space: nowrap;
}

.bar-value {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  font-weight: bold;
}

.mock-line-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px;
  position: relative;
}

.chart-main {
  flex: 1;
  display: flex;
  min-height: 0;
}

.chart-axis-y {
  width: 40px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.chart-content {
  flex: 1;
  height: 100%;
  position: relative;
}

.chart-axis-x {
  height: 30px;
  position: relative;
  margin-top: 10px;
  margin-left: 40px; /* 对应Y轴宽度 */
  margin-right: 15px; /* 为右侧标签留出更多空间 */
  padding: 0 15px; /* 增加左右内边距，为边缘标签留出更多空间 */
  border-top: 1px solid #e4e7ed;
  /* 确保宽度与chart-content一致 */
  width: calc(100% - 55px); /* 调整宽度计算，考虑右边距 */
  box-sizing: border-box;
  overflow: visible; /* 允许标签超出容器边界 */
}

.chart-axis-x .axis-label {
  font-size: 11px;
  color: #909399;
  text-align: center;
  white-space: nowrap;
  overflow: visible;
  line-height: 30px;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 3px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  max-width: none; /* 允许标签根据内容扩展 */
}

.chart-axis-y .axis-label {
  font-size: 12px;
  color: #909399;
  text-align: right;
  padding-right: 5px;
}

.line-svg {
  width: 100%;
  height: 100%;
}

.trend-point {
  cursor: pointer;
  transition: r 0.2s ease;
}

.trend-point:hover {
  r: 5;
  filter: drop-shadow(0 2px 4px rgba(64, 158, 255, 0.3));
}

/* 负责人分配表格样式 */
.assignee-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress-wrapper {
  padding: 0 10px;
}

/* 项目分布卡片样式 */
.project-distribution {
  height: auto !important;
  min-height: 300px;
}

.project-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  padding: 20px;
}

.project-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.project-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #06b6d4, #10b981);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.project-card-hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.project-card-hover::before {
  opacity: 1;
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.project-info {
  flex: 1;
}

.project-name {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.project-stats {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 50px;
}

.stat-label {
  font-size: 11px;
  color: #64748b;
  margin-bottom: 2px;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
}

.stat-item.total .stat-value {
  color: #3b82f6;
}

.stat-item.open .stat-value {
  color: #f59e0b;
}

.stat-item.resolved .stat-value {
  color: #10b981;
}

.project-rate {
  margin-left: 16px;
}

.rate-circle {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: conic-gradient(
    #10b981 0deg calc(var(--rate) * 3.6deg),
    #e2e8f0 calc(var(--rate) * 3.6deg) 360deg
  );
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.rate-circle::before {
  content: '';
  position: absolute;
  width: 44px;
  height: 44px;
  background: white;
  border-radius: 50%;
}

.rate-text {
  position: relative;
  z-index: 1;
  font-size: 12px;
  font-weight: 600;
  color: #1e293b;
}

.project-progress {
  margin-bottom: 16px;
}

.progress-bar {
  margin-bottom: 8px;
}

.progress-track {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.6s ease;
}

.progress-fill.resolved {
  background: linear-gradient(90deg, #10b981, #059669);
}

.progress-labels {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-label {
  font-size: 12px;
  color: #64748b;
}

.progress-value {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

.project-metrics {
  display: flex;
  gap: 16px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.metric-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.metric-icon.new {
  background: #fef2f2;
  color: #dc2626;
}

.metric-icon.processing {
  background: #fff7ed;
  color: #ea580c;
}

.metric-info {
  display: flex;
  flex-direction: column;
}

.metric-label {
  font-size: 11px;
  color: #64748b;
  line-height: 1.2;
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.2;
}

.project-additional-info {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  gap: 16px;
}

.info-row {
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: #64748b;
}

.info-value {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.info-value.rate {
  color: #059669;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .stat-cards > div {
    margin-bottom: 15px;
  }
  
  .chart-row > div {
    margin-bottom: 15px;
  }
  
  .pie-chart-wrapper {
    flex-direction: column;
    gap: 15px;
  }
  
  .chart-legend {
    padding-left: 0;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .legend-item {
    flex-direction: column;
    text-align: center;
    min-width: 80px;
    padding: 6px 8px;
  }
  
  .legend-content {
    align-items: center;
  }
  
  .legend-stats {
    flex-direction: column;
    gap: 2px;
  }
  
  .project-cards-container {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }
  
  .project-card {
    padding: 16px;
  }
  
  .project-header {
    flex-direction: column;
    gap: 12px;
  }
  
  .project-rate {
    margin-left: 0;
    align-self: center;
  }
  
  .rate-circle {
    width: 50px;
    height: 50px;
  }
  
  .rate-circle::before {
    width: 36px;
    height: 36px;
  }
  
  .project-metrics {
    flex-direction: column;
    gap: 8px;
  }
  
  .project-additional-info {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .header-actions > * {
    width: 100%;
  }
}
</style> 