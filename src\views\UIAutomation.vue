<template>
  <Home>
    <div class="ui-automation-container">
      <!-- 页面标题 -->
      <div class="page-header">
        <h2>界面自动化测试</h2>
        <p>通过录制用户操作生成Playwright自动化测试脚本</p>
      </div>

      <!-- 控制面板 -->
      <el-card class="control-panel" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>录制控制</span>
          </div>
        </template>
        
        <div class="control-content">
          <el-row :gutter="20" align="middle">
            <el-col :span="8">
              <el-input 
                v-model="targetUrl" 
                placeholder="请输入要测试的网站URL"
                :prefix-icon="Link"
                clearable
              />
            </el-col>
            <el-col :span="8">
              <el-button-group>
                <el-button 
                  @click="startRecording" 
                  :loading="isRecording" 
                  type="primary"
                  :icon="VideoPlay"
                >
                  {{ isRecording ? '录制中...' : '开始录制' }}
                </el-button>
                <el-button 
                  @click="stopRecording" 
                  :disabled="!isRecording"
                  type="danger"
                  :icon="VideoPause"
                >
                  停止录制
                </el-button>
              </el-button-group>
            </el-col>
            <el-col :span="8">
              <el-tag v-if="isRecording" type="success" effect="dark">
                <el-icon><Timer /></el-icon>
                已录制 {{ recordedActions.length }} 个操作
              </el-tag>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 浏览器容器 -->
      <el-card v-if="isRecording" class="browser-container" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>测试浏览器</span>
            <el-tag type="info" size="small">{{ targetUrl }}</el-tag>
          </div>
        </template>
        
        <div class="iframe-wrapper">
          <iframe 
            ref="browserFrame"
            :src="proxyUrl" 
            width="100%" 
            height="600px"
            frameborder="0"
            @load="setupRecording"
          ></iframe>
        </div>
      </el-card>

      <!-- 录制的操作列表 -->
      <el-card v-if="recordedActions.length > 0" class="actions-panel" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>录制的操作 ({{ recordedActions.length }})</span>
            <el-button @click="clearActions" size="small" type="warning" plain>
              清空操作
            </el-button>
          </div>
        </template>
        
        <el-table :data="recordedActions" stripe style="width: 100%">
          <el-table-column prop="index" label="序号" width="80" />
          <el-table-column prop="type" label="操作类型" width="100">
            <template #default="{ row }">
              <el-tag :type="getActionTypeColor(row.type)" size="small">
                {{ getActionTypeName(row.type) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="selector" label="元素选择器" min-width="200" />
          <el-table-column prop="value" label="操作值" width="150" />
          <el-table-column prop="timestamp" label="时间" width="120">
            <template #default="{ row }">
              {{ formatTime(row.timestamp) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ row, $index }">
              <el-button 
                @click="removeAction($index)" 
                size="small" 
                type="danger" 
                plain
                :icon="Delete"
              />
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 生成的脚本 -->
      <el-card v-if="generatedScript" class="script-panel" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>生成的Playwright脚本</span>
            <el-button-group>
              <el-button @click="copyScript" size="small" type="primary" :icon="CopyDocument">
                复制脚本
              </el-button>
              <el-button @click="saveAsTestCase" size="small" type="success" :icon="Check">
                保存为测试用例
              </el-button>
            </el-button-group>
          </div>
        </template>
        
        <el-input 
          type="textarea" 
          :rows="15" 
          v-model="generatedScript" 
          readonly
          class="script-textarea"
        />
      </el-card>
    </div>
  </Home>
</template>

<script setup>
import { ref, computed, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  VideoPlay, 
  VideoPause, 
  Timer, 
  Link, 
  Delete, 
  CopyDocument, 
  Check 
} from '@element-plus/icons-vue'
import Home from '@/components/HomePage.vue'
import axios from 'axios'

const router = useRouter()
const route = useRoute()

// 基础数据
const projectId = ref(route.query.projectId)
const projectName = ref(route.query.projectName || '未知项目')
const targetUrl = ref('https://example.com')
const isRecording = ref(false)
const recordedActions = ref([])
const browserFrame = ref(null)

// 计算属性
const proxyUrl = computed(() => {
  return `http://localhost:8081/api/proxy?url=${encodeURIComponent(targetUrl.value)}`
})

const generatedScript = computed(() => {
  return generatePlaywrightScript(recordedActions.value)
})

// 开始录制
const startRecording = async () => {
  if (!targetUrl.value) {
    ElMessage.warning('请输入目标网站URL')
    return
  }

  try {
    isRecording.value = true
    recordedActions.value = []
    
    // 通知后端启动代理服务
    await axios.post('http://localhost:8081/api/playwright/start-proxy', {
      targetUrl: targetUrl.value
    }, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    ElMessage.success('录制已启动，请在下方浏览器中进行操作')
  } catch (error) {
    ElMessage.error('启动录制失败，请检查网络连接')
    isRecording.value = false
  }
}

// 设置录制
const setupRecording = () => {
  const iframe = browserFrame.value
  if (!iframe) return
  
  try {
    const iframeDoc = iframe.contentDocument || iframe.contentWindow.document
    
    // 注入录制脚本
    const script = iframeDoc.createElement('script')
    script.textContent = `
      (function() {
        let actionIndex = 0;
        
        // 录制点击事件
        document.addEventListener('click', function(e) {
          const selector = generateSelector(e.target);
          const action = {
            type: 'click',
            selector: selector,
            timestamp: Date.now(),
            index: actionIndex++
          };
          
          window.parent.postMessage({
            type: 'playwright-action',
            action: action
          }, '*');
        }, true);
        
        // 录制输入事件
        document.addEventListener('input', function(e) {
          const selector = generateSelector(e.target);
          const action = {
            type: 'fill',
            selector: selector,
            value: e.target.value,
            timestamp: Date.now(),
            index: actionIndex++
          };
          
          window.parent.postMessage({
            type: 'playwright-action',
            action: action
          }, '*');
        });
        
        // 录制导航事件
        let lastUrl = window.location.href;
        setInterval(function() {
          if (window.location.href !== lastUrl) {
            const action = {
              type: 'navigate',
              url: window.location.href,
              timestamp: Date.now(),
              index: actionIndex++
            };
            
            window.parent.postMessage({
              type: 'playwright-action',
              action: action
            }, '*');
            
            lastUrl = window.location.href;
          }
        }, 1000);
        
        // 生成选择器
        function generateSelector(element) {
          if (element.id) return '#' + element.id;
          if (element.getAttribute('data-testid')) {
            return '[data-testid="' + element.getAttribute('data-testid') + '"]';
          }
          if (element.className && typeof element.className === 'string') {
            const classes = element.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) {
              return '.' + classes[0];
            }
          }
          
          // 生成路径选择器
          let path = [];
          let current = element;
          while (current && current.nodeType === Node.ELEMENT_NODE) {
            let selector = current.nodeName.toLowerCase();
            if (current.id) {
              selector += '#' + current.id;
              path.unshift(selector);
              break;
            }
            path.unshift(selector);
            current = current.parentNode;
          }
          return path.join(' > ');
        }
      })();
    `
    
    iframeDoc.head.appendChild(script)
  } catch (error) {
    console.warn('无法注入录制脚本，可能存在跨域限制')
  }
}

// 监听来自iframe的消息
const handleMessage = (event) => {
  if (event.data.type === 'playwright-action') {
    const action = event.data.action
    action.index = recordedActions.value.length + 1
    recordedActions.value.push(action)
  }
}

// 停止录制
const stopRecording = () => {
  isRecording.value = false
  ElMessage.success(`录制完成，共记录 ${recordedActions.value.length} 个操作`)
}

// 清空操作
const clearActions = () => {
  ElMessageBox.confirm('确定要清空所有录制的操作吗？', '确认清空', {
    type: 'warning'
  }).then(() => {
    recordedActions.value = []
    ElMessage.success('操作已清空')
  })
}

// 移除单个操作
const removeAction = (index) => {
  recordedActions.value.splice(index, 1)
  // 重新编号
  recordedActions.value.forEach((action, idx) => {
    action.index = idx + 1
  })
}

// 获取操作类型颜色
const getActionTypeColor = (type) => {
  const colorMap = {
    'click': 'primary',
    'fill': 'success',
    'navigate': 'warning'
  }
  return colorMap[type] || 'info'
}

// 获取操作类型名称
const getActionTypeName = (type) => {
  const nameMap = {
    'click': '点击',
    'fill': '输入',
    'navigate': '导航'
  }
  return nameMap[type] || type
}

// 格式化时间
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 生成Playwright脚本
const generatePlaywrightScript = (actions) => {
  if (actions.length === 0) return ''
  
  let script = `const { test, expect } = require('@playwright/test');

test('录制的界面自动化测试 - ${projectName.value}', async ({ page }) => {
  // 导航到目标页面
  await page.goto('${targetUrl.value}');
  
`

  actions.forEach(action => {
    switch (action.type) {
      case 'click':
        script += `  // 点击元素: ${action.selector}\n`
        script += `  await page.click('${action.selector}');\n\n`
        break
      case 'fill':
        script += `  // 输入文本: ${action.value}\n`
        script += `  await page.fill('${action.selector}', '${action.value}');\n\n`
        break
      case 'navigate':
        script += `  // 导航到新页面\n`
        script += `  await page.goto('${action.url}');\n\n`
        break
    }
  })

  script += `  // 添加断言验证测试结果
  // await expect(page).toHaveTitle(/期望的标题/);
  // await expect(page.locator('选择器')).toBeVisible();
});`

  return script
}

// 复制脚本
const copyScript = async () => {
  try {
    await navigator.clipboard.writeText(generatedScript.value)
    ElMessage.success('脚本已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 保存为测试用例
const saveAsTestCase = async () => {
  try {
    const response = await axios.post('http://localhost:8081/api/testcase/', {
      title: `界面自动化测试-${new Date().toLocaleString()}`,
      description: '通过界面录制生成的Playwright自动化测试',
      test_type: 'ui_automation',
      playwright_script: generatedScript.value,
      project_id: projectId.value,
      status: 1,
      priority: '中',
      method: 'UI',
      api_path: targetUrl.value
    }, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    })
    
    if (response.data.code === 200) {
      ElMessage.success('测试用例保存成功')
      // 清空录制数据
      recordedActions.value = []
    } else {
      ElMessage.error(response.data.message || '保存失败')
    }
  } catch (error) {
    ElMessage.error('保存测试用例失败，请检查网络连接')
  }
}

// 生命周期
window.addEventListener('message', handleMessage)

onUnmounted(() => {
  window.removeEventListener('message', handleMessage)
})
</script>

<style scoped>
.ui-automation-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.control-panel,
.browser-container,
.actions-panel,
.script-panel {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.control-content {
  padding: 10px 0;
}

.iframe-wrapper {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
}

.script-textarea {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.script-textarea :deep(.el-textarea__inner) {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
}
</style>