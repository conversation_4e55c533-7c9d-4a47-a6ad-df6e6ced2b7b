import request from '@/utils/request';

/**
 * 获取项目列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getProjectList(params) {
  return request({
    url: '/api/projects',
    method: 'get',
    params
  });
}

/**
 * 获取缺陷列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getDefectList(params) {
  return request({
    url: '/api/defects',
    method: 'get',
    params
  });
}

/**
 * 获取缺陷详情
 * @param {string|number} id - 缺陷ID
 * @returns {Promise}
 */
export function getDefectDetail(id) {
  return request({
    url: `/api/defects/${id}`,
    method: 'get'
  });
}

/**
 * 创建缺陷
 * @param {Object} data - 缺陷数据
 * @returns {Promise}
 */
export function createDefect(data) {
  return request({
    url: '/api/defects/create',
    method: 'post',
    data
  });
}

/**
 * 更新缺陷
 * @param {string|number} id - 缺陷ID
 * @param {Object} data - 缺陷数据
 * @returns {Promise}
 */
export function updateDefect(id, data) {
  return request({
    url: `/api/defects/${id}`,
    method: 'put',
    data
  });
}

/**
 * 获取筛选条件选项
 * @returns {Promise}
 */
export function getDefectOptions() {
  return request({
    url: '/api/defects/options',
    method: 'get'
  });
}

/**
 * 添加评论
 * @param {string|number} defectId - 缺陷ID
 * @param {string} content - 评论内容
 * @returns {Promise}
 */
export function addDefectComment(defectId, content) {
  return request({
    url: `/api/defects/${defectId}/comments`,
    method: 'post',
    data: { content }
  });
}

/**
 * 更改缺陷状态
 * @param {string|number} defectId - 缺陷ID
 * @param {string} status - 状态代码
 * @param {string} resolution - 解决方案
 * @returns {Promise}
 */
export function changeDefectStatus(defectId, status, resolution = '') {
  return request({
    url: `/api/defects/${defectId}/status`,
    method: 'put',
    data: { status, resolution }
  });
}

/**
 * 指派缺陷
 * @param {string|number} defectId - 缺陷ID
 * @param {string|number} assigneeId - 被指派人ID
 * @returns {Promise}
 */
export function assignDefect(defectId, assigneeId) {
  return request({
    url: `/api/defects/${defectId}/assign`,
    method: 'put',
    data: { assigneeId }
  });
}

/**
 * 关联测试用例
 * @param {string|number} defectId - 缺陷ID
 * @param {Array} testcaseIds - 测试用例ID数组
 * @returns {Promise}
 */
export function relateTestcases(defectId, testcaseIds) {
  return request({
    url: `/api/defects/${defectId}/testcases`,
    method: 'post',
    data: { testcaseIds }
  });
}

/**
 * 上传附件
 * @param {string|number} defectId - 缺陷ID
 * @param {File} file - 文件对象
 * @returns {Promise}
 */
export function uploadAttachment(defectId, file) {
  const formData = new FormData();
  formData.append('file', file);
  
  return request({
    url: `/api/defects/${defectId}/attachments`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 上传粘贴的图片
 * @param {string|number} defectId - 缺陷ID，如果为空则仅上传返回URL
 * @param {Blob} imageBlob - 图片Blob数据
 * @returns {Promise}
 */
export function uploadPastedImage(defectId, imageBlob) {
  const formData = new FormData();
  const timestamp = new Date().getTime();
  // 创建一个文件对象，给定文件名以便服务器处理
  const file = new File([imageBlob], `pasted_image_${timestamp}.png`, { type: 'image/png' });
  formData.append('file', file);
  
  // 如果有defectId，则关联到对应缺陷
  if (defectId) {
    formData.append('defectId', defectId);
  }
  
  return request({
    url: `/api/defects/upload-image`,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 删除附件
 * @param {string|number} defectId - 缺陷ID
 * @param {string|number} attachmentId - 附件ID
 * @returns {Promise}
 */
export function deleteAttachment(defectId, attachmentId) {
  return request({
    url: `/api/defects/${defectId}/attachments/${attachmentId}`,
    method: 'delete'
  });
}

/**
 * 删除缺陷
 * @param {string|number} id - 缺陷ID
 * @returns {Promise}
 */
export function deleteDefect(id) {
  return request({
    url: `/api/defects/${id}`,
    method: 'delete'
  });
}

/**
 * 批量删除缺陷
 * @param {Array} ids - 缺陷ID数组
 * @returns {Promise}
 */
export function batchDeleteDefects(ids) {
  return request({
    url: '/api/defects/batch-delete',
    method: 'post',
    data: { ids }
  });
}

/**
 * 导出缺陷
 * @param {Array} ids - 缺陷ID数组
 * @returns {Promise}
 */
export function exportDefects(ids) {
  return request({
    url: '/api/defects/export',
    method: 'post',
    data: { ids },
    responseType: 'blob'
  });
} 