<template>
  <div class="test-case-suggestions">
    <div class="suggestions-header">
      <div class="header-left">
        <h3>🤖 AI智能建议</h3>
        <el-tag v-if="suggestions.length > 0" type="info" size="small">
          共 {{ suggestions.length }} 条建议
        </el-tag>
      </div>
      <div class="header-right">
        <el-select 
          v-model="statusFilter" 
          placeholder="筛选状态" 
          size="small" 
          @change="onFilterChange"
          style="width: 120px; margin-right: 10px"
        >
          <el-option label="全部" value="" />
          <el-option label="待处理" value="pending" />
          <el-option label="已应用" value="applied" />
          <el-option label="已拒绝" value="rejected" />
        </el-select>
        <el-button 
          @click="$emit('analyze')" 
          type="primary" 
          size="small"
          :loading="analyzing"
          :disabled="!testCaseId"
        >
          {{ analyzing ? '分析中...' : '重新分析' }}
        </el-button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="3" animated />
      <p style="text-align: center; color: #909399; margin-top: 16px;">
        正在加载建议列表...
      </p>
    </div>

    <!-- 空状态 -->
    <el-empty v-else-if="suggestions.length === 0" :description="emptyDescription">
      <template #image>
        <div class="empty-icon">🤖</div>
      </template>
      <el-button v-if="!hasAnalyzed" @click="$emit('analyze')" type="primary">
        开始智能分析
      </el-button>
    </el-empty>

    <!-- 建议列表 -->
    <div v-else class="suggestions-list">
      <!-- 建议项列表 -->
      <div class="suggestion-items">
        <div
          v-for="suggestion in filteredSuggestions"
          :key="suggestion.suggestion_id"
          class="suggestion-item"
        >
          <div class="suggestion-content">
            <div class="suggestion-type">
              <el-tag :type="getTypeColor(suggestion.type)" size="small">
                {{ suggestion.type_display }}
              </el-tag>
            </div>
            <div class="suggestion-details">
              <h4>{{ suggestion.field_name }}</h4>
              <p class="reason">{{ suggestion.reason }}</p>
              <div class="values">
                <div class="value-item">
                  <span class="label">当前值：</span>
                  <code>{{ suggestion.current_value }}</code>
                </div>
                <div class="value-item">
                  <span class="label">建议值：</span>
                  <code>{{ suggestion.suggested_value }}</code>
                </div>
              </div>
            </div>
            <div class="suggestion-actions">
              <el-button 
                v-if="suggestion.status === 'pending'"
                @click="onApplySuggestion(suggestion)" 
                type="success" 
                size="small"
              >
                应用
              </el-button>
              <el-button 
                v-if="suggestion.status === 'pending'"
                @click="onRejectSuggestion(suggestion)" 
                type="danger" 
                size="small"
              >
                拒绝
              </el-button>
              <el-tag v-else :type="getStatusType(suggestion.status)" size="small">
                {{ getStatusText(suggestion.status) }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { applySuggestions, rejectSuggestions } from '@/api/testcaseSuggestions'

// Props
const props = defineProps({
  testCaseId: {
    type: [String, Number],
    required: true
  },
  suggestions: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  analyzing: {
    type: Boolean,
    default: false
  },
  hasAnalyzed: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'analyze',
  'refresh',
  'suggestion-applied',
  'suggestion-rejected'
])

// 状态
const statusFilter = ref('')

// 计算属性
const filteredSuggestions = computed(() => {
  if (!statusFilter.value) return props.suggestions
  return props.suggestions.filter(s => s.status === statusFilter.value)
})

const emptyDescription = computed(() => {
  if (!props.hasAnalyzed) {
    return '还未进行智能分析，点击"开始智能分析"按钮获取AI建议'
  }
  return '暂无AI建议，可能测试用例运行正常'
})

// 方法
const onFilterChange = () => {
  // 可以在这里添加过滤逻辑
}

const getTypeColor = (type) => {
  const typeColors = {
    'url': 'primary',
    'method': 'success',
    'headers': 'warning',
    'params': 'info',
    'body': 'danger',
    'assertion': 'primary'
  }
  return typeColors[type] || 'info'
}

const getStatusType = (status) => {
  const statusTypes = {
    'applied': 'success',
    'rejected': 'danger',
    'pending': 'warning'
  }
  return statusTypes[status] || 'info'
}

const getStatusText = (status) => {
  const statusTexts = {
    'applied': '已应用',
    'rejected': '已拒绝',
    'pending': '待处理'
  }
  return statusTexts[status] || status
}

const onApplySuggestion = async (suggestion) => {
  try {
    await ElMessageBox.confirm(
      `确认应用建议：${suggestion.reason}？`,
      '应用建议',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const response = await applySuggestions({
      suggestion_ids: [suggestion.suggestion_id],
      apply_reason: '用户手动应用'
    })

    if (response.data.success) {
      ElMessage.success('建议应用成功')
      emit('suggestion-applied', suggestion)
      emit('refresh')
    } else {
      ElMessage.error(response.data.error || '应用失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('应用建议失败:', error)
      ElMessage.error('应用建议失败：' + (error.message || '未知错误'))
    }
  }
}

const onRejectSuggestion = async (suggestion) => {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      `确认拒绝建议：${suggestion.reason}？`,
      '拒绝建议',
      {
        inputType: 'textarea',
        inputPlaceholder: '请说明拒绝此建议的原因...',
        confirmButtonText: '确认拒绝',
        cancelButtonText: '取消'
      }
    )

    const response = await rejectSuggestions({
      suggestion_ids: [suggestion.suggestion_id],
      reject_reason: reason
    })

    if (response.data.success) {
      ElMessage.success('建议已拒绝')
      emit('suggestion-rejected', suggestion)
      emit('refresh')
    } else {
      ElMessage.error(response.data.error || '拒绝失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('拒绝建议失败:', error)
      ElMessage.error('拒绝建议失败：' + (error.message || '未知错误'))
    }
  }
}
</script>

<style scoped>
.test-case-suggestions {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #ebeef5;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-left h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.suggestion-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 12px;
  background: #fafafa;
}

.suggestion-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.suggestion-details {
  flex: 1;
}

.suggestion-details h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.reason {
  color: #606266;
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.values {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.value-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  color: #909399;
  font-size: 12px;
  min-width: 60px;
}

code {
  background: #f5f7fa;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: #e6a23c;
}

.suggestion-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}
</style> 