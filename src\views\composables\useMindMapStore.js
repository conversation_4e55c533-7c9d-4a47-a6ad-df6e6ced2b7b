import { ref, reactive, computed, watch } from 'vue';
import { ElMessage } from 'element-plus';

// 全局状态存储
const state = reactive({
  graph: null,
  currentData: null,
  history: [],
  historyIndex: -1,
  clipboard: null,
  selectedNode: null,
  isExecuting: false,
  executionResults: new Map(),
  settings: {
    autoSave: true,
    autoLayout: true,
    showGrid: false,
    snapToGrid: false,
    animationDuration: 300
  }
});

// 状态常量
const MAX_HISTORY_SIZE = 50;
const NODE_TYPES = {
  ROOT: 'root',
  CASE: 'case',
  GROUP: 'group'
};

const NODE_STATUS = {
  NONE: 'none',
  SUCCESS: 'success',
  FAIL: 'fail',
  EXECUTING: 'executing'
};

export function useMindMapStore() {
  // 设置图实例
  const setGraph = (graph) => {
    state.graph = graph;
    if (graph) {
      initializeGraph();
    }
  };

  // 初始化图
  const initializeGraph = () => {
    const initialData = createInitialData();
    state.currentData = initialData;
    state.graph.data(initialData);
    state.graph.render();
    
    // 设置根节点位置
    setTimeout(() => {
      const rootNode = state.graph.findById('root');
      if (rootNode) {
        const canvas = state.graph.getCanvas();
        const containerHeight = canvas.cfg.height || 600;
        
        state.graph.updateItem(rootNode, {
          x: 200, // 距离左边200px
          y: containerHeight / 2 // 垂直居中
        });
      }
    }, 100);
    
    saveToHistory();
    
    // 监听图变化
    setupGraphListeners();
  };

  // 创建初始数据
  const createInitialData = () => {
    return {
      id: 'root',
      label: '接口自动化测试',
      x: 200, // 根节点初始X位置
      y: 300, // 根节点初始Y位置（默认值，会在render后调整）
      children: [],
      data: {
        type: NODE_TYPES.ROOT,
        text: '接口自动化测试',
        notes: '',
        status: NODE_STATUS.NONE,
        createTime: new Date().toISOString()
      },
      style: {
        fill: '#e6f7ff',
        stroke: '#1890ff'
      }
    };
  };

  // 设置图监听器
  const setupGraphListeners = () => {
    if (!state.graph) return;

    // 节点点击事件
    state.graph.on('node:click', (evt) => {
      const { item } = evt;
      if (item) {
        const nodeModel = item.getModel();
        selectNode(nodeModel);
      }
    });

    // 画布点击事件
    state.graph.on('canvas:click', () => {
      selectNode(null);
    });

    // 节点拖拽结束事件
    state.graph.on('node:dragend', () => {
      saveToHistory();
    });
  };

  // 选择节点
  const selectNode = (node) => {
    // 清除之前选中的节点状态
    if (state.selectedNode) {
      const prevNode = state.graph.findById(state.selectedNode.id);
      if (prevNode) {
        state.graph.setItemState(prevNode, 'selected', false);
      }
    }

    state.selectedNode = node;

    // 设置新选中节点状态
    if (node) {
      const newNode = state.graph.findById(node.id);
      if (newNode) {
        state.graph.setItemState(newNode, 'selected', true);
      }
    }
  };

  // 更新节点数据
  const updateNode = (nodeData) => {
    const data = state.graph.save();
    const node = findNode(data, nodeData.id);
    
    if (node) {
      // 更新节点数据
      Object.assign(node, nodeData);
      node.data.updateTime = new Date().toISOString();
      
      // 更新图数据
      state.graph.changeData(data);
      state.currentData = data;
      
      // 保存到历史记录
      saveToHistory();
      
      return true;
    }
    return false;
  };

  // 添加节点
  const addNode = (parentId, nodeData = {}) => {
    const data = state.graph.save();
    const parentNode = findNode(data, parentId);
    
    if (!parentNode) {
      throw new Error('Parent node not found');
    }

    const newNode = {
      id: `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      label: nodeData.label || '新建节点',
      children: [],
      data: {
        type: NODE_TYPES.CASE,
        testCase: nodeData.testCase || null,
        condition: nodeData.condition || 'none',
        priority: nodeData.priority || 'medium',
        notes: nodeData.notes || '',
        text: nodeData.label || '新建节点',
        status: NODE_STATUS.NONE,
        extractions: nodeData.extractions || [],
        params: nodeData.params || [],
        createTime: new Date().toISOString(),
        ...nodeData.data
      },
      style: nodeData.style || {}
    };

    // 确保父节点有children数组
    if (!parentNode.children) {
      parentNode.children = [];
    }

    parentNode.children.push(newNode);

    // 更新图数据
    state.graph.changeData(data);
    state.currentData = data;
    
    // 保存到历史记录
    saveToHistory();

    return newNode;
  };

  // 添加父节点
  const addParentNode = (childId, nodeData = {}) => {
    const data = state.graph.save();
    const childNode = findNode(data, childId);
    
    if (!childNode || childId === 'root') {
      throw new Error('Invalid child node');
    }

    // 找到子节点的父节点
    const parentNode = findParentNode(data, childId);
    if (!parentNode) {
      throw new Error('Parent node not found');
    }

    // 创建新的父节点
    const newParentNode = {
      id: `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      label: nodeData.label || '新建父节点',
      children: [childNode],
      data: {
        type: NODE_TYPES.CASE,
        testCase: nodeData.testCase || null,
        condition: nodeData.condition || 'none',
        priority: nodeData.priority || 'medium',
        notes: nodeData.notes || '',
        text: nodeData.label || '新建父节点',
        status: NODE_STATUS.NONE,
        extractions: nodeData.extractions || [],
        params: nodeData.params || [],
        createTime: new Date().toISOString(),
        ...nodeData.data
      },
      style: nodeData.style || {}
    };

    // 从原父节点中移除子节点
    const childIndex = parentNode.children.findIndex(child => child.id === childId);
    if (childIndex !== -1) {
      parentNode.children.splice(childIndex, 1, newParentNode);
    }

    // 更新图数据
    state.graph.changeData(data);
    state.currentData = data;
    
    // 保存到历史记录
    saveToHistory();

    return newParentNode;
  };

  // 删除节点
  const removeNode = (nodeId) => {
    if (nodeId === 'root') {
      throw new Error('Cannot remove root node');
    }

    const data = state.graph.save();
    removeNodeFromTree(data, nodeId);

    // 更新图数据
    state.graph.changeData(data);
    state.currentData = data;
    
    // 保存到历史记录
    saveToHistory();

    // 如果删除的是选中节点，清除选择
    if (state.selectedNode && state.selectedNode.id === nodeId) {
      selectNode(null);
    }

    return true;
  };

  // 复制节点
  const copyNode = (nodeId) => {
    const data = state.graph.save();
    const node = findNode(data, nodeId);
    
    if (node) {
      state.clipboard = JSON.parse(JSON.stringify(node));
      // 清除ID以便粘贴时生成新ID
      clearNodeIds(state.clipboard);
      return true;
    }
    return false;
  };

  // 粘贴节点
  const pasteNode = (parentId) => {
    if (!state.clipboard) {
      return null;
    }

    const data = state.graph.save();
    const parentNode = findNode(data, parentId);
    
    if (!parentNode) {
      throw new Error('Parent node not found');
    }

    // 创建新节点（深拷贝剪贴板内容）
    const newNode = JSON.parse(JSON.stringify(state.clipboard));
    assignNewIds(newNode);

    // 确保父节点有children数组
    if (!parentNode.children) {
      parentNode.children = [];
    }

    parentNode.children.push(newNode);

    // 更新图数据
    state.graph.changeData(data);
    state.currentData = data;
    
    // 保存到历史记录
    saveToHistory();

    return newNode;
  };

  // 从测试用例创建节点
  const createNodeFromTestCase = (testCase, parentId = 'root') => {
    const nodeData = {
      label: testCase.title,
      testCase: testCase.id,
      data: {
        method: testCase.method,
        api_path: testCase.api_path,
        priority: testCase.priority,
        status: testCase.status
      }
    };

    return addNode(parentId, nodeData);
  };

  // 从测试用例创建节点（支持复杂拖拽目标）
  const createNodeFromTestCaseWithTarget = (testCase, dropTarget) => {
    const nodeData = {
      label: testCase.title,
      testCase: testCase.id,
      data: {
        method: testCase.method,
        api_path: testCase.api_path,
        priority: testCase.priority,
        status: testCase.status
      }
    };

    switch (dropTarget.type) {
      case 'root':
        // 创建根级节点
        return addNode('root', nodeData);
        
      case 'child':
        // 创建子节点
        return addNode(dropTarget.parentId, nodeData);
        
      case 'sibling':
        // 创建兄弟节点
        return addSiblingNode(dropTarget.parentId, dropTarget.siblingId, dropTarget.position, nodeData);
        
      default:
        // 默认作为子节点
        return addNode(dropTarget.parentId || 'root', nodeData);
    }
  };

  // 添加兄弟节点
  const addSiblingNode = (parentId, siblingId, position, nodeData = {}) => {
    const data = state.graph.save();
    const parentNode = findNode(data, parentId);
    
    if (!parentNode) {
      throw new Error('Parent node not found');
    }

    const newNode = {
      id: `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      label: nodeData.label || '新建节点',
      children: [],
      data: {
        type: NODE_TYPES.CASE,
        testCase: nodeData.testCase || null,
        condition: nodeData.condition || 'none',
        priority: nodeData.priority || 'medium',
        notes: nodeData.notes || '',
        text: nodeData.label || '新建节点',
        status: NODE_STATUS.NONE,
        extractions: nodeData.extractions || [],
        params: nodeData.params || [],
        createTime: new Date().toISOString(),
        ...nodeData.data
      },
      style: nodeData.style || {}
    };

    // 确保父节点有children数组
    if (!parentNode.children) {
      parentNode.children = [];
    }

    // 找到兄弟节点的索引
    const siblingIndex = parentNode.children.findIndex(child => child.id === siblingId);
    
    if (siblingIndex === -1) {
      // 如果找不到兄弟节点，添加到末尾
      parentNode.children.push(newNode);
    } else {
      // 根据位置插入
      const insertIndex = position === 'before' ? siblingIndex : siblingIndex + 1;
      parentNode.children.splice(insertIndex, 0, newNode);
    }

    // 更新图数据
    state.graph.changeData(data);
    state.currentData = data;
    
    // 保存到历史记录
    saveToHistory();

    return newNode;
  };

  // 获取可用变量
  const getAvailableVariables = (nodeId) => {
    const variables = [];
    const data = state.currentData;

    if (!data) return variables;

    // 递归查找前置节点的提取变量
    const findPreviousVariables = (node, targetId, path = []) => {
      if (node.id === targetId) {
        return true;
      }

      // 如果不是目标节点且有提取规则，添加到变量列表
      if (node.id !== targetId && node.data?.extractions) {
        node.data.extractions.forEach(extraction => {
          if (extraction.name) {
            variables.push({
              id: `${node.id}-${extraction.name}`,
              nodeId: node.id,
              nodeName: node.label,
              name: extraction.name,
              type: extraction.type,
              path: [...path, node.label]
            });
          }
        });
      }

      // 递归检查子节点
      if (node.children) {
        for (const child of node.children) {
          if (findPreviousVariables(child, targetId, [...path, node.label])) {
            return true;
          }
        }
      }

      return false;
    };

    findPreviousVariables(data, nodeId);
    return variables;
  };

  // 执行节点
  const executeNode = async (nodeId) => {
    const node = findNode(state.currentData, nodeId);
    if (!node || !node.data?.testCase) {
      throw new Error('Node or test case not found');
    }

    // 设置执行状态
    node.data.status = NODE_STATUS.EXECUTING;
    updateNodeStatus(nodeId, NODE_STATUS.EXECUTING);

    try {
      const result = await executeTestCase(node.data.testCase);
      
      // 更新节点状态
      const status = result.success ? NODE_STATUS.SUCCESS : NODE_STATUS.FAIL;
      node.data.status = status;
      node.data.lastExecutionTime = new Date().toISOString();
      node.data.lastExecutionResult = result;

      updateNodeStatus(nodeId, status);
      
      // 保存执行结果
      state.executionResults.set(nodeId, result);

      return result;
    } catch (error) {
      // 执行失败
      node.data.status = NODE_STATUS.FAIL;
      updateNodeStatus(nodeId, NODE_STATUS.FAIL);
      throw error;
    }
  };

  // 更新节点状态显示
  const updateNodeStatus = (nodeId, status) => {
    const nodeItem = state.graph.findById(nodeId);
    if (nodeItem) {
      const model = nodeItem.getModel();
      model.data.status = status;
      state.graph.refreshItem(nodeItem);
    }
  };

  // 撤销操作
  const undo = () => {
    if (state.historyIndex > 0) {
      state.historyIndex--;
      const data = state.history[state.historyIndex];
      state.currentData = JSON.parse(JSON.stringify(data));
      state.graph.changeData(state.currentData);
      return true;
    }
    return false;
  };

  // 重做操作
  const redo = () => {
    if (state.historyIndex < state.history.length - 1) {
      state.historyIndex++;
      const data = state.history[state.historyIndex];
      state.currentData = JSON.parse(JSON.stringify(data));
      state.graph.changeData(state.currentData);
      return true;
    }
    return false;
  };

  // 保存到历史记录
  const saveToHistory = () => {
    if (!state.currentData) return;

    // 移除后面的历史记录
    state.history = state.history.slice(0, state.historyIndex + 1);
    
    // 添加当前状态
    state.history.push(JSON.parse(JSON.stringify(state.currentData)));
    state.historyIndex = state.history.length - 1;

    // 限制历史记录数量
    if (state.history.length > MAX_HISTORY_SIZE) {
      state.history = state.history.slice(-MAX_HISTORY_SIZE);
      state.historyIndex = state.history.length - 1;
    }
  };

  // 清理资源
  const cleanup = () => {
    if (state.graph) {
      state.graph.destroy();
    }
    state.graph = null;
    state.currentData = null;
    state.selectedNode = null;
    state.history = [];
    state.historyIndex = -1;
    state.clipboard = null;
    state.executionResults.clear();
  };

  // 辅助函数：查找节点
  const findNode = (tree, id) => {
    if (tree.id === id) return tree;
    if (tree.children) {
      for (const child of tree.children) {
        const found = findNode(child, id);
        if (found) return found;
      }
    }
    return null;
  };

  // 辅助函数：查找父节点
  const findParentNode = (tree, childId) => {
    if (tree.children) {
      for (const child of tree.children) {
        if (child.id === childId) {
          return tree;
        }
        const found = findParentNode(child, childId);
        if (found) return found;
      }
    }
    return null;
  };

  // 辅助函数：从树中删除节点
  const removeNodeFromTree = (tree, nodeId) => {
    if (tree.children) {
      tree.children = tree.children.filter(child => {
        if (child.id === nodeId) {
          return false;
        }
        removeNodeFromTree(child, nodeId);
        return true;
      });
    }
  };

  // 辅助函数：清除节点ID
  const clearNodeIds = (node) => {
    delete node.id;
    if (node.children) {
      node.children.forEach(clearNodeIds);
    }
  };

  // 辅助函数：分配新ID
  const assignNewIds = (node) => {
    node.id = `node-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    if (node.children) {
      node.children.forEach(assignNewIds);
    }
  };

  // 执行测试用例
  const executeTestCase = async (testCaseId) => {
    if (!testCaseId) {
      throw new Error('测试用例ID不能为空');
    }

    try {
      // 获取当前环境ID
      const currentEnvId = localStorage.getItem('currentEnvId');
      if (!currentEnvId) {
        throw new Error('请先选择环境套');
      }

      // 获取项目ID
      const projectId = localStorage.getItem('currentProjectId');
      if (!projectId) {
        throw new Error('项目ID不存在');
      }

      // 调用后端API执行测试用例
      const response = await fetch(`http://localhost:8081/api/testcase/execute/${testCaseId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          project_id: projectId,
          env_id: currentEnvId
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP错误: ${response.status}`);
      }

      const data = await response.json();

      if (data.success === true) {
        return {
          success: true,
          message: data.message || '测试用例执行成功',
          data: {
            status: 'PASS',
            responseTime: data.data?.duration || 0,
            statusCode: data.data?.response?.status_code || 200,
            response: data.data?.response || {},
            assertions: {
              all_passed: true
            }
          }
        };
      } else {
        return {
          success: false,
          message: data.message || '测试用例执行失败',
          data: {
            status: 'FAIL',
            responseTime: data.data?.duration || 0,
            statusCode: data.data?.response?.status_code || 500,
            response: data.data?.response || {},
            assertions: {
              all_passed: false
            },
            error: data.error || '未知错误'
          }
        };
      }
    } catch (error) {
      return {
        success: false,
        message: error.message || '网络请求失败',
        data: {
          status: 'ERROR',
          responseTime: 0,
          statusCode: 0,
          response: {},
          assertions: {
            all_passed: false
          },
          error: error.message || '网络请求失败'
        }
      };
    }
  };

  // 计算属性
  const canUndo = computed(() => state.historyIndex > 0);
  const canRedo = computed(() => state.historyIndex < state.history.length - 1);
  const hasClipboard = computed(() => !!state.clipboard);

  return {
    // 状态
    state,
    
    // 基本操作
    setGraph,
    selectNode,
    updateNode,
    
    // 节点操作
    addNode,
    addParentNode,
    removeNode,
    copyNode,
    pasteNode,
    createNodeFromTestCase,
    createNodeFromTestCaseWithTarget,
    addSiblingNode,
    
    // 执行操作
    executeNode,
    updateNodeStatus,
    
    // 历史操作
    undo,
    redo,
    saveToHistory,
    
    // 工具函数
    getAvailableVariables,
    findNode,
    cleanup,
    
    // 计算属性
    canUndo,
    canRedo,
    hasClipboard,
    
    // 常量
    NODE_TYPES,
    NODE_STATUS
  };
} 