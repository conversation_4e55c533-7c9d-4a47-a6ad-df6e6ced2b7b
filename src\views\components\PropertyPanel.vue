<template>
  <div class="properties-panel">
    <div class="panel-header">
      <h3>节点属性</h3>
      <div class="header-actions">
        <el-button v-if="false" link size="small" @click="testComponent">测试</el-button>
        <el-button link @click="handleClose" :icon="Close" />
      </div>
    </div>
    
    <el-scrollbar class="panel-content">
      <el-form
        :model="formData"
        label-position="top"
        size="small"
        @submit.prevent
      >
        <!-- 基本信息 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span class="section-title">基本信息</span>
          </template>
          
          <el-form-item label="节点名称" required>
            <el-input 
              v-model="formData.label" 
              @input="handleLabelChange"
              placeholder="请输入节点名称"
              maxlength="50"
              show-word-limit
            />
          </el-form-item>

          <el-form-item v-if="!isRootNode" label="节点类型">
            <el-select v-model="formData.data.type" @change="handleTypeChange">
              <el-option label="测试用例" value="case" />
              <el-option label="分组节点" value="group" />
            </el-select>
          </el-form-item>

          <el-form-item label="备注">
            <el-input
              type="textarea"
              v-model="formData.data.notes"
              @input="handleNotesChange"
              :rows="3"
              placeholder="请输入备注信息"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-card>

        <!-- 测试用例关联（非根节点且类型为case时显示） -->
        <el-card v-if="!isRootNode && formData.data.type === 'case'" class="form-section" shadow="never">
          <template #header>
            <span class="section-title">测试用例</span>
          </template>
          
          <el-form-item label="关联用例">
            <el-select 
              v-model="formData.data.testCase"
              @change="handleTestCaseChange"
              filterable
              clearable
              placeholder="选择关联的测试用例"
            >
              <el-option
                v-for="item in testCases"
                :key="item.id"
                :label="item.title"
                :value="item.id"
              >
                <div class="case-option">
                  <span>{{ item.title }}</span>
                  <el-tag size="small" :type="getMethodType(item.method)">
                    {{ item.method }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item v-if="selectedTestCase" label="用例信息">
            <div class="test-case-info">
              <div class="info-item">
                <span class="label">API路径:</span>
                <code class="value">{{ selectedTestCase.api_path }}</code>
              </div>
              <div class="info-item">
                <span class="label">请求方法:</span>
                <el-tag size="small" :type="getMethodType(selectedTestCase.method)">
                  {{ selectedTestCase.method }}
                </el-tag>
              </div>
              <div v-if="selectedTestCase.description" class="info-item">
                <span class="label">描述:</span>
                <span class="value">{{ selectedTestCase.description }}</span>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="执行条件">
            <el-select v-model="formData.data.condition" @change="handleConditionChange">
              <el-option label="无条件执行" value="none" />
              <el-option label="父节点成功后执行" value="parent_success" />
              <el-option label="父节点失败后执行" value="parent_fail" />
              <el-option label="自定义条件" value="custom" />
            </el-select>
          </el-form-item>

          <el-form-item v-if="formData.data.condition === 'custom'" label="自定义条件">
            <el-input
              v-model="formData.data.customCondition"
              @input="handleCustomConditionChange"
              placeholder="请输入JavaScript表达式"
            />
          </el-form-item>

          <el-form-item label="优先级">
            <el-select v-model="formData.data.priority" @change="handlePriorityChange">
              <el-option label="高" value="high" />
              <el-option label="中" value="medium" />
              <el-option label="低" value="low" />
            </el-select>
          </el-form-item>
        </el-card>

        <!-- 前置参数 -->
        <el-card v-if="!isRootNode && formData.data.type === 'case'" class="form-section" shadow="never">
          <template #header>
            <div class="section-header">
              <span class="section-title">前置参数</span>
              <el-button type="primary" size="small" @click="addParam" :icon="Plus">
                添加参数
              </el-button>
            </div>
          </template>
          
          <div class="params-list">
            <div 
              v-for="(param, index) in formData.data.params || []" 
              :key="index"
              class="param-item"
            >
              <el-row :gutter="8">
                <el-col :span="8">
                  <el-input
                    v-model="param.name"
                    @input="handleParamChange"
                    placeholder="参数名"
                    size="small"
                  />
                </el-col>
                <el-col :span="8">
                  <el-select
                    v-model="param.source"
                    @change="handleParamChange"
                    placeholder="选择来源变量"
                    filterable
                    size="small"
                  >
                    <el-option label="无" value="" />
                    <el-option
                      v-for="variable in availableVariables"
                      :key="variable.id"
                      :label="`${variable.nodeName} - ${variable.name}`"
                      :value="variable.id"
                    />
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-input
                    v-model="param.defaultValue"
                    @input="handleParamChange"
                    placeholder="默认值"
                    size="small"
                  />
                </el-col>
                <el-col :span="2">
                  <el-button
                    type="danger"
                    size="small"
                    circle
                    @click="removeParam(index)"
                    :icon="Delete"
                  />
                </el-col>
              </el-row>
            </div>
            
            <div v-if="!formData.data.params?.length" class="empty-hint">
              暂无参数，点击上方按钮添加
            </div>
          </div>
        </el-card>

        <!-- 后置提取 -->
        <el-card v-if="!isRootNode && formData.data.type === 'case'" class="form-section" shadow="never">
          <template #header>
            <div class="section-header">
              <span class="section-title">后置提取</span>
              <el-button type="primary" size="small" @click="addExtraction" :icon="Plus">
                添加提取
              </el-button>
            </div>
          </template>
          
          <div class="extractions-list">
            <div 
              v-for="(extraction, index) in formData.data.extractions || []" 
              :key="index"
              class="extraction-item"
            >
              <el-row :gutter="8">
                <el-col :span="7">
                  <el-input
                    v-model="extraction.name"
                    @input="handleExtractionChange"
                    placeholder="变量名"
                    size="small"
                  />
                </el-col>
                <el-col :span="7">
                  <el-select
                    v-model="extraction.type"
                    @change="handleExtractionChange"
                    placeholder="提取方式"
                    size="small"
                  >
                    <el-option label="JSON路径" value="jsonpath" />
                    <el-option label="正则表达式" value="regex" />
                    <el-option label="XPath" value="xpath" />
                    <el-option label="响应头" value="header" />
                  </el-select>
                </el-col>
                <el-col :span="8">
                  <el-input
                    v-model="extraction.expression"
                    @input="handleExtractionChange"
                    :placeholder="getExtractionPlaceholder(extraction.type)"
                    size="small"
                  />
                </el-col>
                <el-col :span="2">
                  <el-button
                    type="danger"
                    size="small"
                    circle
                    @click="removeExtraction(index)"
                    :icon="Delete"
                  />
                </el-col>
              </el-row>
            </div>
            
            <div v-if="!formData.data.extractions?.length" class="empty-hint">
              暂无提取规则，点击上方按钮添加
            </div>
          </div>
        </el-card>

        <!-- 调试信息区域 -->
        <el-card v-if="false" class="form-section debug-section" shadow="never">
          <template #header>
            <span class="section-title">调试信息</span>
          </template>
          
          <div class="debug-info">
            <div class="debug-item">
              <strong>选中节点ID:</strong> {{ props.selectedNode?.id || 'None' }}
            </div>
            <div class="debug-item">
              <strong>节点类型:</strong> {{ formData.data?.type || 'None' }}
            </div>
            <div class="debug-item">
              <strong>测试用例数量:</strong> {{ testCases.length }}
            </div>
            <div class="debug-item">
              <strong>可用变量数量:</strong> {{ availableVariables.length }}
            </div>
            <div class="debug-item">
              <strong>关联测试用例:</strong> {{ formData.data?.testCase || 'None' }}
            </div>
            <div class="debug-item">
              <strong>表单数据:</strong>
              <pre>{{ JSON.stringify(formData, null, 2) }}</pre>
            </div>
          </div>
        </el-card>

        <!-- 执行信息 -->
        <el-card v-if="hasExecutionInfo" class="form-section" shadow="never">
          <template #header>
            <div class="section-header">
              <span class="section-title">执行信息</span>
              <el-button 
                v-if="formData.data.executionResult"
                type="primary" 
                size="small" 
                @click="showExecutionResult"
              >
                查看详情
              </el-button>
            </div>
          </template>
          
          <div class="execution-info">
            <div class="info-item">
              <span class="label">执行状态:</span>
              <el-tag :type="getStatusType(formData.data.status)">
                {{ getStatusText(formData.data.status) }}
              </el-tag>
            </div>
            
            <div v-if="formData.data.executionTime" class="info-item">
              <span class="label">执行时间:</span>
              <span class="value">{{ formatTime(formData.data.executionTime) }}</span>
            </div>
            
            <div v-if="formData.data.duration" class="info-item">
              <span class="label">耗时:</span>
              <span class="value">{{ formData.data.duration }}ms</span>
            </div>
            
            <div v-if="formData.data.errorMessage" class="info-item">
              <span class="label">错误信息:</span>
              <span class="value error-text">{{ formData.data.errorMessage }}</span>
            </div>
          </div>
        </el-card>
      </el-form>
    </el-scrollbar>

    <!-- 执行结果对话框 -->
    <ExecutionResultViewer
      v-if="executionResultVisible"
      :result="formData.data.executionResult"
      @close="handleCloseResultDialog"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, inject } from 'vue';
import { 
  Close,
  Plus,
  Delete
} from '@element-plus/icons-vue';
import ExecutionResultViewer from './ExecutionResultViewer.vue';

const props = defineProps({
  selectedNode: {
    type: Object,
    required: true
  },
  testCases: {
    type: Array,
    default: () => []
  },
  availableVariables: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['node-updated', 'close']);

// 响应式数据
const formData = ref({});
const executionResultVisible = ref(false);

// 注入依赖
const mindMapStore = inject('mindMapStore', null);

// 计算属性
const isRootNode = computed(() => formData.value.id === 'root');

const selectedTestCase = computed(() => {
  if (!formData.value.data?.testCase) return null;
  return props.testCases.find(tc => tc.id === formData.value.data.testCase);
});

const hasExecutionInfo = computed(() => {
  return formData.value.data?.status && formData.value.data.status !== 'none';
});

// 初始化表单数据
const initFormData = () => {
  // 深拷贝选中节点数据
  formData.value = JSON.parse(JSON.stringify(props.selectedNode));
  
  // 确保必要字段存在
  if (!formData.value.data) formData.value.data = {};
  if (!formData.value.data.params) formData.value.data.params = [];
  if (!formData.value.data.extractions) formData.value.data.extractions = [];
  if (!formData.value.data.type) formData.value.data.type = 'case';
  if (!formData.value.data.condition) formData.value.data.condition = 'none';
  if (!formData.value.data.priority) formData.value.data.priority = 'medium';
  if (!formData.value.data.status) formData.value.data.status = 'none';
  if (!formData.value.data.notes) formData.value.data.notes = '';
  if (!formData.value.data.text) formData.value.data.text = formData.value.label || '';
  
  console.log('PropertyPanel: initFormData completed', {
    formData: formData.value,
    selectedNode: props.selectedNode,
    testCases: props.testCases,
    availableVariables: props.availableVariables
  });
};

// 事件处理函数
const handleLabelChange = (value) => {
  console.log('PropertyPanel: handleLabelChange', value);
  formData.value.label = value;
  if (formData.value.data) {
    formData.value.data.text = value;
  }
  emitUpdate();
};

const handleNotesChange = (value) => {
  console.log('PropertyPanel: handleNotesChange', value);
  formData.value.data.notes = value;
  emitUpdate();
};

const handleTypeChange = (value) => {
  console.log('PropertyPanel: handleTypeChange', value);
  formData.value.data.type = value;
  // 如果类型改变，清除相关数据
  if (value !== 'case') {
    formData.value.data.testCase = null;
    formData.value.data.params = [];
    formData.value.data.extractions = [];
  }
  emitUpdate();
};

const handleTestCaseChange = (value) => {
  console.log('PropertyPanel: handleTestCaseChange', value);
  formData.value.data.testCase = value;
  emitUpdate();
};

const handleConditionChange = (value) => {
  console.log('PropertyPanel: handleConditionChange', value);
  formData.value.data.condition = value;
  emitUpdate();
};

const handleCustomConditionChange = (value) => {
  console.log('PropertyPanel: handleCustomConditionChange', value);
  formData.value.data.customCondition = value;
  emitUpdate();
};

const handlePriorityChange = (value) => {
  console.log('PropertyPanel: handlePriorityChange', value);
  formData.value.data.priority = value;
  emitUpdate();
};

const handleParamChange = () => {
  console.log('PropertyPanel: handleParamChange');
  emitUpdate();
};

const handleExtractionChange = () => {
  console.log('PropertyPanel: handleExtractionChange');
  emitUpdate();
};

// 参数操作
const addParam = () => {
  if (!formData.value.data.params) {
    formData.value.data.params = [];
  }
  
  formData.value.data.params.push({
    name: '',
    source: '',
    defaultValue: ''
  });
  
  emitUpdate();
};

const removeParam = (index) => {
  formData.value.data.params.splice(index, 1);
  emitUpdate();
};

// 提取规则操作
const addExtraction = () => {
  if (!formData.value.data.extractions) {
    formData.value.data.extractions = [];
  }
  
  formData.value.data.extractions.push({
    name: '',
    type: 'jsonpath',
    expression: ''
  });
  
  emitUpdate();
};

const removeExtraction = (index) => {
  formData.value.data.extractions.splice(index, 1);
  emitUpdate();
};

// 工具函数
const getMethodType = (method) => {
  const types = {
    'GET': 'success',
    'POST': 'primary', 
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'info'
  };
  return types[method] || 'info';
};

const getStatusType = (status) => {
  const types = {
    'success': 'success',
    'fail': 'danger',
    'executing': 'warning',
    'none': 'info'
  };
  return types[status] || 'info';
};

const getStatusText = (status) => {
  const texts = {
    'success': '执行成功',
    'fail': '执行失败',
    'executing': '执行中',
    'none': '未执行'
  };
  return texts[status] || '未知';
};

const getExtractionPlaceholder = (type) => {
  const placeholders = {
    'jsonpath': '$.data.id',
    'regex': '\\d+',
    'xpath': '//div[@class="result"]',
    'header': 'Authorization'
  };
  return placeholders[type] || '提取表达式';
};

const formatTime = (timeString) => {
  if (!timeString) return '';
  const date = new Date(timeString);
  return date.toLocaleString();
};

const testComponent = () => {
  console.log('PropertyPanel: Test component function called');
  console.log('Current formData:', formData.value);
  console.log('Props:', {
    selectedNode: props.selectedNode,
    testCases: props.testCases,
    availableVariables: props.availableVariables
  });
  
  // 测试表单数据更新
  if (formData.value.data) {
    formData.value.data.notes = '测试更新 - ' + new Date().toLocaleTimeString();
    emitUpdate();
  }
};

const emitUpdate = () => {
  console.log('PropertyPanel: emitUpdate called', formData.value);
  emit('node-updated', formData.value);
};

const showExecutionResult = () => {
  executionResultVisible.value = true;
};

const handleCloseResultDialog = () => {
  executionResultVisible.value = false;
};

const handleClose = () => {
  console.log('PropertyPanel: handleClose called');
  emit('close');
};

// 监听选中节点变化
watch(() => props.selectedNode, (newNode) => {
  console.log('PropertyPanel: selectedNode changed', newNode);
  if (newNode) {
    initFormData();
    console.log('PropertyPanel: formData initialized', formData.value);
  }
}, { immediate: true, deep: true });

// 监听测试用例数据变化
watch(() => props.testCases, (newTestCases) => {
  console.log('PropertyPanel: testCases changed', newTestCases);
}, { immediate: true });

// 监听可用变量变化
watch(() => props.availableVariables, (newVariables) => {
  console.log('PropertyPanel: availableVariables changed', newVariables);
}, { immediate: true });
</script>

<style scoped>
.properties-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 
    0 20px 40px rgba(0,0,0,0.1),
    0 8px 16px rgba(0,0,0,0.05),
    inset 0 1px 0 rgba(255,255,255,0.8);
  border: 1px solid rgba(255,255,255,0.3);
  position: relative;
  overflow: hidden;
}

.properties-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255,255,255,0.1) 0%, 
    rgba(255,255,255,0.05) 50%, 
    rgba(0,0,0,0.02) 100%);
  pointer-events: none;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(0,0,0,0.1);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  flex-shrink: 0;
  position: relative;
  z-index: 1;
  overflow: hidden;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.panel-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255,255,255,0.1) 0%, 
    rgba(255,255,255,0.05) 50%, 
    rgba(0,0,0,0.05) 100%);
  pointer-events: none;
}

.panel-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(255,255,255,0.3) 50%, 
    transparent 100%);
}

.panel-header h3 {
  margin: 0;
  font-size: 17px;
  font-weight: 600;
  color: #fff;
  position: relative;
  z-index: 1;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.panel-content {
  flex: 1;
  padding: 20px;
  position: relative;
  z-index: 1;
}

.panel-header .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  position: relative;
  z-index: 1;
  transition: all 0.3s ease;
}

.panel-header .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

.form-section {
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 
    0 4px 12px rgba(0,0,0,0.08),
    0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.form-section:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 8px 24px rgba(0,0,0,0.12),
    0 4px 8px rgba(0,0,0,0.08);
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.case-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.test-case-info {
  background: var(--el-fill-color-light);
  padding: 12px;
  border-radius: 4px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .label {
  font-weight: 500;
  color: var(--el-text-color-regular);
  min-width: 80px;
  margin-right: 8px;
}

.info-item .value {
  color: var(--el-text-color-primary);
  word-break: break-all;
}

.info-item code {
  background: var(--el-fill-color);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
}

.params-list,
.extractions-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.param-item,
.extraction-item {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 1px solid rgba(0,0,0,0.05);
  border-radius: 10px;
  padding: 16px;
  box-shadow: 
    0 2px 8px rgba(0,0,0,0.05),
    0 1px 2px rgba(0,0,0,0.03);
  transition: all 0.3s ease;
}

.param-item:hover,
.extraction-item:hover {
  transform: translateY(-1px);
  box-shadow: 
    0 4px 16px rgba(0,0,0,0.08),
    0 2px 4px rgba(0,0,0,0.05);
  border-color: rgba(0,0,0,0.1);
}

.empty-hint {
  text-align: center;
  color: #94a3b8;
  font-size: 13px;
  padding: 24px;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 10px;
  border: 2px dashed rgba(0,0,0,0.1);
  position: relative;
  overflow: hidden;
}

.empty-hint::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    transparent 40%, 
    rgba(255,255,255,0.1) 50%, 
    transparent 60%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.execution-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.debug-section {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.debug-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.debug-item {
  font-size: 12px;
  color: #666;
}

.debug-item strong {
  color: #333;
  margin-right: 8px;
}

.debug-item pre {
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
  font-size: 10px;
  max-height: 200px;
  overflow-y: auto;
  margin: 4px 0 0 0;
}

.error-text {
  color: #f56565;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .panel-content {
    padding: 12px;
  }
  
  .param-item .el-row,
  .extraction-item .el-row {
    flex-wrap: wrap;
  }
  
  .param-item .el-col,
  .extraction-item .el-col {
    margin-bottom: 8px;
  }
}

/* 表单样式优化 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

:deep(.el-card__header) {
  padding: 12px 16px;
  background: var(--el-fill-color-lighter);
}

:deep(.el-card__body) {
  padding: 16px;
}

/* 滚动条样式 */
:deep(.el-scrollbar__bar) {
  opacity: 0.3;
}

:deep(.el-scrollbar__bar:hover) {
  opacity: 0.8;
}
</style> 