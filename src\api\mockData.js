// 模拟数据 - 用于在后端接口未完全实现时测试前端功能

export const mockSuggestions = [
  {
    suggestion_id: 1,
    type: 'path',
    type_display: 'URL修改',
    field_name: 'api_path',
    current_value: '/api/session/create1231243',
    suggested_value: '/api/session/create',
    reason: '当前URL路径包含多余的数字后缀，建议使用标准的API路径格式。',
    confidence: 0.95,
    status: 'pending'
  },
  {
    suggestion_id: 2,
    type: 'headers',
    type_display: '请求头修改',
    field_name: 'Authorization',
    current_value: 'Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    suggested_value: 'Bearer [new_valid_token]',
    reason: '当前Bearer token可能已过期或无效，导致401未经授权错误。需要更新为有效的token。',
    confidence: 0.95,
    status: 'pending'
  },
  {
    suggestion_id: 3,
    type: 'method',
    type_display: 'HTTP方法修改',
    field_name: 'method',
    current_value: 'GET',
    suggested_value: 'POST',
    reason: '根据请求体内容判断，应该使用POST方法',
    confidence: 0.92,
    status: 'pending'
  },
  {
    suggestion_id: 4,
    type: 'body',
    type_display: '请求体修改',
    field_name: 'body',
    current_value: '[object Object]',
    suggested_value: '{\n  "username": "admin",\n  "password": "secure123",\n  "remember_me": true\n}',
    reason: '优化请求体结构，添加必需字段并修复JSON格式',
    confidence: 0.89,
    status: 'pending'
  }
]

export const mockAnalysisResponse = {
  data: {
    success: true,
    message: '分析完成',
    data: {
      test_case_id: 28,
      execution_result_id: 539,
      failure_analysis: {
        failure_category: "状态码错误",
        root_cause: "HTTP状态码401表示未经授权，可能是因为提供的Bearer token无效或已过期。",
        error_details: "测试用例中使用的Bearer token可能已经过期，或者没有足够的权限访问该API。401状态码通常意味着需要有效的认证凭证。",
        potential_fixes: [
          "检查并更新Bearer token，确保它是有效的且未过期。",
          "验证用户是否有权限访问该API端点。",
          "如果API需要特定的权限或角色，确保测试用户已被正确分配这些权限。",
          "检查API文档，确认是否需要其他认证方式或参数。"
        ],
        confidence: 0.95,
        analysis_timestamp: "2025-07-01T14:14:38.209585"
      },
      suggestions: mockSuggestions,
      total_suggestions: mockSuggestions.length,
      analysis_time: '2.3s',
      confidence_score: 0.84
    }
  }
}

export const mockApplyResponse = {
  data: {
    success: true,
    message: '建议已成功应用',
    data: {
      applied_count: 2,
      updated_test_case: {
        case_id: 123,
        api_path: '/api/v2/users',
        method: 'POST',
        headers: '{"Content-Type": "application/json"}',
        body: '{"username": "test", "password": "encrypted_password"}',
        updated_at: new Date().toISOString()
      }
    }
  }
}

export const mockRejectResponse = {
  data: {
    success: true,
    message: '建议已拒绝',
    data: {
      rejected_count: 1
    }
  }
}

export const mockEditHistory = {
  data: {
    success: true,
    data: {
      history: [
        {
          id: 1,
          field_name: 'api_path',
          old_value: '/api/user',
          new_value: '/api/v2/users',
          change_type: 'ai_suggestion',
          applied_at: '2024-01-15 10:30:00',
          suggestion_id: 1,
          confidence: 0.85
        },
        {
          id: 2,
          field_name: 'method',
          old_value: 'GET',
          new_value: 'POST',
          change_type: 'manual_edit',
          applied_at: '2024-01-15 10:25:00',
          user_id: 123
        }
      ],
      total: 2,
      ai_suggestions_count: 1,
      manual_edits_count: 1
    }
  }
}

// 模拟延迟函数
export const mockDelay = (ms = 1000) => new Promise(resolve => setTimeout(resolve, ms))

// 是否使用模拟数据
// 可以通过以下方式控制：
// 1. 在浏览器控制台执行：localStorage.setItem('useMockData', 'true')
// 2. 直接修改下面的常量
export const useMockData = 
  process.env.NODE_ENV === 'development' && 
  (localStorage.getItem('useMockData') === 'true' || false) // 改为true启用模拟数据 