import request from '@/utils/request'

/**
 * 调用DeepSeek FIM补全API
 * @param {string} prefix - 前缀内容（光标前的文本）
 * @param {string} suffix - 后缀内容（光标后的文本）
 * @param {number} maxTokens - 最大补全token数
 * @returns {Promise} API响应
 */
export async function getFimCompletion({ prefix, suffix, maxTokens = 128 }) {
  try {
    const response = await request.post('/api/fim/completion', {
      prefix,
      suffix,
      maxTokens,
      model: "deepseek-chat"
    })
    return response
  } catch (error) {
    console.error('FIM补全请求失败:', error)
    throw error
  }
}

/**
 * 获取系统配置的DeepSeek API密钥
 * @returns {Promise} 配置信息
 */
export async function getDeepSeekConfig() {
  try {
    const response = await request.get('/api/system/deepseek-config')
    return response
  } catch (error) {
    console.error('获取DeepSeek配置失败:', error)
    throw error
  }
} 