import { onMounted, onUnmounted } from 'vue';

export function useKeyboardShortcuts(options = {}) {
  const {
    onSave,
    onUndo,
    onRedo,
    onCopy,
    onPaste,
    onCut,
    onDelete,
    onHelp,
    onEscape,
    onZoomIn,
    onZoomOut,
    onResetZoom,
    onFitView,
    onExecuteSelected,
    onExecuteAll,
    onSearch,
    onSelectAll,
    onAddNode,
    onAddParentNode,
    onDuplicateNode,
    enabled = true
  } = options;

  // 快捷键映射
  const shortcutMap = {
    // 文件操作
    'ctrl+s': onSave,
    'ctrl+e': null, // 导出，由于浏览器限制，不直接绑定
    'ctrl+o': null, // 打开，由于浏览器限制，不直接绑定
    
    // 编辑操作
    'ctrl+z': onUndo,
    'ctrl+y': onRedo,
    'ctrl+shift+z': onRedo, // 备选重做快捷键
    'ctrl+c': onCopy,
    'ctrl+v': onPaste,
    'ctrl+x': onCut,
    'delete': onDelete,
    'backspace': onDelete,
    'ctrl+d': onDuplicateNode,
    
    // 节点操作
    'tab': onAddNode,
    'shift+tab': onAddParentNode,
    'enter': onAddNode,
    'shift+enter': onAddParentNode,
    'f2': null, // 编辑节点名称，需要在节点级别处理
    'space': null, // 展开/收起节点，需要在节点级别处理
    
    // 视图操作
    'ctrl+plus': onZoomIn,
    'ctrl+equal': onZoomIn, // = 键，通常不需要按 Shift
    'ctrl+minus': onZoomOut,
    'ctrl+0': onResetZoom,
    'ctrl+f': onFitView,
    'f11': null, // 全屏，浏览器默认行为
    
    // 执行操作
    'ctrl+r': onExecuteSelected,
    'ctrl+shift+r': onExecuteAll,
    'ctrl+pause': null, // 停止执行
    'ctrl+break': null, // 停止执行
    
    // 其他操作
    'ctrl+f': onSearch,
    'f1': onHelp,
    'escape': onEscape,
    'ctrl+a': onSelectAll,
    
    // 导航操作（需要在特定上下文中处理）
    'arrowup': null,
    'arrowdown': null,
    'arrowleft': null,
    'arrowright': null,
    'home': null,
    'end': null
  };

  // 处理键盘事件
  const handleKeyDown = (event) => {
    if (!enabled) return;

    // 检查是否在输入框中
    const activeElement = document.activeElement;
    const isInputElement = activeElement && (
      activeElement.tagName === 'INPUT' ||
      activeElement.tagName === 'TEXTAREA' ||
      activeElement.isContentEditable
    );

    // 构建快捷键字符串
    const shortcutKey = buildShortcutKey(event);
    
    // 获取对应的处理函数
    const handler = shortcutMap[shortcutKey];
    
    if (handler) {
      // 特殊情况：在输入框中，只处理特定的快捷键
      if (isInputElement) {
        const allowedInInput = [
          'ctrl+a', 'ctrl+c', 'ctrl+v', 'ctrl+x', 'ctrl+z', 'ctrl+y',
          'ctrl+s', 'escape', 'f1'
        ];
        
        if (!allowedInInput.includes(shortcutKey)) {
          return;
        }
      }
      
      // 阻止默认行为
      event.preventDefault();
      event.stopPropagation();
      
      // 执行处理函数
      try {
        handler(event);
      } catch (error) {
        console.error(`执行快捷键处理函数失败 (${shortcutKey}):`, error);
      }
    } else {
      // 处理一些特殊情况
      handleSpecialKeys(event);
    }
  };

  // 构建快捷键字符串
  const buildShortcutKey = (event) => {
    const parts = [];
    
    if (event.ctrlKey) parts.push('ctrl');
    if (event.shiftKey) parts.push('shift');
    if (event.altKey) parts.push('alt');
    if (event.metaKey) parts.push('meta');
    
    // 处理特殊键
    let key = event.key.toLowerCase();
    
    // 映射特殊键名
    const keyMap = {
      ' ': 'space',
      'arrowup': 'arrowup',
      'arrowdown': 'arrowdown',
      'arrowleft': 'arrowleft',
      'arrowright': 'arrowright',
      'escape': 'escape',
      'enter': 'enter',
      'tab': 'tab',
      'backspace': 'backspace',
      'delete': 'delete',
      'home': 'home',
      'end': 'end',
      'f1': 'f1',
      'f2': 'f2',
      'f11': 'f11',
      '+': 'plus',
      '=': 'equal',
      '-': 'minus',
      'pause': 'pause',
      'break': 'break'
    };
    
    if (keyMap[key]) {
      key = keyMap[key];
    }
    
    parts.push(key);
    
    return parts.join('+');
  };

  // 处理特殊键
  const handleSpecialKeys = (event) => {
    // 防止某些默认行为
    if (event.ctrlKey && event.key === 'r') {
      // 防止页面刷新
      event.preventDefault();
    }
    
    if (event.key === 'F5') {
      // 可以选择防止F5刷新
      // event.preventDefault();
    }
    
    if (event.ctrlKey && event.key === 'f') {
      // 防止浏览器搜索，如果我们有自己的搜索功能
      if (onSearch) {
        event.preventDefault();
      }
    }
  };

  // 处理键盘抬起事件
  const handleKeyUp = (event) => {
    // 如果需要处理键盘抬起事件，可以在这里添加
  };

  // 添加事件监听器
  const addEventListener = () => {
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('keyup', handleKeyUp);
  };

  // 移除事件监听器
  const removeEventListener = () => {
    document.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('keyup', handleKeyUp);
  };

  // 禁用/启用快捷键
  const setEnabled = (value) => {
    enabled = value;
  };

  // 注册新的快捷键
  const registerShortcut = (shortcutKey, handler) => {
    shortcutMap[shortcutKey] = handler;
  };

  // 取消注册快捷键
  const unregisterShortcut = (shortcutKey) => {
    delete shortcutMap[shortcutKey];
  };

  // 获取所有快捷键
  const getAllShortcuts = () => {
    return Object.keys(shortcutMap).filter(key => shortcutMap[key]);
  };

  // 检查快捷键是否存在
  const hasShortcut = (shortcutKey) => {
    return shortcutKey in shortcutMap && shortcutMap[shortcutKey];
  };

  // 格式化快捷键显示
  const formatShortcut = (shortcutKey) => {
    return shortcutKey
      .split('+')
      .map(part => {
        const formatMap = {
          'ctrl': 'Ctrl',
          'shift': 'Shift',
          'alt': 'Alt',
          'meta': 'Meta',
          'space': 'Space',
          'arrowup': '↑',
          'arrowdown': '↓',
          'arrowleft': '←',
          'arrowright': '→',
          'escape': 'Esc',
          'enter': 'Enter',
          'tab': 'Tab',
          'backspace': 'Backspace',
          'delete': 'Delete',
          'home': 'Home',
          'end': 'End',
          'plus': '+',
          'equal': '=',
          'minus': '-'
        };
        
        return formatMap[part] || part.toUpperCase();
      })
      .join(' + ');
  };

  // 生命周期钩子
  onMounted(() => {
    addEventListener();
  });

  onUnmounted(() => {
    removeEventListener();
  });

  return {
    // 控制方法
    setEnabled,
    registerShortcut,
    unregisterShortcut,
    
    // 查询方法
    getAllShortcuts,
    hasShortcut,
    formatShortcut,
    
    // 手动事件处理
    handleKeyDown,
    handleKeyUp
  };
} 