<template>
  <div class="ui-test-result">
    <el-card class="result-card" shadow="hover">
      <template #header>
        <div class="result-header">
          <div class="result-info">
            <h3>{{ testResult.test_name || '测试执行结果' }}</h3>
            <div class="result-meta">
              <el-tag :type="getStatusType(testResult.status)" size="small">
                {{ getStatusText(testResult.status) }}
              </el-tag>
              <span class="execution-time">执行时间: {{ formatDate(testResult.execution_time) }}</span>
            </div>
          </div>
          <div class="result-actions">
            <el-button-group size="small">
              <el-button @click="showDetails = !showDetails" :icon="View">
                {{ showDetails ? '隐藏' : '查看' }}详情
              </el-button>
              <el-button @click="downloadReport" :icon="Download">下载报告</el-button>
              <el-button @click="rerun" :icon="Refresh" :loading="rerunning">重新执行</el-button>
            </el-button-group>
          </div>
        </div>
      </template>

      <!-- 基本信息 -->
      <div class="result-summary">
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-label">执行耗时</div>
              <div class="summary-value">{{ formatDuration(testResult.duration) }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-label">浏览器</div>
              <div class="summary-value">{{ testResult.browser_info?.name || '未知' }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-label">分辨率</div>
              <div class="summary-value">{{ testResult.browser_info?.viewport || '1920x1080' }}</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="summary-item">
              <div class="summary-label">操作步骤</div>
              <div class="summary-value">{{ testResult.steps_count || 0 }} 步</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 详细信息 -->
      <div v-if="showDetails" class="result-details">
        <el-divider>执行详情</el-divider>
        
        <!-- 错误信息 -->
        <div v-if="testResult.status === 'FAIL' && testResult.error_message" class="error-section">
          <h4>
            <el-icon><Warning /></el-icon>
            错误信息
          </h4>
          <div class="error-content">
            <pre>{{ testResult.error_message }}</pre>
          </div>
        </div>

        <!-- 执行日志 -->
        <div v-if="testResult.execution_log" class="log-section">
          <h4>
            <el-icon><Document /></el-icon>
            执行日志
          </h4>
          <div class="log-content">
            <pre>{{ testResult.execution_log }}</pre>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { ElMessage } from 'element-plus';
import {
  View,
  Download,
  Refresh,
  Warning,
  Document
} from '@element-plus/icons-vue';

// Props
const props = defineProps({
  testResult: {
    type: Object,
    required: true,
    default: () => ({})
  }
});

// Emits
const emit = defineEmits(['rerun']);

// 响应式数据
const showDetails = ref(false);
const rerunning = ref(false);

// 方法
const getStatusType = (status) => {
  const statusMap = {
    'PASS': 'success',
    'FAIL': 'danger',
    'SKIP': 'warning',
    'RUNNING': 'primary'
  };
  return statusMap[status] || 'info';
};

const getStatusText = (status) => {
  const statusMap = {
    'PASS': '通过',
    'FAIL': '失败',
    'SKIP': '跳过',
    'RUNNING': '运行中'
  };
  return statusMap[status] || '未知';
};

const formatDate = (dateString) => {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleString('zh-CN');
};

const formatDuration = (duration) => {
  if (!duration) return '0ms';
  if (duration < 1000) return `${duration}ms`;
  return `${(duration / 1000).toFixed(2)}s`;
};

const downloadReport = () => {
  const reportData = {
    test_name: props.testResult.test_name,
    status: props.testResult.status,
    execution_time: props.testResult.execution_time,
    duration: props.testResult.duration,
    browser_info: props.testResult.browser_info,
    error_message: props.testResult.error_message
  };

  const dataStr = JSON.stringify(reportData, null, 2);
  const dataBlob = new Blob([dataStr], { type: 'application/json' });
  const url = URL.createObjectURL(dataBlob);
  
  const link = document.createElement('a');
  link.href = url;
  link.download = `ui-test-report-${Date.now()}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
  
  ElMessage.success('报告下载成功');
};

const rerun = async () => {
  rerunning.value = true;
  try {
    emit('rerun', props.testResult);
    ElMessage.success('重新执行已开始');
  } catch (error) {
    ElMessage.error('重新执行失败');
  } finally {
    rerunning.value = false;
  }
};
</script>

<style scoped>
.ui-test-result {
  margin-bottom: 20px;
}

.result-card {
  border-radius: 12px;
  overflow: hidden;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.result-info h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #303133;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #6c757d;
}

.result-summary {
  margin-bottom: 20px;
}

.summary-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.summary-label {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 4px;
}

.summary-value {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.result-details {
  margin-top: 20px;
}

.error-section h4,
.log-section h4 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #495057;
}

.error-content {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 20px;
}

.error-content pre {
  margin: 0;
  font-size: 12px;
  color: #e53e3e;
  white-space: pre-wrap;
  word-break: break-word;
}

.log-content {
  background: #2d3748;
  color: #e2e8f0;
  border-radius: 6px;
  padding: 16px;
  max-height: 300px;
  overflow-y: auto;
}

.log-content pre {
  margin: 0;
  font-size: 12px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .result-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .result-meta {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style> 