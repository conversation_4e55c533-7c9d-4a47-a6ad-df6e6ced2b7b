<template>
  <div class="mind-map-canvas" ref="mindMapContainer" @drop="handleDrop" @dragover="handleDragOver" @dragleave="handleDragLeave" @dragend="handleDragEnd">
    <!-- 脑图容器 -->
    <div id="mindmap-container" ref="graphContainer"></div>
    
    <!-- 小地图 -->
    <div v-if="showMinimap" class="minimap-container">
      <div ref="minimapContainer"></div>
    </div>
    
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <el-loading text="加载中..." />
    </div>
    
    <!-- 空状态 -->
    <div v-if="!hasData && !loading" class="empty-state">
      <el-empty 
        :image-size="200"
        description="暂无脑图数据"
      >
        <el-button type="primary" @click="initializeDefaultData">
          创建默认脑图
        </el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick, inject } from 'vue';
import G6 from '@antv/g6';

const props = defineProps({
  projectId: {
    type: String,
    required: true
  },
  width: {
    type: Number,
    default: 800
  },
  height: {
    type: Number,
    default: 600
  },
  showMinimap: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits([
  'node-selected',
  'node-updated', 
  'graph-ready',
  'data-changed',
  'case-dragged'
]);

// 响应式数据
const mindMapContainer = ref(null);
const graphContainer = ref(null);
const minimapContainer = ref(null);
const graph = ref(null);
const minimap = ref(null);
const loading = ref(false);
const hasData = ref(false);

// 注入依赖
const mindMapStore = inject('mindMapStore', null);

// 初始化G6图形
const initGraph = () => {
  if (!graphContainer.value) return;

  try {
    // 注册自定义节点
    registerCustomNodes();
    
    // 创建图实例
    graph.value = new G6.TreeGraph({
      container: graphContainer.value,
      width: mindMapContainer.value.offsetWidth,
      height: mindMapContainer.value.offsetHeight,
      modes: {
        default: [
          'drag-canvas',
          'zoom-canvas',
          'drag-node',
          'click-select'
        ]
      },
      defaultNode: {
        type: 'mindmap-node',
        size: [180, 50],
        anchorPoints: [
          [0, 0.5], // 左侧连接点
          [1, 0.5]  // 右侧连接点
        ]
      },
      defaultEdge: {
        type: 'cubic-horizontal',
        style: {
          stroke: '#91d5ff',
          lineWidth: 2,
          endArrow: {
            path: G6.Arrow.triangle(8, 10, 5),
            fill: '#91d5ff'
          }
        }
      },
      layout: {
        type: 'indented',
        direction: 'LR',
        dropCap: false,
        indent: 200,
        getHeight: () => 60,
        getWidth: () => 180,
        begin: [200, mindMapContainer.value.offsetHeight / 2]
      },
      animate: true,
      fitView: false,
      zoom: 1.0,
      fitViewPadding: [20, 40, 20, 40]
    });

    // 设置事件监听
    setupEventListeners();
    
    // 初始化小地图
    if (props.showMinimap) {
      initMinimap();
    }
    
    // 设置根节点位置的辅助函数
    const ensureRootNodePosition = () => {
      setTimeout(() => {
        const rootNode = graph.value.findById('root');
        if (rootNode && mindMapContainer.value) {
          const containerWidth = mindMapContainer.value.offsetWidth;
          const containerHeight = mindMapContainer.value.offsetHeight;
          const rootX = Math.max(200, containerWidth * 0.1); // 至少200px或容器宽度的10%
          const rootY = containerHeight / 2; // 垂直居中
          
          // 使用 updateItem 更新节点位置
          graph.value.updateItem(rootNode, {
            x: rootX,
            y: rootY
          });
          
          // 确保画布视图正确显示根节点
          graph.value.focusItem(rootNode);
        }
      }, 200);
    };
    
    // 监听数据变化，确保根节点位置正确
    graph.value.on('afterrender', ensureRootNodePosition);
    
    // 通知父组件图实例已准备就绪
    emit('graph-ready', graph.value);
    
    hasData.value = true;
    
  } catch (error) {
    console.error('初始化脑图失败:', error);
  }
};

// 注册自定义节点
const registerCustomNodes = () => {
  G6.registerNode('mindmap-node', {
    draw: (cfg, group) => {
      const { data = {} } = cfg;
      const status = data.status || 'none';
      const isRoot = cfg.id === 'root';
      
      // 根据状态设置颜色
      let fillColor = isRoot ? '#e6f7ff' : '#fff';
      let strokeColor = isRoot ? '#1890ff' : '#91d5ff';
      let lineWidth = isRoot ? 2 : 1;
      
      if (status === 'success') {
        fillColor = '#f6ffed';
        strokeColor = '#52c41a';
      } else if (status === 'fail') {
        fillColor = '#fff2f0';
        strokeColor = '#ff4d4f';
      } else if (status === 'executing') {
        fillColor = '#fff7e6';
        strokeColor = '#fa8c16';
      }
      
      // 主容器
      const keyShape = group.addShape('rect', {
        attrs: {
          x: -90,
          y: -25,
          width: 180,
          height: 50,
          radius: 6,
          fill: fillColor,
          stroke: strokeColor,
          lineWidth: lineWidth,
          cursor: 'pointer',
          shadowColor: 'rgba(0, 0, 0, 0.1)',
          shadowBlur: 4,
          shadowOffsetY: 2
        },
        name: 'main-rect'
      });

      // 文本标签
      group.addShape('text', {
        attrs: {
          text: cfg.label || '',
          x: 0,
          y: 0,
          fontSize: isRoot ? 16 : 14,
          fontWeight: isRoot ? 'bold' : 'normal',
          fill: isRoot ? '#1890ff' : '#333',
          cursor: 'pointer',
          textAlign: 'center',
          textBaseline: 'middle'
        },
        name: 'text-shape'
      });
      
      // 状态指示器
      if (status !== 'none') {
        const iconX = 70;
        const iconY = 0;
        const iconR = 8;
        
        // 状态背景圆
        group.addShape('circle', {
          attrs: {
            x: iconX,
            y: iconY,
            r: iconR,
            fill: strokeColor,
            stroke: '#fff',
            lineWidth: 2
          },
          name: 'status-bg'
        });
        
        // 状态图标
        let iconPath = '';
        if (status === 'success') {
          iconPath = 'M-3,-1 L-1,2 L3,-3';
        } else if (status === 'fail') {
          iconPath = 'M-3,-3 L3,3 M3,-3 L-3,3';
        } else if (status === 'executing') {
          iconPath = 'M-2,-3 L2,0 L-2,3 Z';
        }
        
        if (iconPath) {
          group.addShape('path', {
            attrs: {
              path: iconPath,
              stroke: '#fff',
              lineWidth: 2,
              lineCap: 'round',
              lineJoin: 'round'
            },
            name: 'status-icon'
          });
        }
      }
      
      // 测试用例标识
      if (data.testCase) {
        group.addShape('rect', {
          attrs: {
            x: -88,
            y: -23,
            width: 20,
            height: 16,
            radius: 2,
            fill: '#1890ff',
            opacity: 0.8
          },
          name: 'case-indicator'
        });
        
        group.addShape('text', {
          attrs: {
            text: 'TC',
            x: -78,
            y: -15,
            fontSize: 10,
            fontWeight: 'bold',
            fill: '#fff',
            textAlign: 'center',
            textBaseline: 'middle'
          },
          name: 'case-text'
        });
      }

      return keyShape;
    },
    
    setState: (name, value, item) => {
      const group = item.getContainer();
      const keyShape = item.getKeyShape();
      
      if (name === 'selected') {
        if (value) {
          keyShape.attr('stroke', '#1890ff');
          keyShape.attr('lineWidth', 3);
          keyShape.attr('shadowColor', 'rgba(24, 144, 255, 0.5)');
          keyShape.attr('shadowBlur', 10);
        } else {
          const model = item.getModel();
          const status = model.data?.status || 'none';
          const isRoot = model.id === 'root';
          
          let strokeColor = isRoot ? '#1890ff' : '#91d5ff';
          if (status === 'success') strokeColor = '#52c41a';
          else if (status === 'fail') strokeColor = '#ff4d4f';
          else if (status === 'executing') strokeColor = '#fa8c16';
          
          keyShape.attr('stroke', strokeColor);
          keyShape.attr('lineWidth', isRoot ? 2 : 1);
          keyShape.attr('shadowColor', 'rgba(0, 0, 0, 0.1)');
          keyShape.attr('shadowBlur', 4);
        }
      }
    }
  });
};

// 设置事件监听
const setupEventListeners = () => {
  if (!graph.value) return;

      // 节点点击事件
    graph.value.on('node:click', (evt) => {
      const { item } = evt;
      if (item) {
        const nodeModel = item.getModel();
        emit('node-selected', nodeModel);
      }
    });

    // 画布点击事件
    graph.value.on('canvas:click', () => {
      emit('node-selected', null);
    });

    // 节点双击事件
    graph.value.on('node:dblclick', (evt) => {
      const { item } = evt;
      if (item) {
        const nodeModel = item.getModel();
        // 可以在这里添加双击编辑逻辑
      }
    });

  // 拖拽结束事件
  graph.value.on('node:dragend', () => {
    emit('data-changed');
  });

  // 缩放事件
  graph.value.on('viewportchange', () => {
    // 可以在这里更新缩放级别
  });
};

// 初始化小地图
const initMinimap = () => {
  if (!minimapContainer.value || !graph.value) return;
  
  try {
    minimap.value = new G6.Minimap({
      container: minimapContainer.value,
      className: 'g6-minimap',
      viewportClassName: 'g6-minimap-viewport',
      type: 'delegate',
      size: [150, 100],
      delegateStyle: {
        fill: '#40a9ff',
        stroke: '#096dd9',
        opacity: 0.2
      }
    });
    
    graph.value.addPlugin(minimap.value);
  } catch (error) {
    console.error('初始化小地图失败:', error);
  }
};

// 处理拖拽放置
const handleDrop = (event) => {
  event.preventDefault();
  
  // 检查graph是否已初始化
  if (!graph.value) {
    console.warn('Graph未初始化，无法处理拖拽');
    return;
  }
  
  try {
    const data = event.dataTransfer.getData('text/plain');
    if (data) {
      const dragData = JSON.parse(data);
      if (dragData.type === 'test-case') {
        // 获取鼠标在画布中的位置
        const point = graph.value.getPointByClient(event.clientX, event.clientY);
        
        // 查找拖拽目标位置
        const dropTarget = findDropTarget(point);
        
        // 通知父组件处理测试用例拖拽
        emit('case-dragged', dragData.data, dropTarget);
      }
    }
  } catch (error) {
    console.error('处理拖拽失败:', error);
  }
};

function getNodeAtPoint(graph, x, y) {
  console.log('=== getNodeAtPoint 开始 ===');
  console.log('鼠标坐标:', { x, y });
  
  // 尝试多种命中检测方法
  let item = null;
  let method = '';
  
  // 方法1: getItemAt
  if (typeof graph.getItemAt === 'function') {
    item = graph.getItemAt(x, y);
    if (item) {
      method = 'getItemAt';
      console.log('✅ getItemAt命中:', item.getID ? item.getID() : item, '类型:', graph.getItemType(item));
    } else {
      console.log('❌ getItemAt未命中');
    }
  }
  
  // 方法2: getItemByCanvasXY
  if (!item && typeof graph.getItemByCanvasXY === 'function') {
    item = graph.getItemByCanvasXY(x, y);
    if (item) {
      method = 'getItemByCanvasXY';
      console.log('✅ getItemByCanvasXY命中:', item.getID ? item.getID() : item, '类型:', graph.getItemType(item));
    } else {
      console.log('❌ getItemByCanvasXY未命中');
    }
  }
  
  // 方法3: findByXY
  if (!item && typeof graph.findByXY === 'function') {
    item = graph.findByXY(x, y);
    if (item) {
      method = 'findByXY';
      console.log('✅ findByXY命中:', item.getID ? item.getID() : item, '类型:', graph.getItemType(item));
    } else {
      console.log('❌ findByXY未命中');
    }
  }
  
  // 方法4: 手动遍历所有节点进行命中检测（备用方案）
  if (!item && typeof graph.getNodes === 'function') {
    console.log('🔍 开始手动遍历节点检测...');
    const nodes = graph.getNodes();
    console.log('总节点数:', nodes.length);
    
    // 获取画布的变换信息
    const group = graph.getGroup();
    const matrix = group ? group.getMatrix() : null;
    console.log('画布变换矩阵:', matrix);
    
    // 遍历所有节点，从最深层开始（子节点优先）
    const sortedNodes = [...nodes].sort((a, b) => {
      const aModel = a.getModel();
      const bModel = b.getModel();
      // 按层级深度排序，子节点在前
      const aDepth = (aModel.id.match(/-/g) || []).length;
      const bDepth = (bModel.id.match(/-/g) || []).length;
      return bDepth - aDepth; // 深度大的在前
    });
    
    for (const node of sortedNodes) {
      try {
        const bbox = node.getBBox();
        const model = node.getModel();
        
        // 扩大命中区域（增加20px的容错范围）
        const expandedBbox = {
          minX: bbox.minX - 20,
          minY: bbox.minY - 20,
          maxX: bbox.maxX + 20,
          maxY: bbox.maxY + 20,
          width: bbox.width + 40,
          height: bbox.height + 40
        };
        
        const inBox = x >= expandedBbox.minX && x <= expandedBbox.maxX && 
                     y >= expandedBbox.minY && y <= expandedBbox.maxY;
        
        console.log(`节点 ${model.id}:`, {
          bbox: { minX: bbox.minX, minY: bbox.minY, maxX: bbox.maxX, maxY: bbox.maxY, width: bbox.width, height: bbox.height },
          expandedBbox: expandedBbox,
          mousePos: { x, y },
          inBox: inBox,
          label: model.label || model.text || '无标签'
        });
        
        if (inBox) {
          item = node;
          method = 'manual-traverse';
          console.log('✅ 手动遍历命中:', model.id, '标签:', model.label || model.text);
          break;
        }
      } catch (error) {
        console.warn('节点命中检测出错:', error, '节点:', node);
      }
    }
    
    if (!item) {
      console.log('❌ 手动遍历未命中任何节点');
    }
  }
  
  // 最终结果
  if (item) {
    const model = item.getModel();
    const bbox = item.getBBox();
    console.log('🎯 最终命中结果:', {
      method: method,
      nodeId: model.id,
      nodeLabel: model.label || model.text,
      nodeType: graph.getItemType ? graph.getItemType(item) : 'unknown',
      bbox: bbox,
      mousePos: { x, y }
    });
  } else {
    console.log('❌ getNodeAtPoint: 所有方法都未命中');
  }
  
  console.log('=== getNodeAtPoint 结束 ===');
  return item;
}

// 查找拖拽目标位置
const findDropTarget = (point) => {
  console.log('=== findDropTarget 开始 ===');
  console.log('拖拽点坐标:', point);
  
  try {
    const item = getNodeAtPoint(graph.value, point.x, point.y);
    
    if (!item) {
      console.log('❌ 未命中任何节点，拖拽到空白区域');
      return {
        type: 'root',
        parentId: 'root',
        position: point
      };
    }
    
    const itemType = graph.value.getItemType ? graph.value.getItemType(item) : 'unknown';
    console.log('命中项类型:', itemType);
    
    // 检查是否为节点的多种方法
    let isNode = false;
    
    // 方法1: 通过getItemType判断
    if (itemType === 'node') {
      isNode = true;
    }
    // 方法2: 通过getModel判断（如果item有getModel方法，通常是节点）
    else if (typeof item.getModel === 'function') {
      try {
        const model = item.getModel();
        // 检查模型是否包含节点的特征属性
        if (model && (model.id || model.label || model.text)) {
          isNode = true;
          console.log('✅ 通过getModel判断为节点:', model.id);
        }
      } catch (error) {
        console.warn('getModel调用失败:', error);
      }
    }
    // 方法3: 通过getBBox判断（如果item有getBBox方法，通常是节点）
    else if (typeof item.getBBox === 'function') {
      try {
        const bbox = item.getBBox();
        if (bbox && typeof bbox.width === 'number' && typeof bbox.height === 'number') {
          isNode = true;
          console.log('✅ 通过getBBox判断为节点');
        }
      } catch (error) {
        console.warn('getBBox调用失败:', error);
      }
    }
    
    console.log('节点判断结果:', { itemType, isNode });
    
    if (!isNode) {
      console.log('❌ 命中项不是节点，拖拽到空白区域');
      return {
        type: 'root',
        parentId: 'root',
        position: point
      };
    }
    
    const model = item.getModel();
    const bbox = item.getBBox();
    const centerY = bbox.centerY;
    
    console.log('命中节点信息:', {
      nodeId: model.id,
      nodeLabel: model.label || model.text,
      bbox: bbox,
      centerY: centerY,
      mouseY: point.y,
      diff: point.y - centerY
    });
    
    // 判断拖拽位置
    if (point.y < centerY - 20) {
      const parentId = findParentNodeId(model.id);
      console.log('✅ 拖拽到节点上方', {
        nodeId: model.id,
        parentId: parentId,
        position: 'before'
      });
      return {
        type: 'sibling',
        parentId: parentId,
        siblingId: model.id,
        position: 'before',
        targetNode: model
      };
    } else if (point.y > centerY + 20) {
      const parentId = findParentNodeId(model.id);
      console.log('✅ 拖拽到节点下方', {
        nodeId: model.id,
        parentId: parentId,
        position: 'after'
      });
      return {
        type: 'sibling',
        parentId: parentId,
        siblingId: model.id,
        position: 'after',
        targetNode: model
      };
    } else {
      console.log('✅ 拖拽到节点中心', {
        nodeId: model.id,
        parentId: model.id,
        position: 'child'
      });
      return {
        type: 'child',
        parentId: model.id,
        targetNode: model
      };
    }
  } catch (error) {
    console.error('❌ 查找拖拽目标失败:', error);
    return {
      type: 'root',
      parentId: 'root',
      position: point
    };
  } finally {
    console.log('=== findDropTarget 结束 ===');
  }
};

// 更新拖拽悬停效果
const updateDragOverlay = (point) => {
  clearDragOverlay();
  
  // 调试模式：显示所有节点的命中区域
  if (true) { // 临时开启调试模式
    showAllNodeHitboxes();
  }
  
  try {
    const item = getNodeAtPoint(graph.value, point.x, point.y);
    
    if (!item) {
      showRootDropZone(point);
      return;
    }
    
    // 检查是否为节点的多种方法
    let isNode = false;
    const itemType = graph.value.getItemType ? graph.value.getItemType(item) : 'unknown';
    
    // 方法1: 通过getItemType判断
    if (itemType === 'node') {
      isNode = true;
    }
    // 方法2: 通过getModel判断
    else if (typeof item.getModel === 'function') {
      try {
        const model = item.getModel();
        if (model && (model.id || model.label || model.text)) {
          isNode = true;
        }
      } catch (error) {
        console.warn('getModel调用失败:', error);
      }
    }
    // 方法3: 通过getBBox判断
    else if (typeof item.getBBox === 'function') {
      try {
        const bbox = item.getBBox();
        if (bbox && typeof bbox.width === 'number' && typeof bbox.height === 'number') {
          isNode = true;
        }
      } catch (error) {
        console.warn('getBBox调用失败:', error);
      }
    }
    
    if (!isNode) {
      showRootDropZone(point);
      return;
    }
    const model = item.getModel();
    const bbox = item.getBBox();
    const centerY = bbox.centerY;
    if (point.y < centerY - 20) {
      showInsertZone(item, 'before');
    } else if (point.y > centerY + 20) {
      showInsertZone(item, 'after');
    } else {
      showChildZone(item);
    }
  } catch (error) {
    console.warn('更新拖拽悬停效果失败:', error);
    showRootDropZone(point);
  }
};

// 显示所有节点的命中区域（调试用）
const showAllNodeHitboxes = () => {
  if (!graph.value || typeof graph.value.getNodes !== 'function') return;
  
  const nodes = graph.value.getNodes();
  nodes.forEach((node, index) => {
    try {
      const bbox = node.getBBox();
      const model = node.getModel();
      
      // 创建命中区域指示器
      const hitboxOverlay = document.createElement('div');
      hitboxOverlay.className = 'drag-overlay hitbox-debug';
      hitboxOverlay.style.cssText = `
        position: absolute;
        left: ${bbox.minX - 20}px;
        top: ${bbox.minY - 20}px;
        width: ${bbox.width + 40}px;
        height: ${bbox.height + 40}px;
        border: 2px dashed rgba(255, 0, 0, 0.3);
        background: rgba(255, 0, 0, 0.05);
        border-radius: 4px;
        pointer-events: none;
        z-index: 999;
        font-size: 10px;
        color: rgba(255, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        word-break: break-all;
      `;
      hitboxOverlay.innerHTML = `
        <div>
          <div>${model.id}</div>
          <div>${model.label || model.text || '无标签'}</div>
          <div>${bbox.width}x${bbox.height}</div>
        </div>
      `;
      
      document.body.appendChild(hitboxOverlay);
    } catch (error) {
      console.warn('显示节点命中区域失败:', error);
    }
  });
};

// 清除拖拽悬停效果
const clearDragOverlay = () => {
  // 移除所有拖拽提示元素
  const overlays = document.querySelectorAll('.drag-overlay');
  overlays.forEach(overlay => {
    overlay.remove();
  });
};

// 显示根级节点拖拽区域
const showRootDropZone = (point) => {
  const overlay = document.createElement('div');
  overlay.className = 'drag-overlay root-drop-zone';
  overlay.innerHTML = `
    <div class="drop-zone-content">
      <svg viewBox="0 0 1024 1024" width="16" height="16" fill="currentColor">
        <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm192 472c0 4.4-3.6 8-8 8H544v152c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V544H328c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h152V328c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v152h152c4.4 0 8 3.6 8 8v48z"/>
      </svg>
      <span>创建根级节点</span>
    </div>
  `;
  
  overlay.style.cssText = `
    position: absolute;
    left: ${point.x - 100}px;
    top: ${point.y - 30}px;
    width: 200px;
    height: 60px;
    background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, rgba(24, 144, 255, 0.05) 100%);
    border: 2px dashed #1890ff;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #1890ff;
    font-weight: 500;
    font-size: 14px;
    z-index: 1000;
    pointer-events: none;
    backdrop-filter: blur(4px);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.2);
    transition: all 0.2s ease;
  `;
  
  document.body.appendChild(overlay);
};

// 显示插入区域
const showInsertZone = (item, position) => {
  const bbox = item.getBBox();
  const overlay = document.createElement('div');
  overlay.className = 'drag-overlay insert-zone';
  overlay.innerHTML = `
    <div class="drop-zone-content">
      <svg viewBox="0 0 1024 1024" width="16" height="16" fill="currentColor">
        <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm192 472c0 4.4-3.6 8-8 8H544v152c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V544H328c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h152V328c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v152h152c4.4 0 8 3.6 8 8v48z"/>
      </svg>
      <span>插入为兄弟节点</span>
    </div>
  `;
  
  const y = position === 'before' ? bbox.minY - 30 : bbox.maxY + 10;
  
  overlay.style.cssText = `
    position: absolute;
    left: ${bbox.minX}px;
    top: ${y}px;
    width: ${bbox.width}px;
    height: 24px;
    background: linear-gradient(135deg, rgba(82, 196, 26, 0.1) 0%, rgba(82, 196, 26, 0.05) 100%);
    border: 2px dashed #52c41a;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #52c41a;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
    pointer-events: none;
    backdrop-filter: blur(4px);
    box-shadow: 0 2px 8px rgba(82, 196, 26, 0.2);
    transition: all 0.2s ease;
  `;
  
  document.body.appendChild(overlay);
};

// 显示子节点区域
const showChildZone = (item) => {
  const bbox = item.getBBox();
  const overlay = document.createElement('div');
  overlay.className = 'drag-overlay child-zone';
  overlay.innerHTML = `
    <div class="drop-zone-content">
      <svg viewBox="0 0 1024 1024" width="16" height="16" fill="currentColor">
        <path d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm192 472c0 4.4-3.6 8-8 8H544v152c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V544H328c-4.4 0-8-3.6-8-8v-48c0-4.4 3.6-8 8-8h152V328c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v152h152c4.4 0 8 3.6 8 8v48z"/>
      </svg>
      <span>添加为子节点</span>
    </div>
  `;
  
  overlay.style.cssText = `
    position: absolute;
    left: ${bbox.minX}px;
    top: ${bbox.minY}px;
    width: ${bbox.width}px;
    height: ${bbox.height}px;
    background: linear-gradient(135deg, rgba(250, 140, 22, 0.1) 0%, rgba(250, 140, 22, 0.05) 100%);
    border: 2px dashed #fa8c16;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fa8c16;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
    pointer-events: none;
    backdrop-filter: blur(4px);
    box-shadow: 0 4px 12px rgba(250, 140, 22, 0.2);
    transition: all 0.2s ease;
  `;
  
  document.body.appendChild(overlay);
};

// 处理拖拽离开
const handleDragLeave = (event) => {
  // 清除拖拽悬停效果
  clearDragOverlay();
};

// 处理拖拽结束
const handleDragEnd = (event) => {
  // 只做UI清理，不做任何数据变更
  clearDragOverlay();
};

  // 初始化默认数据
  const initializeDefaultData = () => {
    const containerHeight = mindMapContainer.value?.offsetHeight || 600;
    
    const defaultData = {
      id: 'root',
      label: '接口自动化测试',
      x: 200, // 根节点初始X位置
      y: containerHeight / 2, // 根节点初始Y位置，垂直居中
      children: [],
      data: {
        type: 'root',
        text: '接口自动化测试',
        notes: '',
        status: 'none',
        testCase: null,
        condition: 'none',
        priority: 'medium',
        params: [],
        extractions: [],
        customCondition: ''
      }
    };
    
    if (graph.value) {
      graph.value.data(defaultData);
      graph.value.render();
      
      // 设置根节点位置到画布的左侧中心
      setTimeout(() => {
        const containerWidth = mindMapContainer.value.offsetWidth;
        const containerHeight = mindMapContainer.value.offsetHeight;
        
        // 将根节点定位到画布左侧中心位置
        const rootX = 200; // 距离左边200px
        const rootY = containerHeight / 2; // 垂直居中
        
        const rootNode = graph.value.findById('root');
        if (rootNode) {
          graph.value.updateItem(rootNode, {
            x: rootX,
            y: rootY
          });
        }
      }, 100);
      
      hasData.value = true;
      emit('data-changed');
    }
  };
  
  // 自动初始化默认数据
  const autoInitialize = () => {
    setTimeout(() => {
      if (!hasData.value) {
        initializeDefaultData();
      }
    }, 1000);
  };

// 响应式调整大小
const handleResize = () => {
  if (graph.value && mindMapContainer.value) {
    // 获取容器的实际尺寸
    const containerWidth = mindMapContainer.value.offsetWidth;
    const containerHeight = mindMapContainer.value.offsetHeight;
    
    // 只有当尺寸真正发生变化时才调整
    if (containerWidth > 0 && containerHeight > 0) {
      graph.value.changeSize(containerWidth, containerHeight);
      
      // 移除fitView调用，保持当前缩放级别
      // setTimeout(() => {
      //   if (graph.value) {
      //     graph.value.fitView();
      //   }
      // }, 50);
    }
  }
};

// 强制刷新画布
const forceRefresh = () => {
  if (graph.value && mindMapContainer.value) {
    // 强制重新计算容器尺寸
    const rect = mindMapContainer.value.getBoundingClientRect();
    
    graph.value.changeSize(rect.width, rect.height);
    // 移除fitView调用，保持当前缩放级别
    graph.value.refresh();
  }
};

// 暴露方法给父组件
const zoomIn = () => graph.value?.zoom(1.2);
const zoomOut = () => graph.value?.zoom(0.8);
const fitView = () => graph.value?.fitView();
const downloadImage = (type = 'image/png') => {
  return graph.value?.downloadImage('mindmap', type);
};

defineExpose({
  zoomIn,
  zoomOut,
  fitView,
  downloadImage,
  handleResize,
  forceRefresh,
  graph
});

// 生命周期
onMounted(() => {
  nextTick(() => {
    initGraph();
    autoInitialize();
    window.addEventListener('resize', handleResize);
    
    // 添加ResizeObserver来监听容器大小变化
    if (mindMapContainer.value && window.ResizeObserver) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (let entry of entries) {
          handleResize();
        }
      });
      
      resizeObserver.observe(mindMapContainer.value);
      
      // 在组件卸载时清理observer
      onUnmounted(() => {
        resizeObserver.disconnect();
      });
    }
  });
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (graph.value) {
    graph.value.destroy();
  }
});

// 监听容器大小变化
watch(() => [props.width, props.height], () => {
  handleResize();
});
</script>

<style scoped>
.mind-map-canvas {
  position: relative;
  width: 100%;
  height: 100%;
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 16px;
  overflow: hidden;
  background: 
    radial-gradient(circle at 20% 50%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 50%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 20%, rgba(120, 200, 198, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%),
    linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%), 
    linear-gradient(-45deg, rgba(255,255,255,0.1) 25%, transparent 25%), 
    linear-gradient(45deg, transparent 75%, rgba(255,255,255,0.1) 75%), 
    linear-gradient(-45deg, transparent 75%, rgba(255,255,255,0.1) 75%);
  background-size: 
    600px 600px,
    600px 600px,
    600px 600px,
    100% 100%,
    30px 30px,
    30px 30px,
    30px 30px,
    30px 30px;
  background-position: 0 0, 0 0, 0 0, 0 0, 0 0, 0 15px, 15px -15px, -15px 0px;
  box-shadow: 
    0 20px 40px rgba(0,0,0,0.1),
    0 8px 16px rgba(0,0,0,0.05),
    inset 0 1px 0 rgba(255,255,255,0.3);
}

#mindmap-container {
  width: 100%;
  height: 100%;
}

.minimap-container {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 
    0 12px 24px rgba(0, 0, 0, 0.15),
    0 4px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
}

.minimap-container:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 16px 32px rgba(0, 0, 0, 0.2),
    0 8px 16px rgba(0, 0, 0, 0.15);
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.empty-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}

/* G6 样式覆盖 */
:deep(.g6-minimap) {
  border: none;
}

:deep(.g6-minimap-viewport) {
  border: 2px solid #1890ff;
}

:deep(.g6-tooltip) {
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* 自定义滚动条 */
:deep(.g6-component-minimap) {
  border-radius: 4px;
}

/* 拖拽区域样式 */
.drop-zone-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.drop-zone-content svg {
  flex-shrink: 0;
}

/* 拖拽悬停动画 */
.drag-overlay {
  animation: dragOverlayFadeIn 0.2s ease-out;
}

@keyframes dragOverlayFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .minimap-container {
    display: none;
  }
}
</style> 