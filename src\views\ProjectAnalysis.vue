<template>
  <Home>
    <PageContainer title="项目分析">
      <!-- 添加查询表单 -->
      <div class="search-form">
        <el-form :inline="true" :model="searchForm" class="form-inline">
          <el-form-item label="项目名称">
            <el-select
              v-model="searchForm.projectId"
              placeholder="请选择项目"
              clearable
              filterable
              @clear="handleSearch"
              @change="handleSearch"
              style="width: 200px"
            >
              <el-option
                v-for="project in projectList"
                :key="project.id"
                :label="project.name"
                :value="project.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="项目状态">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择项目状态"
              clearable
              @clear="handleSearch"
            >
              <el-option label="活跃" :value="0" />
              <el-option label="未活跃" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @clear="handleSearch"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">
              <el-icon><Search /></el-icon>查询
            </el-button>
            <el-button @click="resetSearch">
              <el-icon><Refresh /></el-icon>重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 顶部统计卡片 -->
      <div class="stat-cards" v-loading="statisticsLoading">
        <el-card v-for="(stat, index) in statistics" :key="index" class="stat-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <span>{{ stat.title }}</span>
              <el-icon class="icon" :style="{ color: stat.color }">
                <component :is="stat.icon" />
              </el-icon>
            </div>
          </template>
          <div class="card-value" :style="{ color: stat.color }">{{ stat.value }}</div>
          <div class="card-trend">
            <span :class="stat.trend_type === 'up' ? 'up' : 'down'">
              {{ Math.abs(stat.trend) }}% 
              <el-icon><component :is="stat.trend_type === 'up' ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
            </span>
            较上周
          </div>
        </el-card>
      </div>

      <!-- 图表展示区域 -->
      <div class="charts-container">
        <el-row :gutter="20">
          <!-- 执行趋势图 -->
          <el-col :span="18">
            <el-card class="chart-card" shadow="hover" v-loading="trendLoading">
              <template #header>
                <div class="card-header">
                  <span>测试执行趋势</span>
                  <div class="header-controls">
                    <el-radio-group v-model="trendTimeRange" size="small" @change="fetchExecutionTrend">
                      <el-radio-button label="week">近7天</el-radio-button>
                      <el-radio-button label="month">近30天</el-radio-button>
                      <el-radio-button label="quarter">近3个月</el-radio-button>
                    </el-radio-group>
                    <el-button type="primary" plain size="small" @click="fetchExecutionTrend">
                      <el-icon><Refresh /></el-icon>
                    </el-button>
                  </div>
                </div>
              </template>
              <div class="chart-wrapper">
                <LineChart 
                  :data="executionTrendData"
                  :options="lineChartOptions"
                />
              </div>
            </el-card>
          </el-col>

          <!-- 通过率分布图 -->
          <el-col :span="6">
            <el-card class="chart-card defect-distribution-card" shadow="hover" v-loading="defectLoading">
              <template #header>
                <div class="card-header">
                  <span>缺陷严重程度分布</span>
                  <el-button type="primary" plain size="small" @click="fetchDefectDistribution">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </div>
              </template>
              <div class="defect-chart-wrapper">
                <!-- 总体统计 -->
                <div class="defect-summary" v-if="defectSummary.total > 0">
                  <div class="summary-item">
                    <span class="summary-label">总缺陷数</span>
                    <span class="summary-value total">{{ defectSummary.total }}</span>
                  </div>
                  <div class="summary-item">
                    <span class="summary-label">本周新增</span>
                    <span class="summary-value new" :class="defectSummary.weeklyTrend >= 0 ? 'up' : 'down'">
                      {{ defectSummary.weeklyNew }}
                      <el-icon><component :is="defectSummary.weeklyTrend >= 0 ? 'ArrowUp' : 'ArrowDown'" /></el-icon>
                    </span>
                  </div>
                </div>
                
                <!-- 饼图 -->
                <div class="chart-wrapper" v-if="defectDistributionData.length > 0">
                  <PieChart 
                    :data="defectDistributionData"
                    :options="pieChartOptions"
                  />
                </div>
                
                <!-- 详细列表 -->
                <div class="defect-details" v-if="defectDistributionData.length > 0">
                  <div 
                    v-for="(item, index) in defectDistributionData" 
                    :key="index" 
                    class="defect-item"
                  >
                    <div class="defect-indicator" :style="{ backgroundColor: item.color }"></div>
                    <div class="defect-info">
                      <div class="defect-name">{{ item.name }}</div>
                      <div class="defect-stats">
                        <span class="defect-count">{{ item.value }}个</span>
                        <span class="defect-percentage">{{ item.percentage.toFixed(1) }}%</span>
                      </div>
                    </div>
                    <div class="defect-progress">
                      <el-progress 
                        :percentage="item.percentage" 
                        :color="item.color"
                        :stroke-width="6"
                        :show-text="false"
                      />
                    </div>
                  </div>
                </div>
                
                <!-- 空状态 -->
                <div v-if="defectDistributionData.length === 0 && !defectLoading" class="empty-state">
                  <el-empty description="暂无缺陷数据" :image-size="80" />
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 执行成功率趋势 -->
        <el-row :gutter="20" style="margin-top: 20px;">
          <el-col :span="14">
            <el-card class="chart-card" shadow="hover" v-loading="passRateLoading">
              <template #header>
                <div class="card-header">
                  <span>执行成功率趋势</span>
                  <el-button type="primary" plain size="small" @click="fetchPassRateTrend">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </div>
              </template>
              <div class="chart-wrapper">
                <LineChart 
                  :data="passRateTrendData"
                  :options="passRateChartOptions"
                />
              </div>
            </el-card>
          </el-col>

          <!-- 执行时长分布 -->
          <el-col :span="10">
            <el-card class="chart-card" shadow="hover" v-loading="durationLoading">
              <template #header>
                <div class="card-header">
                  <span>执行时长分布</span>
                  <el-button type="primary" plain size="small" @click="fetchDurationDistribution">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </div>
              </template>
              <div class="chart-wrapper">
                <BarChart 
                  :data="durationDistributionData"
                  :options="barChartOptions"
                />
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 测试用例执行情况轮播 -->
        <el-card class="execution-list" shadow="hover" v-loading="executionLoading">
          <template #header>
            <div class="card-header">
              <span>最近执行记录</span>
              <div class="header-controls">
                <el-button type="primary" link @click="viewAllExecutions">查看全部</el-button>
                <el-button type="primary" plain size="small" @click="fetchRecentExecutions">
                  <el-icon><Refresh /></el-icon>
                </el-button>
              </div>
            </div>
          </template>
          <div v-if="executionRecords.length > 0" class="execution-content">
            <el-carousel 
              height="280px" 
              :interval="4000" 
              indicator-position="none"
              direction="vertical"
              :autoplay="true"
            >
              <el-carousel-item v-for="(group, index) in executionRecords" :key="index">
                <div class="execution-group">
                  <div v-for="record in group" :key="record.id" class="execution-item">
                    <div class="execution-info">
                      <el-tag 
                        :type="getExecutionStatusType(record.status)" 
                        size="small"
                        effect="light"
                      >
                        {{ getExecutionStatusText(record.status) }}
                      </el-tag>
                      <span class="case-name">{{ record.case_name }}</span>
                      <el-tag v-if="record.project_name" type="info" size="small" effect="plain">
                        {{ record.project_name }}
                      </el-tag>
                    </div>
                    <div class="execution-meta">
                      <span class="execution-time">{{ formatExecutionTime(record.execute_time) }}</span>
                      <span class="execution-duration">{{ record.duration }}s</span>
                    </div>
                  </div>
                </div>
              </el-carousel-item>
            </el-carousel>
          </div>
          <div v-else class="no-data">
            <el-empty description="暂无执行记录" />
          </div>
        </el-card>
      </div>
    </PageContainer>
  </Home>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { 
  DataLine, 
  Timer, 
  CircleCheck, 
  Warning,
  ArrowUp,
  ArrowDown,
  Search,
  Refresh
} from '@element-plus/icons-vue';
import Home from '@/components/HomePage.vue';
import PageContainer from '@/components/PageContainer.vue';
import LineChart from '@/components/LineChart.vue';
import PieChart from '@/components/PieChart.vue';
import BarChart from '@/components/BarChart.vue';
import request from '@/utils/request';
import { ElMessage } from 'element-plus';
import { formatDate } from '@/utils/format';

// 加载状态
const loading = ref(false);
const statisticsLoading = ref(false);
const trendLoading = ref(false);
const defectLoading = ref(false);
const passRateLoading = ref(false);
const durationLoading = ref(false);
const executionLoading = ref(false);

// 统计数据
const statistics = ref([
  {
    title: '总执行次数',
    value: '0',
    icon: 'DataLine',
    color: '#409EFF',
    trend: 0,
    trend_type: 'up'
  },
  {
    title: '平均执行时长',
    value: '0s',
    icon: 'Timer',
    color: '#67C23A',
    trend: 0,
    trend_type: 'up'
  },
  {
    title: '通过率',
    value: '0%',
    icon: 'CircleCheck',
    color: '#67C23A',
    trend: 0,
    trend_type: 'up'
  },
  {
    title: '失败用例',
    value: '0',
    icon: 'Warning',
    color: '#F56C6C',
    trend: 0,
    trend_type: 'down'
  }
]);

// 时间范围选择
const trendTimeRange = ref('week');

// 执行趋势数据
const executionTrendData = ref({
  labels: [],
  datasets: []
});

// 缺陷分布数据
const defectDistributionData = ref([]);

// 缺陷汇总数据
const defectSummary = ref({
  total: 0,
  weeklyNew: 0,
  weeklyTrend: 0
});

// 通过率趋势数据
const passRateTrendData = ref({
  labels: [],
  datasets: []
});

// 执行时长分布数据
const durationDistributionData = ref({
  labels: [],
  datasets: []
});

// 执行记录数据
const executionRecords = ref([]);

// 查询表单数据
const searchForm = ref({
  projectId: '',
  status: '',
  dateRange: []
});

// 项目列表数据
const projectList = ref([]);

// 构建通用查询参数
const buildQueryParams = (additionalParams = {}) => {
  const params = { ...additionalParams };
  
  if (searchForm.value.projectId) {
    params.project_id = searchForm.value.projectId;
  }
  if (searchForm.value.status !== '') {
    params.status = searchForm.value.status;
  }
  if (searchForm.value.dateRange && searchForm.value.dateRange.length === 2) {
    params.start_date = searchForm.value.dateRange[0];
    params.end_date = searchForm.value.dateRange[1];
  }
  
  console.log('构建的查询参数:', params);
  return params;
};

// 获取项目列表
const fetchProjectList = async () => {
  try {
    const response = await request.get('/api/project/');
    console.log('项目列表API响应:', response.data);
    
    if (response.data.code === 200) {
      // 根据实际API返回的数据结构调整
      let projects = [];
      
      if (response.data.data) {
        if (Array.isArray(response.data.data)) {
          projects = response.data.data;
        } else if (response.data.data.projects) {
          projects = response.data.data.projects;
        } else if (response.data.data.list) {
          projects = response.data.data.list;
        }
      }
      
      projectList.value = projects;
      console.log('解析后的项目列表:', projectList.value);
      
      if (projectList.value.length === 0) {
        console.warn('未获取到项目数据，使用默认数据');
        // 使用默认项目数据
        projectList.value = [
          { id: 1, name: '测试项目A' },
          { id: 2, name: '测试项目B' },
          { id: 3, name: '自动化测试项目' }
        ];
      }
    }
  } catch (error) {
    console.error('获取项目列表失败:', error);
    ElMessage.error('获取项目列表失败');
    // 使用默认项目数据
    projectList.value = [
      { id: 1, name: '测试项目A' },
      { id: 2, name: '测试项目B' },
      { id: 3, name: '自动化测试项目' }
    ];
  }
};

// 获取项目统计概览
const fetchStatistics = async () => {
  statisticsLoading.value = true;
  try {
    const params = buildQueryParams();
    const response = await request.get('/api/project/statistics', { params });
    if (response.data.code === 200) {
      const data = response.data.data;
      statistics.value = [
        {
          title: '总执行次数',
          value: data.total_executions.value.toLocaleString(),
          icon: 'DataLine',
          color: '#409EFF',
          trend: data.total_executions.trend,
          trend_type: data.total_executions.trend_type
        },
        {
          title: '平均执行时长',
          value: `${data.avg_execution_time.value}${data.avg_execution_time.unit}`,
          icon: 'Timer',
          color: '#67C23A',
          trend: data.avg_execution_time.trend,
          trend_type: data.avg_execution_time.trend_type
        },
        {
          title: '通过率',
          value: `${data.pass_rate.value}${data.pass_rate.unit}`,
          icon: 'CircleCheck',
          color: '#67C23A',
          trend: data.pass_rate.trend,
          trend_type: data.pass_rate.trend_type
        },
        {
          title: '失败用例',
          value: data.failed_cases.value.toString(),
          icon: 'Warning',
          color: '#F56C6C',
          trend: data.failed_cases.trend,
          trend_type: data.failed_cases.trend_type
        }
      ];
    }
  } catch (error) {
    console.error('获取统计数据失败:', error);
    ElMessage.error('获取统计数据失败');
  } finally {
    statisticsLoading.value = false;
  }
};

// 获取执行趋势数据
const fetchExecutionTrend = async () => {
  trendLoading.value = true;
  try {
    const params = buildQueryParams({ time_range: trendTimeRange.value });
    const response = await request.get('/api/project/execution-trend', { params });
    if (response.data.code === 200) {
      const data = response.data.data;
      executionTrendData.value = {
        labels: data.labels,
        datasets: data.datasets.map(dataset => ({
          label: dataset.name,
          data: dataset.data,
          borderColor: dataset.color,
          backgroundColor: dataset.color + '20',
          fill: true,
          tension: 0.4,
          borderWidth: 2,
          pointRadius: 4,
          pointHoverRadius: 6
        }))
      };
    }
  } catch (error) {
    console.error('获取执行趋势数据失败:', error);
    ElMessage.error('获取执行趋势数据失败');
  } finally {
    trendLoading.value = false;
  }
};

// 获取缺陷分布数据
const fetchDefectDistribution = async () => {
  defectLoading.value = true;
  try {
    const params = buildQueryParams();
    const response = await request.get('/api/project/defect-distribution', { params });
    if (response.data.code === 200) {
      const data = response.data.data;
      defectDistributionData.value = data.severity_distribution;
      
      // 计算汇总数据
      const total = data.severity_distribution.reduce((sum, item) => sum + item.value, 0);
      defectSummary.value = {
        total: total,
        weeklyNew: data.weekly_new || 0,
        weeklyTrend: data.weekly_trend || 0
      };
    }
  } catch (error) {
    console.error('获取缺陷分布数据失败:', error);
    ElMessage.error('获取缺陷分布数据失败');
  } finally {
    defectLoading.value = false;
  }
};

// 获取通过率趋势数据
const fetchPassRateTrend = async () => {
  passRateLoading.value = true;
  try {
    const params = buildQueryParams();
    const response = await request.get('/api/project/pass-rate-trend', { params });
    if (response.data.code === 200) {
      const data = response.data.data;
      passRateTrendData.value = {
        labels: data.labels,
        datasets: [{
          label: '通过率',
          data: data.pass_rates,
          borderColor: '#67C23A',
          backgroundColor: '#67C23A20',
          fill: true,
          tension: 0.4,
          borderWidth: 3,
          pointRadius: 5,
          pointHoverRadius: 8
        }]
      };
    }
  } catch (error) {
    console.error('获取通过率趋势数据失败:', error);
    ElMessage.error('获取通过率趋势数据失败');
  } finally {
    passRateLoading.value = false;
  }
};

// 获取执行时长分布数据
const fetchDurationDistribution = async () => {
  durationLoading.value = true;
  try {
    const params = buildQueryParams();
    const response = await request.get('/api/project/duration-distribution', { params });
    if (response.data.code === 200) {
      const data = response.data.data;
      durationDistributionData.value = {
        labels: data.labels,
        datasets: [{
          label: '用例数量',
          data: data.counts,
          backgroundColor: [
            '#67C23A',
            '#409EFF',
            '#E6A23C',
            '#F56C6C',
            '#909399'
          ],
          borderWidth: 1
        }]
      };
    }
  } catch (error) {
    console.error('获取执行时长分布数据失败:', error);
    ElMessage.error('获取执行时长分布数据失败');
  } finally {
    durationLoading.value = false;
  }
};

// 获取最近执行记录
const fetchRecentExecutions = async () => {
  executionLoading.value = true;
  try {
    const params = buildQueryParams({ limit: 20 });
    const response = await request.get('/api/project/recent-executions', { params });
    if (response.data.code === 200) {
      const executions = response.data.data.executions;
      // 将执行记录分组，每组4条记录
      const groups = [];
      for (let i = 0; i < executions.length; i += 4) {
        groups.push(executions.slice(i, i + 4));
      }
      executionRecords.value = groups;
    }
  } catch (error) {
    console.error('获取最近执行记录失败:', error);
    ElMessage.error('获取最近执行记录失败');
  } finally {
    executionLoading.value = false;
  }
};

// 图表配置
const lineChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  interaction: {
    intersect: false,
    mode: 'index'
  },
  plugins: {
    legend: {
      position: 'top',
      align: 'start',
      labels: {
        usePointStyle: true,
        padding: 20,
        font: {
          size: 13
        },
        boxWidth: 8,
        boxHeight: 8
      }
    },
    tooltip: {
      mode: 'index',
      intersect: false,
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      titleColor: '#303133',
      bodyColor: '#606266',
      borderColor: '#E4E7ED',
      borderWidth: 1,
      padding: 12,
      bodyFont: {
        size: 13
      },
      titleFont: {
        size: 14,
        weight: 'bold'
      },
      callbacks: {
        label: function(context) {
          let label = context.dataset.label || '';
          if (label) {
            label += ': ';
          }
          label += context.parsed.y + '个';
          return label;
        }
      }
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        drawBorder: false,
        color: 'rgba(0, 0, 0, 0.05)'
      },
      ticks: {
        font: {
          size: 12
        }
      }
    },
    x: {
      grid: {
        display: false
      },
      ticks: {
        font: {
          size: 12
        }
      }
    }
  }
};

const passRateChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      titleColor: '#303133',
      bodyColor: '#606266',
      borderColor: '#E4E7ED',
      borderWidth: 1,
      callbacks: {
        label: function(context) {
          return `通过率: ${context.parsed.y}%`;
        }
      }
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      max: 100,
      grid: {
        drawBorder: false,
        color: 'rgba(0, 0, 0, 0.05)'
      },
      ticks: {
        callback: function(value) {
          return value + '%';
        }
      }
    },
    x: {
      grid: {
        display: false
      }
    }
  }
};

const pieChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  layout: {
    padding: {
      top: 5,
      right: 5,
      bottom: 5,
      left: 5
    }
  },
  plugins: {
    legend: {
      display: false  // 完全隐藏图例，使用自定义列表
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      titleColor: '#303133',
      bodyColor: '#606266',
      borderColor: '#E4E7ED',
      borderWidth: 1,
      padding: 12,
      titleFont: {
        size: 14,
        weight: 'bold'
      },
      bodyFont: {
        size: 13
      },
      callbacks: {
        label: function(context) {
          const value = context.raw;
          const total = context.dataset.data.reduce((a, b) => a + b, 0);
          const percentage = ((value / total) * 100).toFixed(1);
          return `${context.label}: ${percentage}% (${value}个)`;
        }
      }
    }
  },
  elements: {
    arc: {
      borderWidth: 2,
      borderColor: '#fff',
      hoverBorderWidth: 3,
      hoverOffset: 6
    }
  },
  interaction: {
    intersect: false
  }
};

const barChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false
    },
    tooltip: {
      backgroundColor: 'rgba(255, 255, 255, 0.95)',
      titleColor: '#303133',
      bodyColor: '#606266',
      borderColor: '#E4E7ED',
      borderWidth: 1
    }
  },
  scales: {
    y: {
      beginAtZero: true,
      grid: {
        drawBorder: false,
        color: 'rgba(0, 0, 0, 0.05)'
      }
    },
    x: {
      grid: {
        display: false
      }
    }
  }
};

// 工具函数
const getExecutionStatusType = (status) => {
  const statusMap = {
    'passed': 'success',
    'failed': 'danger',
    'error': 'warning',
    'skipped': 'info'
  };
  return statusMap[status] || 'info';
};

const getExecutionStatusText = (status) => {
  const statusMap = {
    'passed': '通过',
    'failed': '失败',
    'error': '错误',
    'skipped': '跳过'
  };
  return statusMap[status] || status;
};

const formatExecutionTime = (timeStr) => {
  return formatDate(timeStr);
};

const viewAllExecutions = () => {
  // 跳转到执行记录页面
  router.push('/execution');
};

// 处理查询
const handleSearch = async () => {
  loading.value = true;
  try {
    // 重新获取所有数据
    await Promise.all([
      fetchStatistics(),
      fetchExecutionTrend(),
      fetchDefectDistribution(),
      fetchPassRateTrend(),
      fetchDurationDistribution(),
      fetchRecentExecutions()
    ]);
  } catch (error) {
    console.error('查询失败:', error);
    ElMessage.error('查询失败');
  } finally {
    loading.value = false;
  }
};

// 重置查询条件
const resetSearch = () => {
  searchForm.value = {
    projectId: '',
    status: '',
    dateRange: []
  };
  handleSearch();
};

// 监听时间范围变化
watch(trendTimeRange, () => {
  fetchExecutionTrend();
});

// 页面加载时执行查询
onMounted(async () => {
  await fetchProjectList(); // 先获取项目列表
  handleSearch(); // 再执行查询
});
</script>

<style scoped>
.stat-cards {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.stat-card {
  transition: all 0.3s ease;
  border-radius: 8px;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.card-value {
  font-size: 32px;
  font-weight: bold;
  margin: 16px 0 8px 0;
  line-height: 1;
}

.card-trend {
  font-size: 13px;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 4px;
}

.card-trend .up {
  color: #67C23A;
  font-weight: 500;
}

.card-trend .down {
  color: #F56C6C;
  font-weight: 500;
}

.charts-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.chart-card {
  height: 420px;
  border-radius: 8px;
}

.chart-wrapper {
  height: 320px;
  padding: 10px;
}

.execution-list {
  margin-top: 20px;
  border-radius: 8px;
}

.execution-content {
  min-height: 280px;
}

.execution-group {
  padding: 0 20px;
}

.execution-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #f0f2f5;
  transition: all 0.2s ease;
}

.execution-item:hover {
  background-color: #f8f9fa;
  border-radius: 6px;
  margin: 0 -12px;
  padding: 16px 12px;
}

.execution-item:last-child {
  border-bottom: none;
}

.execution-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.case-name {
  color: #303133;
  font-weight: 500;
  flex: 1;
}

.execution-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.execution-time {
  color: #909399;
  font-size: 13px;
}

.execution-duration {
  color: #606266;
  font-size: 12px;
  background-color: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 280px;
}

:deep(.el-carousel__container) {
  height: 280px;
}

:deep(.el-carousel__item) {
  overflow-y: hidden;
}

.search-form {
  background: #fff;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.form-inline {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-start;
}

:deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 0;
}

:deep(.el-input),
:deep(.el-select) {
  width: 200px;
}

:deep(.el-date-picker) {
  width: 320px;
}

:deep(.el-button) {
  display: flex;
  align-items: center;
  gap: 4px;
}

  :deep(.el-radio-group .el-radio-button__inner) {
    padding: 8px 16px;
    font-size: 13px;
  }

  /* 缺陷分布卡片样式 */
  .defect-distribution-card {
    height: auto;
    min-height: 420px;
  }

  .defect-chart-wrapper {
    display: flex;
    flex-direction: column;
    height: 100%;
    gap: 12px;
  }

  .defect-summary {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    padding: 12px;
    background-color: #f8f9fa;
    border-radius: 6px;
  }

  .summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
  }

  .summary-label {
    font-size: 12px;
    color: #909399;
    margin-bottom: 4px;
  }

  .summary-value {
    font-size: 18px;
    font-weight: bold;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .summary-value.total {
    color: #409EFF;
  }

  .summary-value.new.up {
    color: #F56C6C;
  }

  .summary-value.new.down {
    color: #67C23A;
  }

  .defect-details {
    margin-top: 16px;
    max-height: 200px;
    overflow-y: auto;
  }

  .defect-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f0f2f5;
  }

  .defect-item:last-child {
    border-bottom: none;
  }

  .defect-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
    flex-shrink: 0;
  }

  .defect-info {
    flex: 1;
    min-width: 0;
  }

  .defect-name {
    font-size: 13px;
    font-weight: 500;
    color: #303133;
    margin-bottom: 2px;
  }

  .defect-stats {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .defect-count {
    font-size: 12px;
    color: #606266;
  }

  .defect-percentage {
    font-size: 12px;
    color: #909399;
  }

  .defect-progress {
    width: 60px;
    margin-left: 8px;
  }

  .defect-progress :deep(.el-progress-bar__outer) {
    background-color: #f0f2f5;
  }

  .empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
  }

  /* 缺陷分布卡片中的图表样式 */
  .defect-distribution-card .chart-wrapper {
    height: 160px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

/* 响应式设计 */
@media (max-width: 1200px) {
  .stat-cards {
    grid-template-columns: repeat(2, 1fr);
    max-width: 100%;
  }
  
  .charts-container :deep(.el-col:first-child) {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .stat-cards {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  .form-inline {
    flex-direction: column;
    align-items: stretch;
  }
  
  :deep(.el-input),
  :deep(.el-select),
  :deep(.el-date-picker) {
    width: 100%;
  }
  
  .charts-container :deep(.el-col) {
    margin-bottom: 16px;
  }
  
  .chart-card {
    height: 350px;
  }
  
  .chart-wrapper {
    height: 250px;
  }
  
  .defect-distribution-card {
    height: auto;
    min-height: 300px;
  }
  
  .defect-distribution-card .chart-wrapper {
    height: 120px;
  }
  
  .defect-summary {
    flex-direction: column;
    gap: 8px;
  }
  
  .summary-item {
    flex-direction: row;
    justify-content: space-between;
  }
  
  .defect-details {
    max-height: 150px;
  }
}
</style> 