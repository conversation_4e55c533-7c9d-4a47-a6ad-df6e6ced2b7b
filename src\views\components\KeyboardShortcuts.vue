<template>
  <el-dialog
    v-model="visible"
    title="快捷键帮助"
    width="600px"
    @close="handleClose"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
  >
    <div class="shortcuts-container">
      <div class="shortcuts-section" v-for="section in shortcutSections" :key="section.title">
        <h4 class="section-title">{{ section.title }}</h4>
        <div class="shortcuts-list">
          <div 
            v-for="shortcut in section.shortcuts" 
            :key="shortcut.key"
            class="shortcut-item"
          >
            <div class="shortcut-keys">
              <kbd 
                v-for="key in shortcut.keys" 
                :key="key" 
                class="key"
              >
                {{ key }}
              </kbd>
            </div>
            <div class="shortcut-description">
              {{ shortcut.description }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handleClose">知道了</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'close']);

const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

// 快捷键数据
const shortcutSections = ref([
  {
    title: '文件操作',
    shortcuts: [
      {
        key: 'save',
        keys: ['Ctrl', 'S'],
        description: '保存脑图'
      },
      {
        key: 'export',
        keys: ['Ctrl', 'E'],
        description: '导出脑图'
      },
      {
        key: 'import',
        keys: ['Ctrl', 'I'],
        description: '导入脑图'
      }
    ]
  },
  {
    title: '编辑操作',
    shortcuts: [
      {
        key: 'undo',
        keys: ['Ctrl', 'Z'],
        description: '撤销操作'
      },
      {
        key: 'redo',
        keys: ['Ctrl', 'Y'],
        description: '重做操作'
      },
      {
        key: 'copy',
        keys: ['Ctrl', 'C'],
        description: '复制节点'
      },
      {
        key: 'paste',
        keys: ['Ctrl', 'V'],
        description: '粘贴节点'
      },
      {
        key: 'cut',
        keys: ['Ctrl', 'X'],
        description: '剪切节点'
      },
      {
        key: 'delete',
        keys: ['Delete'],
        description: '删除选中节点'
      },
      {
        key: 'duplicate',
        keys: ['Ctrl', 'D'],
        description: '复制并粘贴节点'
      }
    ]
  },
  {
    title: '节点操作',
    shortcuts: [
      {
        key: 'add-child',
        keys: ['Tab'],
        description: '添加子节点'
      },
      {
        key: 'add-sibling',
        keys: ['Enter'],
        description: '添加同级节点'
      },
      {
        key: 'add-parent',
        keys: ['Shift', 'Tab'],
        description: '添加父节点'
      },
      {
        key: 'edit',
        keys: ['F2'],
        description: '编辑节点名称'
      },
      {
        key: 'expand-collapse',
        keys: ['Space'],
        description: '展开/收起节点'
      }
    ]
  },
  {
    title: '导航操作',
    shortcuts: [
      {
        key: 'move-up',
        keys: ['↑'],
        description: '选择上一个节点'
      },
      {
        key: 'move-down',
        keys: ['↓'],
        description: '选择下一个节点'
      },
      {
        key: 'move-left',
        keys: ['←'],
        description: '选择父节点'
      },
      {
        key: 'move-right',
        keys: ['→'],
        description: '选择第一个子节点'
      },
      {
        key: 'home',
        keys: ['Home'],
        description: '选择根节点'
      },
      {
        key: 'end',
        keys: ['End'],
        description: '选择最后一个节点'
      }
    ]
  },
  {
    title: '视图操作',
    shortcuts: [
      {
        key: 'zoom-in',
        keys: ['Ctrl', '+'],
        description: '放大视图'
      },
      {
        key: 'zoom-out',
        keys: ['Ctrl', '-'],
        description: '缩小视图'
      },
      {
        key: 'reset-zoom',
        keys: ['Ctrl', '0'],
        description: '重置缩放'
      },
      {
        key: 'fit-view',
        keys: ['Ctrl', 'F'],
        description: '适应视图'
      },
      {
        key: 'full-screen',
        keys: ['F11'],
        description: '全屏显示'
      }
    ]
  },
  {
    title: '执行操作',
    shortcuts: [
      {
        key: 'execute-selected',
        keys: ['Ctrl', 'R'],
        description: '执行选中节点'
      },
      {
        key: 'execute-all',
        keys: ['Ctrl', 'Shift', 'R'],
        description: '执行全部节点'
      },
      {
        key: 'stop-execution',
        keys: ['Ctrl', 'Break'],
        description: '停止执行'
      }
    ]
  },
  {
    title: '其他操作',
    shortcuts: [
      {
        key: 'search',
        keys: ['Ctrl', 'F'],
        description: '搜索节点'
      },
      {
        key: 'help',
        keys: ['F1'],
        description: '显示快捷键帮助'
      },
      {
        key: 'escape',
        keys: ['Esc'],
        description: '取消选择/关闭弹窗'
      },
      {
        key: 'select-all',
        keys: ['Ctrl', 'A'],
        description: '选择所有节点'
      }
    ]
  }
]);

const handleClose = () => {
  visible.value = false;
  emit('close');
};
</script>

<style scoped>
.shortcuts-container {
  max-height: 500px;
  overflow-y: auto;
}

.shortcuts-section {
  margin-bottom: 24px;
}

.shortcuts-section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 8px;
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--el-fill-color-light);
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.shortcut-item:hover {
  background: var(--el-fill-color);
}

.shortcut-keys {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 120px;
}

.key {
  display: inline-block;
  padding: 2px 6px;
  background: #fff;
  border: 1px solid var(--el-border-color);
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  min-width: 24px;
  text-align: center;
}

.key + .key::before {
  content: '+';
  margin: 0 4px;
  color: var(--el-text-color-secondary);
  font-weight: normal;
}

.shortcut-description {
  flex: 1;
  text-align: right;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 滚动条样式 */
.shortcuts-container::-webkit-scrollbar {
  width: 6px;
}

.shortcuts-container::-webkit-scrollbar-track {
  background: var(--el-fill-color-light);
  border-radius: 3px;
}

.shortcuts-container::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

.shortcuts-container::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 12px;
  }
  
  .shortcut-keys {
    min-width: auto;
  }
  
  .shortcut-description {
    text-align: left;
    font-size: 13px;
  }
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
  .key {
    background: var(--el-fill-color-dark);
    border-color: var(--el-border-color-dark);
    color: var(--el-text-color-primary);
  }
}

/* 打印样式 */
@media print {
  .shortcuts-container {
    max-height: none;
    overflow: visible;
  }
  
  .shortcut-item {
    background: transparent;
    border: 1px solid #ddd;
    margin-bottom: 4px;
  }
  
  .key {
    border: 1px solid #000;
    background: #f5f5f5;
  }
}
</style> 