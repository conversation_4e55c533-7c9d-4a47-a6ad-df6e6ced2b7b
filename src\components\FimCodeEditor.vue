<template>
  <div class="fim-editor-container">
    <!-- 工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <el-button size="small" @click="formatJson" plain>
          <el-icon><Document /></el-icon> 格式化JSON
        </el-button>
        <el-button size="small" @click="clearContent" plain>
          <el-icon><Delete /></el-icon> 清空
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-tooltip content="输入时自动触发AI补全，按Tab接受" placement="top">
          <el-tag size="small" type="info">
            <el-icon><MagicStick /></el-icon> 实时补全
          </el-tag>
        </el-tooltip>
        <el-switch 
          v-model="autoCompletionEnabled" 
          size="small" 
          style="margin-left: 8px;"
          @change="handleAutoCompletionToggle"
        />
      </div>
    </div>

    <!-- 编辑器主体 -->
    <div class="editor-wrapper" :style="{ height: editorHeight + 'px' }">
      <!-- 行号 -->
      <div class="line-numbers">
        <div v-for="i in lineCount" :key="i" class="line-number">{{ i }}</div>
      </div>

      <!-- 文本编辑区域 -->
      <div class="editor-content">
        <div class="textarea-wrapper">
          <textarea
            ref="textareaRef"
            v-model="content"
            class="editor-textarea"
            :placeholder="placeholder"
            @input="handleInput"
            @keydown="handleKeydown"
            @click="handleClick"
            @scroll="handleScroll"
            spellcheck="false"
          ></textarea>
          
          <!-- 内联补全建议 -->
          <div 
            v-if="showInlineSuggestion && inlineSuggestion" 
            class="inline-suggestion-mirror"
            ref="suggestionOverlayRef"
          >
            <span class="mirror-text">{{ content }}</span><span class="suggestion-text">{{ inlineSuggestion }}</span>
          </div>
        </div>

        <!-- 补全建议弹窗 -->
        <div
          v-if="showCompletion"
          class="completion-popup"
          :style="completionStyle"
        >
          <div class="completion-header">
            <el-icon><MagicStick /></el-icon>
            <span>AI 代码补全</span>
            <el-button
              type="text"
              size="small"
              @click="hideCompletion"
              class="close-btn"
            >
              <el-icon><Close /></el-icon>
            </el-button>
          </div>
          
          <div class="completion-content">
            <div v-if="isLoading" class="loading-state">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>AI正在思考中...</span>
            </div>
            
            <div v-else-if="completionText" class="completion-text">
              <div class="completion-preview">
                <pre>{{ completionText }}</pre>
              </div>
              <div class="completion-actions">
                <el-button type="primary" size="small" @click="acceptCompletion">
                  <el-icon><Check /></el-icon> 接受 (Tab)
                </el-button>
                <el-button size="small" @click="hideCompletion">
                  <el-icon><Close /></el-icon> 拒绝 (Esc)
                </el-button>
              </div>
            </div>
            
            <div v-else class="error-state">
              <el-icon><Warning /></el-icon>
              <span>补全失败，请稍后重试</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <span>行: {{ currentLine }} 列: {{ currentColumn }}</span>
        <span class="separator">|</span>
        <span>字符数: {{ content.length }}</span>
      </div>
      <div class="status-right">
        <span v-if="autoCompletionEnabled && showInlineSuggestion" class="suggestion-hint">
          <el-icon><MagicStick /></el-icon>
          按Tab接受 | 按Esc拒绝
        </span>
        <span v-else-if="lastCompletionTime">
          上次补全: {{ formatTime(lastCompletionTime) }}
        </span>
        <span v-else-if="autoCompletionEnabled">
          实时补全已启用
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Document, Delete, MagicStick, Close, Loading, 
  Check, Warning 
} from '@element-plus/icons-vue'
import { getFimCompletion } from '@/api/fim'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '请输入内容，AI将自动提供补全建议...'
  },
  height: {
    type: Number,
    default: 300
  },
  maxTokens: {
    type: Number,
    default: 128
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'change'])

// Refs
const textareaRef = ref(null)
const suggestionOverlayRef = ref(null)

// 响应式数据
const content = ref(props.modelValue)
const editorHeight = ref(props.height)
const currentLine = ref(1)
const currentColumn = ref(1)
const lastCompletionTime = ref(null)

// FIM补全相关
const showCompletion = ref(false)
const isLoading = ref(false)
const completionText = ref('')
const completionPosition = ref({ top: 0, left: 0 })
const savedCursorPosition = ref(0)

// 实时补全相关
const autoCompletionEnabled = ref(true)
const inlineSuggestion = ref('')
const showInlineSuggestion = ref(false)
const debounceTimer = ref(null)
const isCompletingInline = ref(false)

// 计算属性
const lineCount = computed(() => {
  return Math.max(content.value.split('\n').length, 1)
})

const completionStyle = computed(() => ({
  top: completionPosition.value.top + 'px',
  left: completionPosition.value.left + 'px'
}))

const suggestionStyle = computed(() => ({
  top: completionPosition.value.top + 'px',
  left: completionPosition.value.left + 'px'
}))

// 监听器
watch(() => props.modelValue, (newValue) => {
  if (newValue !== content.value) {
    content.value = newValue
  }
})

watch(content, (newValue) => {
  emit('update:modelValue', newValue)
  emit('change', newValue)
  updateCursorPosition()
})

// 方法
const handleInput = () => {
  updateCursorPosition()
  
  // 清除之前的内联建议
  hideInlineSuggestion()
  
  // 如果启用了自动补全，设置防抖触发
  if (autoCompletionEnabled.value) {
    clearTimeout(debounceTimer.value)
    debounceTimer.value = setTimeout(() => {
      triggerInlineCompletion()
    }, 800) // 800ms防抖
  }
}

const handleClick = () => {
  updateCursorPosition()
  hideInlineSuggestion()
}

const handleScroll = () => {
  // 滚动时隐藏补全弹窗
  if (showCompletion.value) {
    hideCompletion()
  }
  
  // 更新内联建议位置
  if (showInlineSuggestion.value) {
    updateInlineSuggestionPosition()
  }
}

const handleKeydown = async (event) => {
  // Tab 接受内联补全建议
  if (event.key === 'Tab' && showInlineSuggestion.value && inlineSuggestion.value) {
    event.preventDefault()
    acceptInlineSuggestion()
    return
  }

  // Esc 拒绝内联补全建议
  if (event.key === 'Escape' && showInlineSuggestion.value) {
    event.preventDefault()
    hideInlineSuggestion()
    return
  }

  // Tab 接受弹窗补全（保留原有功能）
  if (event.key === 'Tab' && showCompletion.value && completionText.value) {
    event.preventDefault()
    acceptCompletion()
    return
  }

  // Esc 拒绝弹窗补全（保留原有功能）
  if (event.key === 'Escape' && showCompletion.value) {
    event.preventDefault()
    hideCompletion()
    return
  }

  // Ctrl + Space 手动触发弹窗补全（保留原有功能）
  if (event.ctrlKey && event.code === 'Space') {
    event.preventDefault()
    await triggerCompletion()
    return
  }

  // 其他按键时隐藏所有补全
  if (!['Tab', 'Escape', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
    hideInlineSuggestion()
    hideCompletion()
  }
}

const updateCursorPosition = () => {
  const textarea = textareaRef.value
  if (!textarea) return

  const cursorPos = textarea.selectionStart
  const textBeforeCursor = content.value.substring(0, cursorPos)
  const lines = textBeforeCursor.split('\n')
  
  currentLine.value = lines.length
  currentColumn.value = lines[lines.length - 1].length + 1
}

const getCursorCoordinates = () => {
  const textarea = textareaRef.value
  if (!textarea) return { top: 0, left: 0 }

  // 创建一个临时div来计算光标位置
  const div = document.createElement('div')
  const computedStyle = window.getComputedStyle(textarea)
  
  // 复制样式
  div.style.position = 'absolute'
  div.style.visibility = 'hidden'
  div.style.whiteSpace = 'pre-wrap'
  div.style.wordWrap = 'break-word'
  div.style.font = computedStyle.font
  div.style.padding = computedStyle.padding
  div.style.border = computedStyle.border
  div.style.width = computedStyle.width
  div.style.height = computedStyle.height
  
  document.body.appendChild(div)
  
  const cursorPos = textarea.selectionStart
  const textBeforeCursor = content.value.substring(0, cursorPos)
  
  div.textContent = textBeforeCursor
  
  // 添加一个span来标记光标位置
  const span = document.createElement('span')
  span.textContent = '|'
  div.appendChild(span)
  
  const rect = textarea.getBoundingClientRect()
  const spanRect = span.getBoundingClientRect()
  
  document.body.removeChild(div)
  
  return {
    top: spanRect.top - rect.top + textarea.scrollTop + 20,
    left: spanRect.left - rect.left
  }
}

const triggerCompletion = async () => {
  const textarea = textareaRef.value
  if (!textarea) return

  const cursorPos = textarea.selectionStart
  savedCursorPosition.value = cursorPos
  
  // 获取前缀和后缀
  const prefix = content.value.substring(0, cursorPos)
  const suffix = content.value.substring(cursorPos)
  
  // 如果前缀为空，不触发补全
  if (!prefix.trim()) {
    ElMessage.info('请先输入一些内容再触发补全')
    return
  }

  // 显示补全弹窗
  showCompletion.value = true
  isLoading.value = true
  completionText.value = ''
  
  // 计算弹窗位置
  completionPosition.value = getCursorCoordinates()

  try {
    const response = await getFimCompletion({
      prefix,
      suffix,
      maxTokens: props.maxTokens
    })

    if (response.data.code === 200) {
      completionText.value = response.data.data.completion || ''
      lastCompletionTime.value = new Date()
      
      if (!completionText.value.trim()) {
        ElMessage.info('AI暂时没有补全建议')
        hideCompletion()
      }
    } else {
      throw new Error(response.data.message || '补全请求失败')
    }
  } catch (error) {
    console.error('FIM补全失败:', error)
    ElMessage.error('AI补全失败: ' + (error.message || '网络错误'))
    hideCompletion()
  } finally {
    isLoading.value = false
  }
}

const acceptCompletion = () => {
  if (!completionText.value) return

  const textarea = textareaRef.value
  if (!textarea) return

  // 使用智能合并补全内容
  const result = smartMergeCompletion(
    content.value,
    savedCursorPosition.value,
    completionText.value
  )
  
  console.log('弹窗补全智能合并结果:', result)
  
  content.value = result.newContent
  
  // 设置新的光标位置
  nextTick(() => {
    textarea.setSelectionRange(result.newCursorPos, result.newCursorPos)
    textarea.focus()
    updateCursorPosition()
  })

  hideCompletion()
  ElMessage.success('已接受AI补全建议')
}

const hideCompletion = () => {
  showCompletion.value = false
  isLoading.value = false
  completionText.value = ''
}

// 内联补全相关方法
const triggerInlineCompletion = async () => {
  if (isCompletingInline.value) return
  
  const textarea = textareaRef.value
  if (!textarea) return

  const cursorPos = textarea.selectionStart
  const prefix = content.value.substring(0, cursorPos)
  const suffix = content.value.substring(cursorPos)
  
  // 如果前缀太短或为空，不触发补全
  if (!prefix.trim() || prefix.trim().length < 2) {
    return
  }

  isCompletingInline.value = true

  try {
    const response = await getFimCompletion({
      prefix,
      suffix,
      maxTokens: props.maxTokens
    })

    if (response.data.code === 200) {
      const completion = response.data.data.completion || ''
      
      if (completion.trim()) {
        inlineSuggestion.value = completion
        showInlineSuggestion.value = true
        savedCursorPosition.value = cursorPos
        lastCompletionTime.value = new Date()
        
        console.log('FIM补全成功，内容长度:', completion.length)
        
        // 更新建议位置
        await nextTick()
        updateInlineSuggestionPosition()
      }
    } else {
      console.log('补全请求失败，状态码:', response.data.code)
    }
  } catch (error) {
    console.error('内联补全失败:', error)
  } finally {
    isCompletingInline.value = false
  }
}

const acceptInlineSuggestion = () => {
  if (!inlineSuggestion.value) return

  const textarea = textareaRef.value
  if (!textarea) return

  // 智能合并补全内容
  const result = smartMergeCompletion(
    content.value,
    savedCursorPosition.value,
    inlineSuggestion.value
  )
  
  console.log('智能合并结果:', result)
  
  content.value = result.newContent
  
  // 设置新的光标位置
  nextTick(() => {
    textarea.setSelectionRange(result.newCursorPos, result.newCursorPos)
    textarea.focus()
    updateCursorPosition()
  })

  hideInlineSuggestion()
  ElMessage.success('已接受AI建议')
}

const hideInlineSuggestion = () => {
  showInlineSuggestion.value = false
  inlineSuggestion.value = ''
  clearTimeout(debounceTimer.value)
}



const smartMergeCompletion = (currentContent, cursorPos, completion) => {
  const beforeCursor = currentContent.substring(0, cursorPos)
  const afterCursor = currentContent.substring(cursorPos)
  
  console.log('合并前分析:')
  console.log('- 当前完整内容:', currentContent)
  console.log('- 光标位置:', cursorPos)
  console.log('- 光标前内容:', beforeCursor)
  console.log('- 光标后内容:', afterCursor) 
  console.log('- 补全内容:', completion)
  
  // 超级智能合并策略：尝试多种重叠检测方式
  
  // 策略1：检查前缀重叠（光标前内容的后缀与补全内容的前缀）
  let frontOverlap = 0
  const maxFrontOverlap = Math.min(beforeCursor.length, completion.length)
  for (let i = maxFrontOverlap; i > 0; i--) {
    const suffixOfBefore = beforeCursor.slice(-i)
    const prefixOfCompletion = completion.slice(0, i)
    if (suffixOfBefore === prefixOfCompletion) {
      frontOverlap = i
      break
    }
  }
  
  // 策略2：检查后缀重叠（补全内容的前缀与光标后内容的前缀）  
  let backOverlap = 0
  const maxBackOverlap = Math.min(completion.length, afterCursor.length)
  for (let i = maxBackOverlap; i > 0; i--) {
    const prefixOfCompletion = completion.slice(0, i)
    const prefixOfAfter = afterCursor.slice(0, i)
    if (prefixOfCompletion === prefixOfAfter) {
      backOverlap = i
      break
    }
  }
  
  // 策略3：检查是否补全内容包含了光标后的内容（完全包含策略）
  let containsAfter = false
  let containsLength = 0
  if (afterCursor.length > 0 && completion.includes(afterCursor)) {
    const index = completion.indexOf(afterCursor)
    if (index >= 0) {
      containsAfter = true
      containsLength = afterCursor.length
      console.log('- 补全内容包含光标后内容，位置:', index, '长度:', containsLength)
    }
  }
  
  // 策略4：JSON对象补全特殊处理
  let isJsonObjectCompletion = false
  if (beforeCursor.endsWith('{"') && afterCursor.match(/^[^"]*"\}?/)) {
    // 检测到JSON对象开始模式：{"  +  key"}
    const keyMatch = afterCursor.match(/^([^"]*)"/)
    if (keyMatch && completion.startsWith(keyMatch[1])) {
      isJsonObjectCompletion = true
      console.log('- 检测到JSON对象补全模式，key:', keyMatch[1])
    }
  }
  
  console.log('- 前缀重叠长度:', frontOverlap)
  console.log('- 后缀重叠长度:', backOverlap)
  console.log('- 是否包含后内容:', containsAfter)
  console.log('- JSON对象补全模式:', isJsonObjectCompletion)
  
  let newContent
  let newCursorPos
  
  // 智能合并策略（按优先级）
  if (isJsonObjectCompletion) {
    // 特殊策略：JSON对象补全，智能处理结构
    const needsClosingQuote = !completion.endsWith('"')
    const needsClosingBrace = afterCursor.includes('}')
    
    let suffix = ''
    if (needsClosingQuote) suffix += '"'
    if (needsClosingBrace) suffix += '}'
    else if (!suffix) suffix = '}'
    
    newContent = beforeCursor + completion + suffix
    newCursorPos = beforeCursor.length + completion.length + suffix.length
    console.log('- 使用JSON对象补全策略，添加后缀:', suffix)
  } else if (containsAfter) {
    // 最优策略：补全内容包含光标后内容，直接替换
    newContent = beforeCursor + completion
    newCursorPos = beforeCursor.length + completion.length
    console.log('- 使用包含策略，直接替换光标后内容')
  } else if (frontOverlap > 0 && backOverlap > 0) {
    // 双向重叠：去掉前后重叠部分
    const uniquePart = completion.slice(frontOverlap, -backOverlap)
    const newAfterCursor = afterCursor.slice(backOverlap)
    newContent = beforeCursor + uniquePart + newAfterCursor
    newCursorPos = cursorPos + uniquePart.length
    console.log('- 双向重叠，唯一部分:', uniquePart)
  } else if (frontOverlap > 0) {
    // 前缀重叠：去掉前缀重叠部分
    const uniquePart = completion.slice(frontOverlap)
    newContent = beforeCursor + uniquePart + afterCursor
    newCursorPos = cursorPos + uniquePart.length
    console.log('- 前缀重叠，唯一部分:', uniquePart)
  } else if (backOverlap > 0) {
    // 后缀重叠：去掉补全内容前面重叠的部分，替换光标后重叠的内容
    const uniquePart = completion.slice(backOverlap)
    const newAfterCursor = afterCursor.slice(backOverlap)
    newContent = beforeCursor + uniquePart + newAfterCursor
    newCursorPos = cursorPos + uniquePart.length
    console.log('- 后缀重叠，去掉前', backOverlap, '个字符，唯一部分:', uniquePart)
  } else {
    // 无重叠：直接插入
    newContent = beforeCursor + completion + afterCursor
    newCursorPos = cursorPos + completion.length
    console.log('- 无重叠，直接插入')
  }
  
  // 特殊处理：修复JSON格式问题
  newContent = fixJsonFormat(newContent)
  
  console.log('- 最终内容:', newContent)
  
  return {
    newContent,
    newCursorPos
  }
}

const fixJsonFormat = (content) => {
  try {
    // 尝试解析并重新格式化JSON，修复可能的格式问题
    if (content.trim().startsWith('{') || content.trim().startsWith('[')) {
      // 处理不完整的JSON
      let fixedContent = content
      
      // 修复常见的JSON格式问题
      // 1. 确保字符串都有结束引号
      if (fixedContent.includes('"') && !fixedContent.endsWith('"') && !fixedContent.endsWith('}')) {
        const lastQuoteIndex = fixedContent.lastIndexOf('"')
        const afterLastQuote = fixedContent.substring(lastQuoteIndex + 1)
        if (!afterLastQuote.includes('"')) {
          fixedContent += '"'
        }
      }
      
      // 2. 确保对象有结束括号
      const openBraces = (fixedContent.match(/\{/g) || []).length
      const closeBraces = (fixedContent.match(/\}/g) || []).length
      if (openBraces > closeBraces) {
        fixedContent += '}'
      }
      
      console.log('JSON格式修复:', content, '->', fixedContent)
      return fixedContent
    }
  } catch (error) {
    console.log('JSON格式修复失败，返回原内容:', error)
  }
  
  return content
}

const updateInlineSuggestionPosition = () => {
  const textarea = textareaRef.value
  const overlay = suggestionOverlayRef.value
  if (!textarea || !overlay) return

  // 同步textarea的滚动位置和样式
  overlay.scrollTop = textarea.scrollTop
  overlay.scrollLeft = textarea.scrollLeft
}

const handleAutoCompletionToggle = (enabled) => {
  if (!enabled) {
    hideInlineSuggestion()
    clearTimeout(debounceTimer.value)
  }
}

const formatJson = () => {
  try {
    if (!content.value.trim()) {
      ElMessage.warning('请先输入JSON内容')
      return
    }

    const parsed = JSON.parse(content.value)
    content.value = JSON.stringify(parsed, null, 2)
    ElMessage.success('JSON格式化成功')
  } catch (error) {
    ElMessage.error('JSON格式错误: ' + error.message)
  }
}

const clearContent = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有内容吗？', '清空确认', {
      type: 'warning'
    })
    content.value = ''
    hideCompletion()
  } catch {
    // 用户取消
  }
}

const formatTime = (date) => {
  return date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 生命周期
onMounted(() => {
  updateCursorPosition()
})

onUnmounted(() => {
  hideCompletion()
  hideInlineSuggestion()
  clearTimeout(debounceTimer.value)
})
</script>

<style scoped>
.fim-editor-container {
  display: flex;
  flex-direction: column;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  overflow: hidden;
  background: #fff;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.editor-wrapper {
  position: relative;
  display: flex;
  overflow: hidden;
}

.line-numbers {
  width: 50px;
  background: #f8f9fa;
  border-right: 1px solid #e4e7ed;
  padding: 8px 0;
  text-align: center;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: #909399;
  user-select: none;
  overflow: hidden;
}

.line-number {
  height: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.editor-content {
  flex: 1;
  position: relative;
}

.textarea-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
}

.editor-textarea {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  resize: none;
  padding: 8px 12px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  background: transparent;
  color: #303133;
  position: relative;
  z-index: 2;
}

/* 内联补全建议样式 */
.inline-suggestion-mirror {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  padding: 8px 12px;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  background: transparent;
  pointer-events: none;
  z-index: 1;
  overflow: hidden;
}

.mirror-text {
  color: transparent;
  white-space: pre-wrap;
}

.suggestion-text {
  color: #999;
  opacity: 0.6;
  font-style: italic;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 2px;
  padding: 0 2px;
}

.completion-popup {
  position: absolute;
  z-index: 1000;
  min-width: 300px;
  max-width: 500px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.completion-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 13px;
  font-weight: 500;
}

.completion-header .el-icon {
  margin-right: 6px;
}

.close-btn {
  margin-left: auto;
  color: white;
}

.completion-content {
  padding: 12px;
}

.loading-state,
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #909399;
}

.loading-state .el-icon {
  margin-right: 8px;
  font-size: 16px;
}

.completion-text {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.completion-preview {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 8px;
  max-height: 150px;
  overflow-y: auto;
}

.completion-preview pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-break: break-all;
}

.completion-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  background: #f5f7fa;
  border-top: 1px solid #e4e7ed;
  font-size: 12px;
  color: #909399;
}

.separator {
  margin: 0 8px;
}

.status-right {
  font-style: italic;
}

.suggestion-hint {
  color: #409eff;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .completion-popup {
    min-width: 250px;
    max-width: calc(100vw - 40px);
  }
  
  .completion-actions {
    flex-direction: column;
  }
  
  .completion-actions .el-button {
    width: 100%;
  }
}
</style> 