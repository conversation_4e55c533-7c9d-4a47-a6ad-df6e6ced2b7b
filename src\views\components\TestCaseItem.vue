<template>
  <div 
    class="case-item"
    :class="{ 
      'selected': selected,
      'dragging': isDragging,
      'drag-over': isDragOver
    }"
    draggable="true"
    @dragstart="handleDragStart"
    @dragend="handleDragEnd"
    @dragover="handleDragOver"
    @dragleave="handleDragLeave"
    @drop="handleDrop"
    @click="handleClick"
    @dblclick="handleDoubleClick"
  >
    <!-- 选择框 -->
    <div v-if="selectMode" class="select-checkbox">
      <el-checkbox 
        :model-value="selected"
        @change="handleSelect"
        @click.stop
      />
    </div>
    
    <!-- 状态指示器 -->
    <div class="status-indicator">
      <el-icon 
        v-if="testCase.status === 'pass'"
        class="status-icon success"
      >
        <CircleCheck />
      </el-icon>
      <el-icon 
        v-else-if="testCase.status === 'fail'"
        class="status-icon error"
      >
        <CircleClose />
      </el-icon>
      <el-icon 
        v-else
        class="status-icon pending"
      >
        <Clock />
      </el-icon>
    </div>
    
    <!-- 主要内容 -->
    <div class="case-content">
      <div class="case-header">
        <el-tooltip 
          :content="testCase.title" 
          placement="top" 
          :disabled="!isTextOverflow"
          :show-after="500"
        >
          <span class="case-title" ref="titleRef">
            {{ testCase.title }}
          </span>
        </el-tooltip>
        <div class="case-tags">
          <el-tag 
            size="small" 
            :type="getMethodType(testCase.method)"
            class="method-tag"
          >
            {{ testCase.method }}
          </el-tag>
          <el-tag 
            v-if="testCase.priority"
            size="small" 
            :type="getPriorityType(testCase.priority)"
            class="priority-tag"
          >
            {{ getPriorityText(testCase.priority) }}
          </el-tag>
          <el-tag 
            v-if="testCase.status"
            size="small" 
            :type="getStatusType(testCase.status)"
            class="status-tag"
          >
            {{ getStatusText(testCase.status) }}
          </el-tag>
        </div>
      </div>
      
      <el-tooltip 
        :content="testCase.api_path" 
        placement="top" 
        :show-after="500"
      >
        <div class="case-path">
          {{ testCase.api_path }}
        </div>
      </el-tooltip>
      
      <div v-if="testCase.description" class="case-description">
        {{ testCase.description }}
      </div>
      
      <div class="case-meta">
        <span class="meta-item">
          <el-icon><User /></el-icon>
          {{ testCase.creator || '未知' }}
        </span>
        <span class="meta-item">
          <el-icon><Clock /></el-icon>
          {{ formatTime(testCase.update_time) }}
        </span>
        <span v-if="testCase.execution_count" class="meta-item">
          <el-icon><Position /></el-icon>
          执行 {{ testCase.execution_count }} 次
        </span>
      </div>
    </div>
    
    <!-- 操作按钮 -->
    <div class="case-actions">
      <el-dropdown 
        trigger="click"
        @command="handleAction"
        @click.stop
      >
        <el-button 
          link 
          :icon="More"
          class="action-button"
        />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="edit">
              <el-icon><Edit /></el-icon>
              编辑用例
            </el-dropdown-item>
            <el-dropdown-item command="copy">
              <el-icon><CopyDocument /></el-icon>
              复制用例
            </el-dropdown-item>
            <el-dropdown-item command="run">
              <el-icon><VideoPlay /></el-icon>
              单独执行
            </el-dropdown-item>
            <el-dropdown-item command="view-result">
              <el-icon><View /></el-icon>
              查看结果
            </el-dropdown-item>
            <el-dropdown-item command="delete" divided>
              <el-icon><Delete /></el-icon>
              删除用例
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    
    <!-- 拖拽预览 -->
    <div v-if="isDragging" class="drag-preview">
      <el-icon><Rank /></el-icon>
      拖拽到脑图中添加节点
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  CircleCheck,
  CircleClose,
  Clock,
  User,
  Position,
  More,
  Edit,
  CopyDocument,
  VideoPlay,
  View,
  Delete,
  Rank
} from '@element-plus/icons-vue';

const props = defineProps({
  testCase: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    default: false
  },
  selectMode: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits([
  'select',
  'drag-start',
  'drag-end',
  'double-click',
  'action'
]);

// 响应式数据
const isDragging = ref(false);
const isDragOver = ref(false);
const titleRef = ref(null);
const isTextOverflow = ref(false);

// 计算属性
const getMethodType = (method) => {
  const types = {
    'GET': 'success',
    'POST': 'primary',
    'PUT': 'warning',
    'DELETE': 'danger',
    'PATCH': 'info',
    'HEAD': 'info',
    'OPTIONS': 'info'
  };
  return types[method] || 'info';
};

const getPriorityType = (priority) => {
  const types = {
    'high': 'danger',
    'medium': 'warning', 
    'low': 'info',
    '高': 'danger',
    '中': 'warning',
    '低': 'info'
  };
  return types[priority] || 'info';
};

const getPriorityText = (priority) => {
  const texts = {
    'high': '高',
    'medium': '中',
    'low': '低',
    '高': '高',
    '中': '中',
    '低': '低'
  };
  return texts[priority] || priority;
};

const getStatusType = (status) => {
  const types = {
    'pass': 'success',
    'fail': 'danger',
    'pending': 'info',
    'success': 'success',
    'error': 'danger',
    'executing': 'warning',
    '未执行': 'info',
    '成功': 'success',
    '失败': 'danger',
    '执行中': 'warning'
  };
  return types[status] || 'info';
};

const getStatusText = (status) => {
  const texts = {
    'pass': '通过',
    'fail': '失败',
    'pending': '未执行',
    'success': '成功',
    'error': '失败',
    'executing': '执行中',
    '未执行': '未执行',
    '成功': '成功',
    '失败': '失败',
    '执行中': '执行中'
  };
  return texts[status] || status;
};

const formatTime = (timeString) => {
  if (!timeString) return '';
  
  const date = new Date(timeString);
  const now = new Date();
  const diff = now - date;
  
  // 小于1小时显示分钟
  if (diff < 3600000) {
    const minutes = Math.floor(diff / 60000);
    return `${minutes}分钟前`;
  }
  
  // 小于1天显示小时
  if (diff < 86400000) {
    const hours = Math.floor(diff / 3600000);
    return `${hours}小时前`;
  }
  
  // 小于7天显示天数
  if (diff < 604800000) {
    const days = Math.floor(diff / 86400000);
    return `${days}天前`;
  }
  
  // 否则显示日期
  return date.toLocaleDateString();
};

// 方法
const handleSelect = (value) => {
  emit('select', props.testCase.id, value);
};

const handleClick = () => {
  if (props.selectMode) {
    handleSelect(!props.selected);
  }
};

const handleDoubleClick = () => {
  // 不再 emit('case-dragged', ...) 只做UI清理
};

const handleDragStart = (event) => {
  if (props.selectMode) {
    event.preventDefault();
    return;
  }
  
  isDragging.value = true;
  
  // 设置拖拽数据
  event.dataTransfer.effectAllowed = 'copy';
  event.dataTransfer.setData('text/plain', JSON.stringify({
    type: 'test-case',
    data: props.testCase
  }));
  
  // 创建自定义拖拽图像
  const dragImage = document.createElement('div');
  dragImage.className = 'custom-drag-image';
  dragImage.innerHTML = `
    <div class="drag-image-content">
      <div class="drag-image-header">
        <el-tag size="small" :type="getMethodType(props.testCase.method)">
          ${props.testCase.method}
        </el-tag>
        <span class="drag-image-title">${props.testCase.title}</span>
      </div>
      <div class="drag-image-path">${props.testCase.api_path}</div>
    </div>
  `;
  
  // 设置拖拽图像样式
  dragImage.style.cssText = `
    position: absolute;
    top: -1000px;
    left: -1000px;
    background: white;
    border: 2px solid #1890ff;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 8px 24px rgba(0,0,0,0.15);
    opacity: 0.9;
    transform: rotate(5deg);
    z-index: 10000;
    pointer-events: none;
    min-width: 200px;
  `;
  
  document.body.appendChild(dragImage);
  event.dataTransfer.setDragImage(dragImage, 100, 20);
  
  // 清理拖拽图像
  setTimeout(() => {
    if (document.body.contains(dragImage)) {
      document.body.removeChild(dragImage);
    }
  }, 0);
  
  emit('drag-start', props.testCase);
};

const handleDragEnd = (event) => {
  isDragging.value = false;
  // 只做UI清理，不做任何数据变更
};

const handleDragOver = (event) => {
  event.preventDefault();
  event.dataTransfer.dropEffect = 'copy';
  isDragOver.value = true;
};

const handleDragLeave = () => {
  isDragOver.value = false;
};

const handleDrop = (event) => {
  event.preventDefault();
  isDragOver.value = false;
  
  try {
    const data = JSON.parse(event.dataTransfer.getData('text/plain'));
    if (data.type === 'test-case') {
      // 处理测试用例拖拽到另一个测试用例上的逻辑
      console.log('Test case dropped:', data.data);
    }
  } catch (error) {
    console.error('Invalid drag data:', error);
  }
};

const handleAction = (command) => {
  emit('action', command, props.testCase);
  
  switch (command) {
    case 'edit':
      ElMessage.info('编辑功能待实现');
      break;
    case 'copy':
      ElMessage.success('用例已复制');
      break;
    case 'run':
      ElMessage.info('正在执行测试用例...');
      break;
    case 'view-result':
      ElMessage.info('查看结果功能待实现');
      break;
    case 'delete':
      ElMessage.warning('删除功能待实现');
      break;
  }
};

// 检测文本是否溢出
const checkTextOverflow = () => {
  nextTick(() => {
    if (titleRef.value) {
      const element = titleRef.value;
      isTextOverflow.value = element.scrollHeight > element.clientHeight;
    }
  });
};

// 生命周期
onMounted(() => {
  checkTextOverflow();
});
</script>

<style scoped>
.case-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border: 1px solid rgba(0,0,0,0.06);
  border-radius: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 
    0 2px 8px rgba(0,0,0,0.04),
    0 1px 2px rgba(0,0,0,0.02);
}

.case-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255,255,255,0.1) 0%, 
    rgba(255,255,255,0.05) 50%, 
    rgba(0,0,0,0.01) 100%);
  pointer-events: none;
}

.case-item:hover {
  border-color: #3b82f6;
  box-shadow: 
    0 8px 24px rgba(59, 130, 246, 0.15),
    0 4px 8px rgba(59, 130, 246, 0.1);
  transform: translateY(-3px);
  background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
}

.case-item.selected {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  box-shadow: 
    0 8px 24px rgba(59, 130, 246, 0.2),
    0 4px 8px rgba(59, 130, 246, 0.15);
  transform: translateY(-2px);
}

.case-item.dragging {
  opacity: 0.7;
  transform: rotate(5deg) scale(0.95) translateY(-8px);
  z-index: 1000;
  box-shadow: 
    0 16px 32px rgba(0, 0, 0, 0.2),
    0 8px 16px rgba(0, 0, 0, 0.15);
}

.case-item.drag-over {
  border-color: #10b981;
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  box-shadow: 
    0 8px 24px rgba(16, 185, 129, 0.2),
    0 4px 8px rgba(16, 185, 129, 0.15);
}

.select-checkbox {
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.status-indicator {
  flex-shrink: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.status-icon {
  font-size: 16px;
}

.status-icon.success {
  color: var(--el-color-success);
}

.status-icon.error {
  color: var(--el-color-error);
}

.status-icon.pending {
  color: var(--el-color-info);
}

.case-content {
  flex: 1;
  min-width: 0;
  position: relative;
  z-index: 1;
}

.case-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.case-title {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 14px;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 1.4;
  flex: 1;
  margin-right: 8px;
  max-height: 2.8em; /* 限制最大高度为2行 */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.case-tags {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.method-tag,
.priority-tag,
.status-tag {
  font-size: 12px;
  height: 20px;
  line-height: 18px;
}

.case-path {
  font-size: 12px;
  color: var(--el-text-color-regular);
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 1.3;
  margin-bottom: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  background: var(--el-fill-color-light);
  padding: 4px 6px;
  border-radius: 3px;
  max-height: 2.6em; /* 限制最大高度为2行 */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.case-description {
  font-size: 12px;
  color: var(--el-text-color-regular);
  margin-bottom: 4px;
  word-wrap: break-word;
  word-break: break-all;
  white-space: normal;
  line-height: 1.3;
  max-height: 2.6em; /* 限制最大高度为2行 */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.case-meta {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  color: var(--el-text-color-secondary);
}

.meta-item .el-icon {
  font-size: 12px;
}

.case-actions {
  flex-shrink: 0;
  opacity: 0;
  transition: opacity 0.3s ease;
  position: relative;
  z-index: 1;
}

.case-item:hover .case-actions {
  opacity: 1;
}

.action-button {
  padding: 4px;
  border-radius: 4px;
}

.action-button:hover {
  background: var(--el-fill-color);
}

.drag-preview {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(59, 130, 246, 0.1) 0%, 
    rgba(59, 130, 246, 0.15) 100%);
  border: 2px dashed #3b82f6;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 15px;
  color: #3b82f6;
  font-weight: 600;
  z-index: 10;
  backdrop-filter: blur(4px);
}

.drag-preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, 
    transparent 40%, 
    rgba(255,255,255,0.2) 50%, 
    transparent 60%);
  animation: shimmer 2s infinite;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .case-item {
    padding: 8px;
    gap: 8px;
  }
  
  .case-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .case-title {
    margin-right: 0;
    margin-bottom: 4px;
  }
  
  .case-tags {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 4px;
  }
  
  .case-meta {
    flex-direction: column;
    gap: 4px;
  }
  
  .case-actions {
    opacity: 1;
  }
}

/* 动画效果 */
@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

.case-item.error {
  animation: shake 0.5s ease-in-out;
}

/* 特殊状态样式 */
.case-item[data-status="executing"] {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  box-shadow: 
    0 8px 24px rgba(245, 158, 11, 0.2),
    0 4px 8px rgba(245, 158, 11, 0.15);
}

.case-item[data-status="executing"]::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg, 
    transparent, 
    rgba(255, 255, 255, 0.4), 
    transparent
  );
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 自定义拖拽图像样式 */
.custom-drag-image {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.drag-image-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.drag-image-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.drag-image-title {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.drag-image-path {
  color: #666;
  font-size: 12px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  word-break: break-all;
}
</style> 