<template>
  <div class="execution-result-viewer">
    <div v-if="!result" class="empty-state">
      <el-empty description="暂无执行结果" />
    </div>
    
    <div v-else class="result-content">
      <!-- 基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag :type="result.success ? 'success' : 'danger'">
              {{ result.success ? '成功' : '失败' }}
            </el-tag>
          </div>
        </template>
        
        <div class="info-grid">
          <div class="info-item">
            <label>执行时间:</label>
            <span>{{ formatTime(result.executionTime) }}</span>
          </div>
          <div class="info-item">
            <label>响应时间:</label>
            <span>{{ result.responseTime || 0 }}ms</span>
          </div>
          <div class="info-item">
            <label>状态码:</label>
            <span :class="getStatusClass(result.statusCode)">
              {{ result.statusCode || 'N/A' }}
            </span>
          </div>
          <div class="info-item">
            <label>请求URL:</label>
            <span class="url">{{ result.url || 'N/A' }}</span>
          </div>
        </div>
      </el-card>

      <!-- 请求响应详情 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <span>详细信息</span>
        </template>
        
        <el-tabs v-model="activeTab" class="result-tabs">
          <el-tab-pane label="响应内容" name="response">
            <div class="content-area">
              <pre v-if="result.response" class="json-content">{{ formatJson(result.response) }}</pre>
              <div v-else class="empty-tip">无响应内容</div>
            </div>
          </el-tab-pane>
          
          <el-tab-pane label="错误信息" name="error" v-if="result.error">
            <div class="error-content">
              <div class="error-message">
                <el-icon class="error-icon">
                  <WarningFilled />
                </el-icon>
                <span>{{ result.error.message || result.error }}</span>
              </div>
              
              <div v-if="result.error.stack" class="error-stack">
                <el-collapse>
                  <el-collapse-item title="错误堆栈" name="stack">
                    <pre class="stack-trace">{{ result.error.stack }}</pre>
                  </el-collapse-item>
                </el-collapse>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { WarningFilled } from '@element-plus/icons-vue';

const props = defineProps({
  result: {
    type: Object,
    default: null
  }
});

const activeTab = ref('response');

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-';
  const date = new Date(timestamp);
  return date.toLocaleString();
};

// 获取状态码样式
const getStatusClass = (statusCode) => {
  if (!statusCode) return 'status-unknown';
  if (statusCode >= 200 && statusCode < 300) {
    return 'status-success';
  } else if (statusCode >= 400 && statusCode < 500) {
    return 'status-client-error';
  } else if (statusCode >= 500) {
    return 'status-server-error';
  }
  return 'status-other';
};

// 格式化JSON
const formatJson = (data) => {
  if (!data) return '';
  if (typeof data === 'string') {
    try {
      return JSON.stringify(JSON.parse(data), null, 2);
    } catch {
      return data;
    }
  }
  return JSON.stringify(data, null, 2);
};
</script>

<style scoped>
.execution-result-viewer {
  height: 100%;
  overflow-y: auto;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
}

.info-card,
.detail-card {
  border: 1px solid var(--el-border-color-lighter);
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 500;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-item label {
  font-weight: 500;
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

.info-item span {
  color: var(--el-text-color-primary);
  word-break: break-all;
}

.url {
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
}

.status-success {
  color: var(--el-color-success);
  font-weight: 500;
}

.status-client-error {
  color: var(--el-color-warning);
  font-weight: 500;
}

.status-server-error {
  color: var(--el-color-danger);
  font-weight: 500;
}

.status-unknown,
.status-other {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.result-tabs {
  margin-top: -8px;
}

.content-area {
  max-height: 300px;
  overflow-y: auto;
}

.json-content {
  background: var(--el-fill-color-light);
  padding: 16px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
  line-height: 1.5;
  color: var(--el-text-color-primary);
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
}

.empty-tip {
  color: var(--el-text-color-secondary);
  text-align: center;
  padding: 20px;
  font-size: 14px;
}

.error-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: var(--el-color-danger-light-9);
  border-radius: 4px;
  border: 1px solid var(--el-color-danger-light-7);
  color: var(--el-color-danger);
}

.error-icon {
  color: var(--el-color-danger);
}

.stack-trace {
  background: var(--el-fill-color-light);
  padding: 12px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 11px;
  line-height: 1.4;
  color: var(--el-text-color-primary);
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
  margin: 0;
}

/* 滚动条样式 */
:deep(.el-scrollbar__bar) {
  opacity: 0.3;
}

:deep(.el-scrollbar__bar:hover) {
  opacity: 0.8;
}

/* 标签页样式 */
:deep(.el-tabs__header) {
  margin-bottom: 12px;
}

:deep(.el-tabs__content) {
  padding: 0;
}

/* 卡片样式 */
:deep(.el-card__header) {
  padding: 12px 16px;
  background: var(--el-fill-color-lighter);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-card__body) {
  padding: 16px;
}

/* 折叠面板样式 */
:deep(.el-collapse-item__header) {
  background: var(--el-fill-color-light);
  border-bottom: 1px solid var(--el-border-color-lighter);
}

:deep(.el-collapse-item__content) {
  padding: 12px;
}
</style> 