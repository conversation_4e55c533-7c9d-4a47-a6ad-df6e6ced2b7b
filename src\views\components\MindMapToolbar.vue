<template>
  <div class="toolbar">
    <div class="left-tools">
      <!-- 文件操作 -->
      <el-button-group>
        <el-tooltip content="保存脑图 (Ctrl+S)" placement="bottom">
          <el-button 
            type="primary" 
            @click="$emit('save-map')"
            :icon="Save"
          >
            <span class="button-text">保存脑图</span>
          </el-button>
        </el-tooltip>
        <el-tooltip content="导出脑图" placement="bottom">
          <el-button 
            @click="$emit('export-map')"
            :icon="Download"
          >
            <span class="button-text">导出</span>
          </el-button>
        </el-tooltip>
      </el-button-group>

      <el-divider direction="vertical" />

      <!-- 编辑操作 -->
      <el-button-group>
        <el-tooltip content="撤销 (Ctrl+Z)" placement="bottom">
          <el-button 
            @click="$emit('undo')"
            :icon="RefreshLeft"
            :disabled="!canUndo"
          >
            <span class="button-text">撤销</span>
          </el-button>
        </el-tooltip>
        <el-tooltip content="重做 (Ctrl+Y)" placement="bottom">
          <el-button 
            @click="$emit('redo')"
            :icon="RefreshRight"
            :disabled="!canRedo"
          >
            <span class="button-text">重做</span>
          </el-button>
        </el-tooltip>
      </el-button-group>

      <el-divider direction="vertical" />

      <!-- 节点操作 -->
      <el-button-group>
        <el-tooltip content="添加子节点" placement="bottom">
          <el-button 
            @click="$emit('add-node')"
            :icon="Plus"
            :disabled="!selectedNode"
          >
            <span class="button-text">添加子节点</span>
          </el-button>
        </el-tooltip>
        <el-tooltip content="复制节点 (Ctrl+C)" placement="bottom">
          <el-button 
            @click="$emit('copy-node')"
            :icon="CopyDocument"
            :disabled="!selectedNode"
          >
            <span class="button-text">复制</span>
          </el-button>
        </el-tooltip>
        <el-tooltip content="粘贴节点 (Ctrl+V)" placement="bottom">
          <el-button 
            @click="$emit('paste-node')"
            :icon="Document"
            :disabled="!selectedNode || !hasClipboard"
          >
            <span class="button-text">粘贴</span>
          </el-button>
        </el-tooltip>
        <el-tooltip content="删除节点 (Delete)" placement="bottom">
          <el-button 
            @click="handleDelete"
            :icon="Delete"
            :disabled="!selectedNode || selectedNode.id === 'root'"
            type="danger"
          >
            <span class="button-text">删除</span>
          </el-button>
        </el-tooltip>
      </el-button-group>

      <el-divider direction="vertical" />

      <!-- 执行操作 -->
      <el-button-group>
        <el-tooltip content="执行选中用例" placement="bottom">
          <el-button 
            @click="$emit('execute-selected')"
            type="success"
            :icon="VideoPlay"
            :disabled="!selectedNode || !hasTestCase"
          >
            <span class="button-text">执行选中</span>
          </el-button>
        </el-tooltip>
        <el-tooltip content="执行全部用例" placement="bottom">
          <el-button 
            @click="$emit('execute-all')"
            type="warning"
            :icon="VideoPlay"
          >
            <span class="button-text">执行全部</span>
          </el-button>
        </el-tooltip>
        <el-tooltip content="停止执行" placement="bottom">
          <el-button 
            @click="handleStop"
            type="info"
            :icon="VideoPause"
            :disabled="!isExecuting"
          >
            <span class="button-text">停止执行</span>
          </el-button>
        </el-tooltip>
      </el-button-group>
    </div>

    <div class="right-tools">
      <!-- 视图操作 -->
      <el-button-group>
        <el-tooltip content="放大" placement="bottom">
          <el-button 
            @click="$emit('zoom-in')"
            :icon="ZoomIn"
          >
            <span class="button-text">放大</span>
          </el-button>
        </el-tooltip>
        <el-tooltip content="缩小" placement="bottom">
          <el-button 
            @click="$emit('zoom-out')"
            :icon="ZoomOut"
          >
            <span class="button-text">缩小</span>
          </el-button>
        </el-tooltip>
        <el-tooltip content="适应视图" placement="bottom">
          <el-button 
            @click="$emit('reset-zoom')"
            :icon="FullScreen"
          >
            <span class="button-text">适应视图</span>
          </el-button>
        </el-tooltip>
      </el-button-group>

      <el-divider direction="vertical" />

      <!-- 更多功能 -->
      <el-dropdown @command="handleCommand" trigger="click">
        <el-button :icon="More">
          <span class="button-text">更多</span>
          <el-icon class="el-icon--right"><ArrowDown /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="import">
              <el-icon><Upload /></el-icon>
              导入脑图
            </el-dropdown-item>
            <el-dropdown-item command="template">
              <el-icon><Grid /></el-icon>
              模板管理
            </el-dropdown-item>
            <el-dropdown-item command="settings">
              <el-icon><Setting /></el-icon>
              设置
            </el-dropdown-item>
            <el-dropdown-item command="help" divided>
              <el-icon><QuestionFilled /></el-icon>
              快捷键帮助
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>

      <!-- 缩放比例显示 -->
      <div class="zoom-indicator">
        {{ Math.round(zoomLevel * 100) }}%
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { ElMessageBox } from 'element-plus';
import { 
  Document as Save,
  Download,
  CirclePlus as Plus,
  Delete,
  VideoPlay,
  VideoPause,
  ZoomIn,
  ZoomOut,
  FullScreen,
  RefreshLeft,
  RefreshRight,
  CopyDocument,
  Document,
  More,
  Upload,
  Grid,
  Setting,
  QuestionFilled,
  ArrowDown
} from '@element-plus/icons-vue';

const props = defineProps({
  selectedNode: {
    type: Object,
    default: null
  },
  graph: {
    type: Object,
    default: null
  },
  canUndo: {
    type: Boolean,
    default: false
  },
  canRedo: {
    type: Boolean,
    default: false
  },
  hasClipboard: {
    type: Boolean,
    default: false
  },
  isExecuting: {
    type: Boolean,
    default: false
  },
  zoomLevel: {
    type: Number,
    default: 1
  }
});

const emit = defineEmits([
  'save-map',
  'export-map',
  'add-node',
  'add-parent-node',
  'copy-node',
  'paste-node',
  'remove-node',
  'execute-selected',
  'execute-all',
  'stop-execution',
  'zoom-in',
  'zoom-out',
  'reset-zoom',
  'undo',
  'redo',
  'import-map',
  'show-templates',
  'show-settings',
  'show-help'
]);

// 计算属性
const hasTestCase = computed(() => {
  return props.selectedNode?.data?.testCase;
});

// 方法
const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(
      '确认删除选中的节点吗？此操作不可撤销。',
      '删除确认',
      {
        confirmButtonText: '确认删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );
    emit('remove-node');
  } catch {
    // 用户取消删除
  }
};

const handleStop = () => {
  emit('stop-execution');
};

const handleCommand = (command) => {
  console.log('下拉菜单命令:', command);
  switch (command) {
    case 'import':
      emit('import-map');
      break;
    case 'template':
      emit('show-templates');
      break;
    case 'settings':
      emit('show-settings');
      break;
    case 'help':
      emit('show-help');
      break;
    default:
      console.warn('未知的命令:', command);
  }
};
</script>

<style scoped>
.toolbar {
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  box-shadow: 
    0 12px 24px rgba(0,0,0,0.15),
    0 4px 8px rgba(0,0,0,0.1),
    inset 0 1px 0 rgba(255,255,255,0.3);
  min-height: 80px;
  border: 1px solid rgba(255,255,255,0.2);
  position: relative;
  overflow: hidden;
}

.toolbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255,255,255,0.1) 0%, 
    rgba(255,255,255,0.05) 50%, 
    rgba(0,0,0,0.05) 100%);
  pointer-events: none;
}

.left-tools,
.right-tools {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 24px;
  position: relative;
  z-index: 1;
}

.left-tools {
  flex: 1;
}

.right-tools {
  flex-shrink: 0;
}

.el-button-group {
  margin-right: 0;
}

.el-button-group .el-button {
  margin-left: -1px;
}

.el-button-group .el-button:first-child {
  margin-left: 0;
}

/* 增加按钮组之间的间距 */
.el-button-group + .el-button-group {
  margin-left: 8px;
}

/* 按钮组样式优化 */
.el-button-group {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 优化分隔线样式 */
.el-divider--vertical {
  height: 32px;
  margin: 0 16px;
  border-color: rgba(255, 255, 255, 0.3);
  border-width: 1px;
}

.zoom-indicator {
  padding: 10px 16px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  color: #fff;
  min-width: 70px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* 工具栏按钮特殊样式 */
.toolbar .el-button {
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  font-weight: 500;
}

.toolbar .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
}

.toolbar .el-button:active {
  transform: translateY(0);
}

.toolbar .el-button--primary {
  background: linear-gradient(135deg, #42a5f5 0%, #1976d2 100%);
  border-color: rgba(255, 255, 255, 0.3);
}

.toolbar .el-button--primary:hover {
  background: linear-gradient(135deg, #64b5f6 0%, #1976d2 100%);
  box-shadow: 0 8px 16px rgba(66, 165, 245, 0.4);
}

.toolbar .el-button--success {
  background: linear-gradient(135deg, #66bb6a 0%, #388e3c 100%);
  border-color: rgba(255, 255, 255, 0.3);
}

.toolbar .el-button--success:hover {
  background: linear-gradient(135deg, #81c784 0%, #4caf50 100%);
  box-shadow: 0 8px 16px rgba(102, 187, 106, 0.4);
}

.toolbar .el-button--warning {
  background: linear-gradient(135deg, #ffa726 0%, #f57c00 100%);
  border-color: rgba(255, 255, 255, 0.3);
}

.toolbar .el-button--warning:hover {
  background: linear-gradient(135deg, #ffb74d 0%, #ff9800 100%);
  box-shadow: 0 8px 16px rgba(255, 167, 38, 0.4);
}

.toolbar .el-button--danger {
  background: linear-gradient(135deg, #ef5350 0%, #d32f2f 100%);
  border-color: rgba(255, 255, 255, 0.3);
}

.toolbar .el-button--danger:hover {
  background: linear-gradient(135deg, #f44336 0%, #c62828 100%);
  box-shadow: 0 8px 16px rgba(239, 83, 80, 0.4);
}

.toolbar .el-button--info {
  background: linear-gradient(135deg, #78909c 0%, #455a64 100%);
  border-color: rgba(255, 255, 255, 0.3);
}

.toolbar .el-button--info:hover {
  background: linear-gradient(135deg, #90a4ae 0%, #546e7a 100%);
  box-shadow: 0 8px 16px rgba(120, 144, 156, 0.4);
}

.toolbar .el-button:disabled {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
}

.toolbar .el-button:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .toolbar {
    flex-wrap: wrap;
    gap: 16px;
    min-height: 100px;
  }
  
  .left-tools,
  .right-tools {
    gap: 20px;
  }
  
  .el-button-group + .el-button-group {
    margin-left: 6px;
  }
  
  .el-divider--vertical {
    margin: 0 12px;
  }
}

@media (max-width: 1200px) {
  .toolbar {
    flex-wrap: wrap;
    gap: 12px;
    min-height: 120px;
  }
  
  .left-tools,
  .right-tools {
    gap: 16px;
  }
  
  .el-button-group + .el-button-group {
    margin-left: 4px;
  }
  
  .el-divider--vertical {
    margin: 0 8px;
  }
  
  .button-text {
    display: none;
  }
}

@media (max-width: 768px) {
  .toolbar {
    padding: 16px;
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .left-tools,
  .right-tools {
    gap: 8px;
    justify-content: center;
  }
  
  .left-tools {
    order: 1;
  }
  
  .right-tools {
    order: 2;
  }
  
  .el-button-group + .el-button-group {
    margin-left: 4px;
  }
  
  .el-divider--vertical {
    display: none;
  }
  
  .zoom-indicator {
    display: none;
  }
}

/* 按钮动画效果 */
.el-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.el-button:hover {
  transform: translateY(-2px);
}

.el-button:active {
  transform: translateY(0);
}

/* 按钮组悬停效果 */
.el-button-group:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 按钮文字样式 */
.button-text {
  margin-left: 8px;
  font-size: 14px;
  font-weight: 500;
}

/* 下拉菜单样式优化 */
:deep(.el-dropdown-menu) {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

:deep(.el-dropdown-menu__item) {
  color: #333;
  padding: 12px 16px;
  transition: all 0.3s ease;
}

:deep(.el-dropdown-menu__item:hover) {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

/* 危险操作按钮特殊样式 */
.el-button--danger {
  position: relative;
}

.el-button--danger:hover {
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
}
</style> 