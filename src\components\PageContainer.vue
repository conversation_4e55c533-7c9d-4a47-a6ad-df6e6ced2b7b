<template>
  <div class="page-container">
    <div class="page-wrapper">
      <div class="page-header">
        <h2 class="page-title">{{ title }}</h2>
        <div class="page-actions">
          <slot name="actions"></slot>
        </div>
      </div>
      <div class="page-content">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  title: {
    type: String,
    required: true
  }
});
</script>

<style scoped>
.page-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
}

.page-wrapper {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.page-title {
  font-size: 20px;
  font-weight: 500;
  color: #303133;
  margin: 0;
}

.page-content {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 24px;
  width: 100%;
}

/* 响应式设计 */
@media (max-width: 1440px) {
  .page-wrapper {
    max-width: 1200px;
  }
}

@media (max-width: 1200px) {
  .page-wrapper {
    max-width: 1000px;
  }
}

@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .page-wrapper {
    max-width: 100%;
  }
  
  .page-content {
    padding: 16px;
    border-radius: 6px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .page-title {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .page-container {
    padding: 12px;
  }
  
  .page-content {
    padding: 12px;
  }
}
</style> 