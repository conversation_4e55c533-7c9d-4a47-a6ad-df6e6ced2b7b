# 项目分析页面 API 接口规范

## 概述

本文档定义了项目分析页面所需的所有API接口，包括统计数据、图表数据和执行记录等。

## 接口列表

### 1. 项目统计概览接口

**接口地址：** `GET /api/project/statistics`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| project_id | integer | 否 | 项目ID，不传则统计所有项目 |
| start_date | string | 否 | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 否 | 结束日期，格式：YYYY-MM-DD |

**返回数据结构：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_executions": {
      "value": 1286,
      "trend": 12.5,
      "trend_type": "up"
    },
    "avg_execution_time": {
      "value": 1.5,
      "unit": "s",
      "trend": -5.2,
      "trend_type": "down"
    },
    "pass_rate": {
      "value": 98.5,
      "unit": "%",
      "trend": 2.3,
      "trend_type": "up"
    },
    "failed_cases": {
      "value": 12,
      "trend": -8.5,
      "trend_type": "down"
    }
  }
}
```

**字段说明：**
- `value`: 统计值
- `trend`: 趋势百分比（正数表示上升，负数表示下降）
- `trend_type`: 趋势类型（up/down）
- `unit`: 单位（可选）

---

### 2. 测试执行趋势接口

**接口地址：** `GET /api/project/execution-trend`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| time_range | string | 是 | 时间范围：week/month/quarter |
| project_id | integer | 否 | 项目ID，不传则查询所有项目 |

**返回数据结构：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "time_range": "week",
    "labels": ["2024-01-15", "2024-01-16", "2024-01-17", "2024-01-18", "2024-01-19", "2024-01-20", "2024-01-21"],
    "datasets": [
      {
        "name": "总执行次数",
        "data": [150, 230, 180, 290, 200, 250, 220],
        "color": "#409EFF"
      },
      {
        "name": "成功用例",
        "data": [140, 220, 175, 280, 190, 240, 215],
        "color": "#67C23A"
      },
      {
        "name": "失败用例",
        "data": [8, 7, 3, 6, 8, 7, 4],
        "color": "#F56C6C"
      },
      {
        "name": "阻塞用例",
        "data": [2, 3, 2, 4, 2, 3, 1],
        "color": "#E6A23C"
      }
    ]
  }
}
```

**字段说明：**
- `labels`: X轴标签数组
- `datasets`: 数据集数组
  - `name`: 数据集名称
  - `data`: 数据点数组
  - `color`: 图表颜色

---

### 3. 缺陷分布统计接口

**接口地址：** `GET /api/project/defect-distribution`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| project_id | integer | 否 | 项目ID，不传则查询所有项目 |
| start_date | string | 否 | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 否 | 结束日期，格式：YYYY-MM-DD |

**返回数据结构：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "severity_distribution": [
      {
        "name": "致命",
        "value": 20,
        "percentage": 20.0,
        "color": "#F56C6C"
      },
      {
        "name": "严重",
        "value": 30,
        "percentage": 30.0,
        "color": "#E6A23C"
      },
      {
        "name": "一般",
        "value": 40,
        "percentage": 40.0,
        "color": "#409EFF"
      },
      {
        "name": "轻微",
        "value": 10,
        "percentage": 10.0,
        "color": "#67C23A"
      }
    ],
    "weekly_new": 15,
    "weekly_trend": 8.5,
    "total_count": 100
  }
}
```

**字段说明：**
- `severity_distribution`: 严重程度分布数组
  - `name`: 严重程度名称
  - `value`: 缺陷数量
  - `percentage`: 占比百分比
  - `color`: 显示颜色
- `weekly_new`: 本周新增缺陷数量
- `weekly_trend`: 本周增长趋势百分比（正数表示增长，负数表示减少）
- `total_count`: 缺陷总数

---

### 4. 通过率趋势接口

**接口地址：** `GET /api/project/pass-rate-trend`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| time_range | string | 否 | 时间范围：week/month/quarter，默认week |
| project_id | integer | 否 | 项目ID，不传则查询所有项目 |

**返回数据结构：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "labels": ["2024-01-15", "2024-01-16", "2024-01-17", "2024-01-18", "2024-01-19", "2024-01-20", "2024-01-21"],
    "pass_rates": [95.2, 97.8, 94.5, 98.1, 96.7, 99.2, 97.5]
  }
}
```

**字段说明：**
- `labels`: 日期标签数组
- `pass_rates`: 对应日期的通过率数组（百分比数值）

---

### 5. 执行时长分布接口

**接口地址：** `GET /api/project/duration-distribution`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| project_id | integer | 否 | 项目ID，不传则查询所有项目 |
| start_date | string | 否 | 开始日期，格式：YYYY-MM-DD |
| end_date | string | 否 | 结束日期，格式：YYYY-MM-DD |

**返回数据结构：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "labels": ["0-1s", "1-3s", "3-5s", "5-10s", "10s+"],
    "counts": [120, 85, 45, 25, 8]
  }
}
```

**字段说明：**
- `labels`: 时长区间标签数组
- `counts`: 对应区间的用例数量数组

---

### 6. 最近执行记录接口

**接口地址：** `GET /api/project/recent-executions`

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| limit | integer | 否 | 返回记录数量，默认20 |
| project_id | integer | 否 | 项目ID，不传则查询所有项目 |

**返回数据结构：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "executions": [
      {
        "id": 1,
        "case_name": "登录功能测试",
        "status": "passed",
        "execute_time": "2024-01-16 15:30:00",
        "duration": 1.2,
        "project_name": "智能助手项目",
        "executor": "张三"
      },
      {
        "id": 2,
        "case_name": "商品搜索测试",
        "status": "failed",
        "execute_time": "2024-01-16 15:29:30",
        "duration": 2.1,
        "project_name": "智能助手项目",
        "executor": "李四",
        "error_message": "断言失败：期望值不匹配"
      },
      {
        "id": 3,
        "case_name": "用户注册测试",
        "status": "error",
        "execute_time": "2024-01-16 15:28:00",
        "duration": 0.8,
        "project_name": "电商平台",
        "executor": "王五",
        "error_message": "网络连接超时"
      },
      {
        "id": 4,
        "case_name": "订单支付流程",
        "status": "skipped",
        "execute_time": "2024-01-16 15:27:30",
        "duration": 0.0,
        "project_name": "电商平台",
        "executor": "赵六",
        "skip_reason": "依赖服务不可用"
      }
    ]
  }
}
```

**字段说明：**
- `id`: 执行记录ID
- `case_name`: 测试用例名称
- `status`: 执行状态（passed/failed/error/skipped）
- `execute_time`: 执行时间
- `duration`: 执行时长（秒）
- `project_name`: 项目名称
- `executor`: 执行者
- `error_message`: 错误信息（可选，失败/错误时提供）
- `skip_reason`: 跳过原因（可选，跳过时提供）

---

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 执行状态枚举

| 状态值 | 中文名称 | 说明 |
|--------|----------|------|
| passed | 通过 | 测试用例执行成功 |
| failed | 失败 | 测试用例执行失败 |
| error | 错误 | 测试用例执行出现异常 |
| skipped | 跳过 | 测试用例被跳过 |

## 趋势类型枚举

| 类型值 | 说明 |
|--------|------|
| up | 上升趋势 |
| down | 下降趋势 |

## 时间范围枚举

| 范围值 | 说明 |
|--------|------|
| week | 近7天 |
| month | 近30天 |
| quarter | 近3个月 |

## 注意事项

1. 所有时间字段均使用 `YYYY-MM-DD HH:mm:ss` 格式
2. 所有接口都需要在请求头中携带认证信息
3. 分页参数从1开始计数
4. 数值类型的趋势百分比保留一位小数
5. 颜色值使用十六进制格式（如：#409EFF）
6. 所有接口都应支持跨域请求（CORS）

## 示例请求

```bash
# 获取项目统计概览
curl -X GET "http://localhost:8081/api/project/statistics" \
  -H "Authorization: Bearer your_token_here" \
  -H "Content-Type: application/json"

# 获取近7天执行趋势
curl -X GET "http://localhost:8081/api/project/execution-trend?time_range=week" \
  -H "Authorization: Bearer your_token_here" \
  -H "Content-Type: application/json"

# 获取最近20条执行记录
curl -X GET "http://localhost:8081/api/project/recent-executions?limit=20" \
  -H "Authorization: Bearer your_token_here" \
  -H "Content-Type: application/json"
``` 