<template>
  <Home>
    <div class="defect-list-container">
      <div class="page-header">
        <h2>缺陷列表</h2>
        <el-button type="primary" @click="createDefect">
          <el-icon><plus /></el-icon>新建缺陷
        </el-button>
      </div>

      <!-- 搜索表单 -->
      <el-card class="filter-container">
        <el-form :model="queryParams" ref="queryForm" :inline="true">
          <el-form-item label="缺陷标题" prop="title">
            <el-input v-model="queryParams.title" placeholder="请输入缺陷标题" clearable />
          </el-form-item>
          
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
              <el-option
                v-for="item in statusOptions"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="queryParams.priority" placeholder="请选择优先级" clearable>
              <el-option
                v-for="item in priorityOptions"
                :key="item.code"
                :label="item.name"
                :value="item.code"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="测试计划" prop="testPlanId">
            <el-select v-model="queryParams.testPlanId" placeholder="请选择测试计划" clearable>
              <el-option 
                v-for="item in testPlans"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          
          <el-form-item label="创建时间">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
          
          <el-form-item>
            <el-button type="primary" @click="handleQuery">查询</el-button>
            <el-button @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        
        <!-- 已选筛选条件展示区域 -->
        <div class="selected-filters" v-if="hasSelectedFilters">
          <span class="filter-label">已选筛选条件：</span>
          <el-tag 
            v-if="queryParams.title" 
            closable
            @close="queryParams.title = ''"
            class="filter-tag"
          >
            标题: {{ queryParams.title }}
          </el-tag>
          
          <el-tag 
            v-if="queryParams.status" 
            closable
            @close="queryParams.status = ''"
            class="filter-tag"
            type="success"
          >
            状态: {{ getStatusName(queryParams.status) }}
          </el-tag>
          
          <el-tag 
            v-if="queryParams.priority" 
            closable
            @close="queryParams.priority = ''"
            class="filter-tag"
            type="warning"
          >
            优先级: {{ getPriorityName(queryParams.priority) }}
          </el-tag>
          
          <el-tag 
            v-if="queryParams.testPlanId" 
            closable
            @close="queryParams.testPlanId = ''"
            class="filter-tag"
            type="info"
          >
            测试计划: {{ getTestPlanName(queryParams.testPlanId) }}
          </el-tag>
          
          <el-tag 
            v-if="dateRange && dateRange.length === 2" 
            closable
            @close="dateRange = []"
            class="filter-tag"
            type="danger"
          >
            创建时间: {{ dateRange[0] }} 至 {{ dateRange[1] }}
          </el-tag>
          
          <el-button type="text" @click="resetQuery" class="clear-all">清除全部</el-button>
        </div>
      </el-card>

      <!-- 数据表格 -->
      <el-card>
        <div class="table-toolbar">
          <div class="left">
            <el-button type="danger" :disabled="selectedRows.length === 0" @click="handleBatchDelete" class="toolbar-button">
              <el-icon><Delete /></el-icon><span>批量删除</span>
            </el-button>
            <el-button type="success" :disabled="selectedRows.length === 0" @click="handleBatchExport" class="toolbar-button">
              <el-icon><Download /></el-icon><span>导出选中</span>
            </el-button>
          </div>
          <div class="right">
            <el-button type="primary" plain @click="refreshTable" class="toolbar-button">
              <el-icon><Refresh /></el-icon><span>刷新</span>
            </el-button>
          </div>
        </div>
        <el-table
          v-loading="loading"
          :data="defectList"
          border
          stripe
          highlight-current-row
          style="width: 100%"
          @selection-change="handleSelectionChange"
          row-key="id"
        >
          <el-table-column type="selection" width="45" align="center" />
          <el-table-column label="ID" prop="id" width="60" align="center" />
          <el-table-column label="标题" prop="title" min-width="200" show-overflow-tooltip>
            <template #default="scope">
              <el-link type="primary" @click="viewDefect(scope.row)">{{ scope.row.title }}</el-link>
            </template>
          </el-table-column>
          <el-table-column label="优先级" prop="priority" width="90" align="center">
            <template #default="scope">
              <el-tag :type="getPriorityType(scope.row.priorityLevel)" size="small" effect="dark">
                {{ scope.row.priority }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="状态" prop="status" width="90" align="center">
            <template #default="scope">
              <el-tag :type="getStatusTagType(scope.row.statusCode)" size="small" effect="plain">
                {{ scope.row.status }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="关联测试计划" prop="testPlan" width="150" show-overflow-tooltip />
          <el-table-column label="负责人" prop="assignee" width="90" align="center" />
          <el-table-column label="创建时间" prop="createTime" width="140" align="center" />
          <el-table-column label="操作" width="180" fixed="right">
            <template #default="scope">
              <div class="operation-buttons">
                <el-tooltip content="查看详情" placement="top">
                  <el-button type="primary" circle size="small" @click="viewDefect(scope.row)">
                    <el-icon><View /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="编辑" placement="top">
                  <el-button type="warning" circle size="small" @click="editDefect(scope.row)">
                    <el-icon><Edit /></el-icon>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="更多操作" placement="top">
                  <el-dropdown trigger="click">
                    <el-button type="info" circle size="small">
                      <el-icon><MoreFilled /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item @click="showAssignDialog(scope.row)">
                          <el-icon><UserFilled /></el-icon>指派
                        </el-dropdown-item>
                        <el-dropdown-item @click="showStatusChangeDialog(scope.row)">
                          <el-icon><SetUp /></el-icon>更改状态
                        </el-dropdown-item>
                        <el-dropdown-item divided @click="handleDelete(scope.row)">
                          <el-icon><Delete /></el-icon>删除
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="queryParams.pageNum"
            v-model:page-size="queryParams.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            background
          />
        </div>
      </el-card>
    </div>
    
    <!-- 指派对话框 -->
    <el-dialog title="指派缺陷" v-model="assignDialogVisible" width="30%">
      <el-form :model="assignForm" label-width="80px">
        <el-form-item label="指派给">
          <el-select v-model="assignForm.assigneeId" placeholder="请选择">
                  <el-option
                    v-for="user in userList"
                    :key="user.id"
                    :label="user.username"
              :value="user.id"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
          <el-button @click="assignDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmAssign">确定</el-button>
          </span>
        </template>
      </el-dialog>

    <!-- 状态变更对话框 -->
    <el-dialog title="更改状态" v-model="statusDialogVisible" width="30%">
      <el-form :model="statusForm" label-width="80px">
        <el-form-item label="新状态">
          <el-select v-model="statusForm.statusCode" placeholder="请选择">
              <el-option
              v-for="item in statusOptions"
              :key="item.code"
              :label="item.name"
              :value="item.code"
              />
            </el-select>
          </el-form-item>
        <el-form-item label="解决方案" v-if="isResolutionRequired">
          <el-input
            v-model="statusForm.resolution"
            type="textarea"
            rows="3"
            placeholder="请输入解决方案"
          />
        </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
          <el-button @click="statusDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmStatusChange">确定</el-button>
          </span>
        </template>
      </el-dialog>
  </Home>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Delete, Download, Refresh, View, Edit, MoreFilled, UserFilled, SetUp } from '@element-plus/icons-vue';
import Home from '@/components/HomePage.vue';
import { 
  getDefectList, 
  getDefectOptions, 
  assignDefect,
  changeDefectStatus,
  deleteDefect,
  batchDeleteDefects,
  exportDefects
} from '@/api/defect';
import { getUserList } from '@/api/user';

const router = useRouter();
const loading = ref(false);
const defectList = ref([]);
const total = ref(0);
const selectedRows = ref([]);
const dateRange = ref([]);

// 查询参数
const queryParams = reactive({
  title: '',
  status: '',
  priority: '',
  testPlanId: '',
  createTimeStart: '',
  createTimeEnd: '',
  pageNum: 1,
  pageSize: 10
});

// 选项数据
const statusOptions = ref([]);
const priorityOptions = ref([]);
const testPlans = ref([]);
const userList = ref([]);

// 计算是否有已选择的筛选条件
const hasSelectedFilters = computed(() => {
  return queryParams.title || 
         queryParams.status || 
         queryParams.priority || 
         queryParams.testPlanId || 
         (dateRange.value && dateRange.value.length === 2);
});

// 获取状态名称
const getStatusName = (code) => {
  const status = statusOptions.value.find(item => item.code === code);
  return status ? status.name : code;
};

// 获取优先级名称
const getPriorityName = (code) => {
  const priority = priorityOptions.value.find(item => item.code === code);
  return priority ? priority.name : code;
};

// 获取测试计划名称
const getTestPlanName = (id) => {
  const plan = testPlans.value.find(item => item.id === id);
  return plan ? plan.name : id;
};

// 指派表单
const assignDialogVisible = ref(false);
const assignForm = reactive({
  defectId: '',
  assigneeId: ''
});

// 状态变更表单
const statusDialogVisible = ref(false);
const statusForm = reactive({
  defectId: '',
  statusCode: '',
  resolution: ''
});

// 计算是否需要填写解决方案
const isResolutionRequired = computed(() => {
  return ['resolved', 'closed', 'rejected'].includes(statusForm.statusCode);
});

// 监听日期范围变化
watch(dateRange, (val) => {
  if (val && val.length === 2) {
    queryParams.createTimeStart = val[0];
    queryParams.createTimeEnd = val[1];
  } else {
    queryParams.createTimeStart = '';
    queryParams.createTimeEnd = '';
  }
});

// 监听任何筛选条件变化，自动触发查询
watch(() => [
  queryParams.title,
  queryParams.status,
  queryParams.priority,
  queryParams.testPlanId
], () => {
  if (hasSelectedFilters.value) {
    handleQuery();
  }
}, { deep: true });

// 获取缺陷列表数据
const fetchDefectList = async () => {
  loading.value = true;
  try {
    const res = await getDefectList(queryParams);
    if (res.data.code === 200) {
      defectList.value = res.data.data.list;
      total.value = res.data.data.total;
    } else {
      ElMessage.error('获取缺陷列表失败');
    }
  } catch (error) {
    console.error('获取缺陷列表出错:', error);
    ElMessage.error('获取缺陷列表时发生错误');
  } finally {
    loading.value = false;
  }
};

// 获取选项数据
const fetchOptions = async () => {
  try {
    const res = await getDefectOptions();
    if (res.data.code === 200) {
      const { status, priority, testPlans: plans } = res.data.data;
      statusOptions.value = status;
      priorityOptions.value = priority;
      testPlans.value = plans;
      
      // 获取用户列表数据
      const userRes = await getUserList();
      if (userRes.data.code === 200) {
        userList.value = userRes.data.data.items || [];
      } else {
        // 加载失败使用默认数据
        userList.value = [
          { id: 1, username: '李四' },
          { id: 2, username: '王五' },
          { id: 3, username: '赵六' }
        ];
      }
    }
  } catch (error) {
    console.error('获取选项数据出错:', error);
    ElMessage.error('获取选项数据时发生错误');
    // 发生错误时使用默认数据
    userList.value = [
      { id: 1, username: '李四' },
      { id: 2, username: '王五' },
      { id: 3, username: '赵六' }
    ];
  }
};

// 查询
const handleQuery = () => {
  queryParams.pageNum = 1;
  fetchDefectList();
};

// 重置查询
const resetQuery = () => {
  dateRange.value = [];
  Object.assign(queryParams, {
    title: '',
    status: '',
    priority: '',
    testPlanId: '',
    createTimeStart: '',
    createTimeEnd: '',
    pageNum: 1
  });
  fetchDefectList();
};

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection;
};

// 每页大小变化
const handleSizeChange = (size) => {
  queryParams.pageSize = size;
  fetchDefectList();
};

// 当前页变化
const handleCurrentChange = (page) => {
  queryParams.pageNum = page;
  fetchDefectList();
};

// 查看缺陷详情
const viewDefect = (row) => {
  router.push(`/defect-detail/${row.id}`);
};

// 编辑缺陷
const editDefect = (row) => {
  router.push({
    path: '/defect-edit',
    query: { id: row.id }
  });
};

// 新建缺陷
const createDefect = () => {
  router.push('/defect-edit');
};

// 显示指派对话框
const showAssignDialog = (row) => {
  assignForm.defectId = row.id;
  assignForm.assigneeId = '';
  assignDialogVisible.value = true;
};

// 确认指派
const confirmAssign = async () => {
  if (!assignForm.assigneeId) {
    ElMessage.warning('请选择指派人');
    return;
  }
  
  try {
    const res = await assignDefect(assignForm.defectId, assignForm.assigneeId);
    if (res.data.code === 200) {
      ElMessage.success('指派成功');
      assignDialogVisible.value = false;
      fetchDefectList(); // 刷新列表
    } else {
      ElMessage.error('指派失败');
    }
  } catch (error) {
    console.error('指派出错:', error);
    ElMessage.error('指派时发生错误');
  }
};

// 显示状态变更对话框
const showStatusChangeDialog = (row) => {
  statusForm.defectId = row.id;
  statusForm.statusCode = '';
  statusForm.resolution = '';
  statusDialogVisible.value = true;
};

// 确认状态变更
const confirmStatusChange = async () => {
  if (!statusForm.statusCode) {
    ElMessage.warning('请选择状态');
    return;
  }
  
  if (isResolutionRequired.value && !statusForm.resolution) {
    ElMessage.warning('请输入解决方案');
    return;
  }
  
  try {
    const res = await changeDefectStatus(
      statusForm.defectId, 
      statusForm.statusCode, 
      statusForm.resolution
    );
    
    if (res.data.code === 200) {
      ElMessage.success('状态更新成功');
      statusDialogVisible.value = false;
      fetchDefectList(); // 刷新列表
    } else {
      ElMessage.error('状态更新失败');
    }
  } catch (error) {
    console.error('更新状态出错:', error);
    ElMessage.error('更新状态时发生错误');
  }
};

// 获取优先级对应的类型
const getPriorityType = (level) => {
  const types = {
    0: 'danger',   // 紧急
    1: 'warning',  // 高
    2: '',         // 中
    3: 'info'      // 低
  };
  return types[level] || '';
};

// 获取状态对应的标签类型
const getStatusTagType = (statusCode) => {
  const types = {
    new: 'danger',
    processing: 'warning',
    resolved: 'success',
    closed: 'info',
    rejected: ''
  };
  return types[statusCode] || '';
};

// 刷新表格
const refreshTable = () => {
  fetchDefectList();
  ElMessage({
    message: '数据已刷新',
    type: 'success',
    duration: 1000
  });
};

// 删除单个缺陷
const handleDelete = (row) => {
  ElMessageBox.confirm(`确定要删除缺陷 "${row.title}" 吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      loading.value = true;
      const res = await deleteDefect(row.id);
      if (res.data.code === 200) {
        ElMessage({
          type: 'success',
          message: '删除成功'
        });
        fetchDefectList(); // 刷新列表
      } else {
        ElMessage.error('删除失败');
      }
    } catch (error) {
      console.error('删除缺陷出错:', error);
      ElMessage.error('删除缺陷时发生错误');
    } finally {
      loading.value = false;
    }
  }).catch(() => {
    // 取消删除
  });
};

// 批量删除
const handleBatchDelete = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请至少选择一条记录');
    return;
  }
  
  const names = selectedRows.value.map(item => item.title).join('、');
  const ids = selectedRows.value.map(item => item.id);
  
  ElMessageBox.confirm(`确定要删除以下缺陷吗？<br/>${names}`, '批量删除', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    dangerouslyUseHTMLString: true
  }).then(async () => {
    try {
      loading.value = true;
      const res = await batchDeleteDefects(ids);
      if (res.data.code === 200) {
        ElMessage({
          type: 'success',
          message: `成功删除 ${ids.length} 条记录`
        });
        fetchDefectList(); // 刷新列表
      } else {
        ElMessage.error('批量删除失败');
      }
    } catch (error) {
      console.error('批量删除缺陷出错:', error);
      ElMessage.error('批量删除缺陷时发生错误');
    } finally {
      loading.value = false;
    }
  }).catch(() => {
    // 取消删除
  });
};

// 批量导出
const handleBatchExport = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请至少选择一条记录');
    return;
  }
  
  try {
    loading.value = true;
    const ids = selectedRows.value.map(item => item.id);
    const res = await exportDefects(ids);
    
    // 处理文件下载
    const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
    const fileName = `缺陷列表_${new Date().getTime()}.xlsx`;
    
    if (window.navigator && window.navigator.msSaveOrOpenBlob) {
      // 兼容IE
      window.navigator.msSaveOrOpenBlob(blob, fileName);
    } else {
      // Chrome/Firefox
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = fileName;
      link.click();
      window.URL.revokeObjectURL(link.href);
    }
    
    ElMessage({
      type: 'success',
      message: `已导出 ${ids.length} 条记录`
    });
  } catch (error) {
    console.error('导出缺陷出错:', error);
    ElMessage.error('导出缺陷时发生错误');
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  await fetchOptions();
  await fetchDefectList();
});
</script>

<style scoped>
.defect-list-container {
  padding: 20px;
}
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.filter-container {
  margin-bottom: 20px;
}
.selected-filters {
  margin-top: 16px;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.filter-label {
  margin-right: 8px;
  font-size: 14px;
  color: #606266;
}
.filter-tag {
  margin-right: 8px;
  margin-bottom: 6px;
}
.clear-all {
  margin-left: auto;
  color: #f56c6c;
}
.pagination-container {
  margin-top: 20px;
  text-align: right;
}
.table-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}
.left {
  display: flex;
  align-items: center;
  gap: 10px; /* 统一左侧按钮间距 */
}
.right {
  display: flex;
  align-items: center;
}

/* 工具栏按钮样式 */
.toolbar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px; /* 图标和文字的间距 */
}

.toolbar-button .el-icon {
  margin: 0;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.operation-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px; /* 统一按钮间距 */
}

/* 优化按钮内图标的居中展示 */
.operation-buttons :deep(.el-button) {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px;
}

/* 确保图标在按钮中居中 */
.operation-buttons :deep(.el-icon) {
  margin: 0;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 优化下拉菜单中的图标和文字对齐 */
:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 5px;
}

:deep(.el-dropdown-menu__item .el-icon) {
  margin-right: 5px;
}
</style> 