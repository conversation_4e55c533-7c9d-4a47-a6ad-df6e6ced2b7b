<template>
  <Home>
    <div class="defect-edit-container">
      <div class="page-header">
        <div class="header-left">
          <el-button @click="goBack">返回</el-button>
          <h2>{{ isEdit ? '编辑缺陷' : '新建缺陷' }}</h2>
        </div>
        <div class="header-actions">
          <el-button @click="goBack">取消</el-button>
          <el-button type="primary" @click="submitForm" :loading="submitting">保存</el-button>
        </div>
      </div>

      <el-card v-loading="loading">
        <el-form
          ref="formRef"
          :model="defectForm"
          :rules="rules"
          label-width="100px"
          class="defect-form"
        >
          <el-form-item label="缺陷标题" prop="title">
            <el-input v-model="defectForm.title" placeholder="请输入缺陷标题" />
          </el-form-item>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="所属项目" prop="projectId">
                <el-select v-model="defectForm.projectId" placeholder="请选择项目" style="width: 100%">
                  <el-option
                    v-for="item in projectOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="所属版本" prop="version">
                <el-input v-model="defectForm.version" placeholder="请输入版本" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="严重程度" prop="severity">
                <el-select v-model="defectForm.severity" placeholder="请选择严重程度" style="width: 100%">
                  <el-option label="阻塞" value="blocker" />
                  <el-option label="严重" value="critical" />
                  <el-option label="主要" value="major" />
                  <el-option label="次要" value="minor" />
                  <el-option label="轻微" value="trivial" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="优先级" prop="priority">
                <el-select v-model="defectForm.priorityCode" placeholder="请选择优先级" style="width: 100%">
                  <el-option
                    v-for="item in priorityOptions"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="指派给" prop="assigneeId">
                <el-select v-model="defectForm.assigneeId" placeholder="请选择指派人" style="width: 100%">
                  <el-option
                    v-for="user in userList"
                    :key="user.id"
                    :label="user.username"
                    :value="user.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="关联计划" prop="testPlanId">
                <el-select v-model="defectForm.testPlanId" placeholder="请选择测试计划" style="width: 100%">
                  <el-option
                    v-for="plan in testPlans"
                    :key="plan.id"
                    :label="plan.name"
                    :value="plan.id"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="缺陷描述" prop="description" class="description-form-item">
            <div class="editor-container" style="border: 1px solid #ccc; z-index: 100;">
              <Toolbar
                style="border-bottom: 1px solid #ccc"
                :editor="descriptionEditor"
                :defaultConfig="toolbarConfig"
                :mode="mode"
              />
              <Editor
                style="height: 300px; overflow-y: hidden;"
                v-model="defectForm.description"
                :defaultConfig="editorConfig"
                :mode="mode"
                @onCreated="handleDescriptionCreated"
                @onChange="handleDescriptionChange"
                @onPaste="handleEditorPaste"
              />
            </div>
          </el-form-item>

          <el-form-item label="重现步骤" prop="steps" class="steps-form-item">
            <div class="editor-container" style="border: 1px solid #ccc; z-index: 99;">
              <Toolbar
                style="border-bottom: 1px solid #ccc"
                :editor="stepsEditor"
                :defaultConfig="toolbarConfig"
                :mode="mode"
              />
              <Editor
                style="height: 300px; overflow-y: hidden;"
                v-model="defectForm.steps"
                :defaultConfig="editorConfig"
                :mode="mode"
                @onCreated="handleStepsCreated"
                @onChange="handleStepsChange"
                @onPaste="handleEditorPaste"
              />
            </div>
          </el-form-item>

          <el-form-item label="附件">
            <el-upload
              action="#"
              :http-request="uploadFile"
              :file-list="fileList"
              :on-remove="handleRemove"
              :before-remove="beforeRemove"
              :before-upload="beforeUpload"
              multiple
              :limit="5"
            >
              <el-button type="primary">选择文件</el-button>
              <template #tip>
                <div class="el-upload__tip">大小不超过10MB</div>
              </template>
            </el-upload>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </Home>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onBeforeUnmount, shallowRef, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import Home from '@/components/HomePage.vue';
import { 
  getDefectDetail, 
  createDefect,
  updateDefect,
  getDefectOptions,
  uploadAttachment,
  uploadPastedImage,
  deleteAttachment
} from '@/api/defect';
import { getUserList } from '@/api/user';
import { getProjectList } from '@/api/project';
import '@wangeditor/editor/dist/css/style.css';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';

const route = useRoute();
const router = useRouter();
const loading = ref(false);
const submitting = ref(false);
const formRef = ref(null);
const fileList = ref([]);

// 编辑器实例，必须用 shallowRef
const descriptionEditor = shallowRef();
const stepsEditor = shallowRef();

// 编辑器内容
const descriptionContent = ref('');
const stepsContent = ref('');

// 编辑器配置
const editorConfig = {
  placeholder: '请输入内容，可直接粘贴截图...',
  MENU_CONF: {
    uploadImage: {
      customUpload: async (file, insertFn) => {
        try {
          // 上传图片
          const res = await uploadPastedImage(isEdit.value ? route.query.id : null, file);
          if (res.data.code === 200) {
            const imageUrl = res.data.data.url;
            // 插入图片
            insertFn(imageUrl, '粘贴的图片', imageUrl);
            ElMessage.success('图片已上传');
          } else {
            ElMessage.error('图片上传失败');
          }
        } catch (error) {
          console.error('图片上传处理失败:', error);
          ElMessage.error('图片上传处理失败');
        }
      }
    }
  }
};

// 工具栏配置
const toolbarConfig = {
  excludeKeys: [
    'insertTable',
    'codeBlock',
    'todo',
    'group-video',
    'insertLink'
  ]
};

// 编辑器模式
const mode = 'default'; // 或 'simple'

// 编辑模式标识
const isEdit = computed(() => Boolean(route.query.id));

// 表单数据
const defectForm = reactive({
  title: '',
  description: '',
  steps: '',
  severity: '',
  priorityCode: '',
  projectId: '',
  version: '',
  assigneeId: '',
  testPlanId: '',
});

// 表单验证规则
const rules = {
  title: [{ required: true, message: '请输入缺陷标题', trigger: 'blur' }],
  severity: [{ required: true, message: '请选择严重程度', trigger: 'change' }],
  priorityCode: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  description: [{ required: true, message: '请输入缺陷描述', trigger: 'blur' }],
};

// 选项数据
const statusOptions = ref([]);
const priorityOptions = ref([]);
const testPlans = ref([]);
const userList = ref([]);
const projectOptions = ref([]);

// 处理编辑器创建完成
const handleDescriptionCreated = (editor) => {
  descriptionEditor.value = editor; // 记录 editor 实例，重要！
};

const handleStepsCreated = (editor) => {
  stepsEditor.value = editor; // 记录 editor 实例，重要！
};

// 处理编辑器内容变化
const handleDescriptionChange = (editor) => {
  defectForm.description = editor.getHtml();
};

const handleStepsChange = (editor) => {
  defectForm.steps = editor.getHtml();
};

// 处理编辑器粘贴事件
const handleEditorPaste = (event, editor) => {
  // wangEditor 已经内置了图片粘贴处理，这里不需要额外处理
};

// 获取缺陷详情（编辑模式）
const fetchDefectDetail = async (id) => {
  try {
    loading.value = true;
    const res = await getDefectDetail(id);
    if (res.data.code === 200) {
      const data = res.data.data;
      
      console.log('获取到的缺陷详情数据:', data);
      
      // 填充表单数据
      Object.assign(defectForm, {
        title: data.title,
        description: data.description || '',
        steps: data.steps || '',
        severity: getSeverityCode(data.severity),
        priorityCode: data.priorityCode || getPriorityCode(data.priority), // 直接使用priorityCode或转换
        projectId: data.projectId,
        version: data.version,
        assigneeId: data.assigneeId,
        testPlanId: data.testPlanId,
      });
      
      console.log('加载的缺陷数据:', defectForm);
      
      // 处理附件
      if (data.attachments && data.attachments.length > 0) {
        fileList.value = data.attachments.map(item => ({
          name: item.name,
          url: item.url,
          uid: item.attachment_id, // 使用attachment_id作为uid
        }));
        
        console.log('处理后的附件列表:', fileList.value);
      }
      
      // 手动触发编辑器内容更新
      if (descriptionEditor.value) {
        descriptionEditor.value.setHtml(defectForm.description);
      }
      
      if (stepsEditor.value) {
        stepsEditor.value.setHtml(defectForm.steps);
      }
    } else {
      ElMessage.error('获取缺陷详情失败');
    }
  } catch (error) {
    console.error('获取缺陷详情出错:', error);
    ElMessage.error('获取缺陷详情时发生错误');
  } finally {
    loading.value = false;
  }
};

// 获取选项数据
const fetchOptions = async () => {
  try {
    const res = await getDefectOptions();
    if (res.data.code === 200) {
      const { status, priority, testPlans: plans } = res.data.data;
      statusOptions.value = status;
      priorityOptions.value = priority;
      testPlans.value = plans;
      
      // 如果API没有返回优先级数据，使用默认值
      if (!priorityOptions.value || priorityOptions.value.length === 0) {
        priorityOptions.value = [
          { code: 'critical', name: '紧急' },
          { code: 'high', name: '高' },
          { code: 'medium', name: '中' },
          { code: 'low', name: '低' }
        ];
      }
      
      // 获取项目列表数据
      const projectRes = await getProjectList();
      if (projectRes.data.code === 200) {
        projectOptions.value = projectRes.data.data.projects || [];
        console.log('项目列表数据:', projectOptions.value);
      } else {
        // 获取项目数据失败，使用默认数据
        projectOptions.value = [
          { id: 1, name: '自动化测试平台' },
          { id: 2, name: '移动端应用' }
        ];
        console.warn('获取项目列表失败，使用默认数据');
      }
      
      // 获取用户列表数据
      const userRes = await getUserList();
      if (userRes.data.code === 200) {
        userList.value = userRes.data.data.items || [];
      } else {
        // 加载失败使用默认数据
        userList.value = [
          { id: 1, name: '李四' },
          { id: 2, name: '王五' },
          { id: 3, name: '赵六' }
        ];
      }
    }
  } catch (error) {
    console.error('获取选项数据出错:', error);
    ElMessage.error('获取选项数据时发生错误');
    
    // 发生错误时使用默认数据
    priorityOptions.value = [
      { code: 'critical', name: '紧急' },
      { code: 'high', name: '高' },
      { code: 'medium', name: '中' },
      { code: 'low', name: '低' }
    ];
    
    projectOptions.value = [
      { id: 1, name: '自动化测试平台' },
      { id: 2, name: '移动端应用' }
    ];
    
    userList.value = [
      { id: 1, name: '李四' },
      { id: 2, name: '王五' },
      { id: 3, name: '赵六' }
    ];
  }
};

// 根据优先级名称获取优先级代码
const getPriorityCode = (priorityName) => {
  if (!priorityName) return '';
  
  console.log('获取优先级代码，输入:', priorityName);
  
  // 如果输入的已经是代码，直接返回
  const isCode = ['critical', 'high', 'medium', 'low'].includes(priorityName);
  if (isCode) return priorityName;
  
  // 优先从选项中查找
  const priority = priorityOptions.value.find(item => item.name === priorityName);
  if (priority) {
    console.log('从选项中找到优先级:', priority);
    return priority.code;
  }
  
  // 如果没找到，使用映射表
  const priorityNameToCode = {
    '紧急': 'critical',
    '高': 'high',
    '中': 'medium',
    '低': 'low'
  };
  
  const code = priorityNameToCode[priorityName] || '';
  console.log('从映射表获取优先级代码:', code);
  return code;
};

// 根据优先级代码获取优先级名称
const getPriorityName = (priorityCode) => {
  // 优先从选项中查找
  const priority = priorityOptions.value.find(item => item.code === priorityCode);
  if (priority) return priority.name;
  
  // 如果没找到，使用映射表
  const priorityCodeToName = {
    'critical': '紧急',
    'high': '高',
    'medium': '中',
    'low': '低'
  };
  
  return priorityCodeToName[priorityCode] || '';
};

// 处理严重程度的中英文转换
const getSeverityCode = (severityName) => {
  // 中文到英文的映射
  const severityZhToEn = {
    '阻塞': 'blocker',
    '严重': 'critical',
    '主要': 'major',
    '次要': 'minor',
    '轻微': 'trivial'
  };
  
  return severityZhToEn[severityName] || severityName;
};

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return;
  
  try {
    await formRef.value.validate();
    
    submitting.value = true;
    
    // 确保从编辑器获取最新内容
    if (descriptionEditor.value) {
      defectForm.description = descriptionEditor.value.getHtml();
    }
    
    if (stepsEditor.value) {
      defectForm.steps = stepsEditor.value.getHtml();
    }
    
    // 构造提交数据
    const submitData = {
      title: defectForm.title,
      description: defectForm.description,
      steps: defectForm.steps,
      severity: defectForm.severity,
      priority: getPriorityName(defectForm.priorityCode),
      priorityCode: defectForm.priorityCode, // 同时提交优先级代码
      projectId: defectForm.projectId,
      version: defectForm.version,
      assigneeId: defectForm.assigneeId,
      testPlanId: defectForm.testPlanId,
      // 附件稍后处理
    };
    
    console.log('提交的数据:', submitData);
    
    let res;
    if (isEdit.value) {
      // 编辑模式
      res = await updateDefect(route.query.id, submitData);
    } else {
      // 新建模式
      res = await createDefect(submitData);
    }
    
    if (res.data.code === 200) {
      ElMessage.success(isEdit.value ? '缺陷更新成功' : '缺陷创建成功');
      
      // 上传附件（如有新上传的文件）
      const newFiles = fileList.value.filter(file => file.raw);
      if (newFiles.length > 0 && res.data.data?.id) {
        const defectId = res.data.data.id;
        console.log(`准备上传${newFiles.length}个新附件到缺陷ID: ${defectId}`);
        
        try {
          await Promise.all(newFiles.map(async file => {
            const uploadRes = await uploadAttachment(defectId, file.raw);
            console.log('附件上传结果:', uploadRes);
            return uploadRes;
          }));
          console.log('所有附件上传完成');
        } catch (uploadError) {
          console.error('上传附件时出错:', uploadError);
          ElMessage.warning('部分附件可能上传失败，请检查');
        }
      }
      
      // 跳转到详情页
      if (res.data.data?.id) {
        router.push(`/defect-detail/${res.data.data.id}`);
      } else {
        router.push('/defect-list');
      }
    } else {
      ElMessage.error(isEdit.value ? '更新缺陷失败' : '创建缺陷失败');
    }
  } catch (error) {
    console.error('提交表单出错:', error);
    ElMessage.error('提交表单时发生错误');
  } finally {
    submitting.value = false;
  }
};

// 文件上传前的验证
const beforeUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error('上传文件大小不能超过 10MB!');
    return false;
  }
  return true;
};

// 自定义上传
const uploadFile = (params) => {
  const file = params.file;
  console.log('准备上传文件:', file);
  
  // 生成唯一ID作为临时uid
  const tempUid = Date.now() + '_' + Math.floor(Math.random() * 1000);
  
  fileList.value.push({
    name: file.name,
    raw: file,
    uid: tempUid,
  });
  
  console.log('添加到文件列表:', fileList.value);
  return false;
};

// 移除文件
const handleRemove = async (file) => {
  try {
    console.log('准备删除文件:', file);
    
    // 如果是编辑模式且是已有的附件（有uid且没有raw属性），则调用API删除
    if (isEdit.value && file.uid && !file.raw) {
      loading.value = true;
      const defectId = route.query.id;
      const attachmentId = file.uid; // 这里的uid就是attachment_id
      
      console.log(`删除缺陷ID: ${defectId} 的附件ID: ${attachmentId}`);
      
      // 调用删除附件API
      const res = await deleteAttachment(defectId, attachmentId);
      
      if (res.data.code === 200) {
        ElMessage.success('附件删除成功');
      } else {
        ElMessage.error('附件删除失败');
        return; // 删除失败不更新本地列表
      }
      loading.value = false;
    } else {
      // 新建模式或临时上传的文件，直接从列表中移除
      console.log('移除临时上传文件:', file.name);
    }
    
    // 更新本地文件列表
    fileList.value = fileList.value.filter(item => item.uid !== file.uid);
  } catch (error) {
    console.error('删除附件出错:', error);
    ElMessage.error('删除附件时发生错误');
    loading.value = false;
  }
};

// 删除文件前的确认
const beforeRemove = (file) => {
  return new Promise((resolve, reject) => {
    ElMessageBox.confirm(`确定要删除附件 "${file.name}" 吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      resolve(true);
    }).catch(() => {
      reject(new Error('取消删除'));
    });
  });
};

// 返回
const goBack = () => {
  ElMessageBox.confirm('确定要离开吗？未保存的内容将会丢失', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    router.push('/defect-list');
  }).catch(() => {});
};

// 组件销毁前，也销毁编辑器
onBeforeUnmount(() => {
  if (descriptionEditor.value) {
    descriptionEditor.value.destroy();
  }
  if (stepsEditor.value) {
    stepsEditor.value.destroy();
  }
});

onMounted(async () => {
  try {
    // 先加载选项数据
    await fetchOptions();
    console.log('选项数据加载完成:', {
      priorityOptions: priorityOptions.value,
      testPlans: testPlans.value,
      userList: userList.value,
      projectOptions: projectOptions.value
    });
    
    // 如果是编辑模式，获取缺陷详情
    if (isEdit.value) {
      await fetchDefectDetail(route.query.id);
    }
  } catch (error) {
    console.error('初始化页面数据出错:', error);
    ElMessage.error('加载页面数据失败');
  }
});

// 监听编辑器实例，当编辑器创建后，如果已有内容则设置
watch(descriptionEditor, (editor) => {
  if (editor && defectForm.description) {
    editor.setHtml(defectForm.description);
  }
});

watch(stepsEditor, (editor) => {
  if (editor && defectForm.steps) {
    editor.setHtml(defectForm.steps);
  }
});
</script>

<style scoped>
.defect-edit-container {
  padding: 20px;
  height: calc(100vh - 60px); /* 设置容器高度，减去顶部导航栏高度 */
  overflow-y: auto; /* 添加垂直方向滚动 */
}
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  position: sticky; /* 保持头部固定 */
  top: 0;
  background-color: #f5f7fa;
  z-index: 1;
  padding: 10px 0;
}
.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}
.header-actions {
  display: flex;
  gap: 12px;
}
.defect-form {
  padding: 20px 0;
}
.editor-container {
  width: 100%;
  margin-bottom: 20px;
}
.description-form-item,
.steps-form-item {
  margin-bottom: 30px;
  width: 100%;
}

/* 针对移动设备的优化 */
@media (max-width: 991px) {
  .editor-container {
    width: 100%;
  }
}
</style> 