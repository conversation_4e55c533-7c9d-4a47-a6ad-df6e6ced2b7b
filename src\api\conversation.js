import request from '@/utils/request'

// 会话管理API
export const conversationApi = {
  // 创建新会话
  createConversation(data) {
    return request.post('api/conversations', data)
  },
  
  // 获取会话列表
  getConversations(params = {}) {
    return request.get('api/conversations', { params })
  },
  
  // 获取单个会话详情
  getConversation(sessionId) {
    return request.get(`api/conversations/${sessionId}`)
  },
  
  // 更新会话信息
  updateConversation(sessionId, data) {
    return request.put(`api/conversations/${sessionId}`, data)
  },
  
  // 删除会话
  deleteConversation(sessionId) {
    return request.delete(`api/conversations/${sessionId}`)
  },
  
  // 获取会话消息历史
  getMessages(sessionId, params = {}) {
    return request.get(`api/conversations/${sessionId}/messages`, { params })
  },
  
  // 清空会话消息
  clearMessages(sessionId) {
    return request.delete(`api/conversations/${sessionId}/messages`)
  }
}

// 获取工具调用记录
export const getToolCalls = (params = {}) => {
  return request({
    url: '/api/conversations/tool-calls',
    method: 'get',
    params
  })
}

// 获取特定会话的工具调用
export const getSessionToolCalls = (sessionId, params = {}) => {
  return request({
    url: `/api/conversations/${sessionId}/tool-calls`,
    method: 'get',
    params
  })
}

// 获取工具调用统计
export const getToolCallsStatistics = (params = {}) => {
  return request({
    url: '/api/conversations/tool-calls/statistics',
    method: 'get',
    params
  })
}

export default conversationApi 