import request from '@/utils/request';

/**
 * 获取缺陷概览统计
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getDefectOverview(params = {}) {
  return request({
    url: '/api/defects/statistics/overview',
    method: 'get',
    params
  });
}

/**
 * 获取缺陷状态分布统计
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getStatusDistribution(params = {}) {
  return request({
    url: '/api/defects/statistics/status-distribution',
    method: 'get',
    params
  });
}

/**
 * 获取缺陷优先级分布统计
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getPriorityDistribution(params = {}) {
  return request({
    url: '/api/defects/statistics/priority-distribution',
    method: 'get',
    params
  });
}

/**
 * 获取缺陷严重程度分布统计
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getSeverityDistribution(params = {}) {
  return request({
    url: '/api/defects/statistics/severity-distribution',
    method: 'get',
    params
  });
}

/**
 * 获取缺陷趋势统计
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getDefectTrend(params = {}) {
  return request({
    url: '/api/defects/statistics/trend',
    method: 'get',
    params
  });
}

/**
 * 获取缺陷负责人统计
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getAssigneeStatistics(params = {}) {
  return request({
    url: '/api/defects/statistics/assignee',
    method: 'get',
    params
  });
}

/**
 * 获取缺陷项目分布统计
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getProjectDistribution(params = {}) {
  return request({
    url: '/api/defects/statistics/project-distribution',
    method: 'get',
    params
  });
}

/**
 * 获取缺陷解决时间统计
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getResolveTimeStatistics(params = {}) {
  return request({
    url: '/api/defects/statistics/resolve-time',
    method: 'get',
    params
  });
}

/**
 * 获取缺陷创建者统计
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getCreatorStatistics(params = {}) {
  return request({
    url: '/api/defects/statistics/creator',
    method: 'get',
    params
  });
}

/**
 * 获取缺陷质量指标
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getQualityMetrics(params = {}) {
  return request({
    url: '/api/defects/statistics/quality-metrics',
    method: 'get',
    params
  });
} 