import request from '@/utils/request'

// 与智能Agent聊天
export const chatWithAgent = (data) => {
  return request({
    url: 'api/intelligent-agent/chat',
    method: 'post',
    data
  })
}

// 检查API密钥状态
export const checkApiKeyStatus = () => {
  return request({
    url: 'api/intelligent-agent/api-key/status',
    method: 'get'
  })
}

// 保存API密钥配置
export const saveApiKeyConfig = (data) => {
  return request({
    url: 'api/intelligent-agent/api-key/save',
    method: 'post',
    data
  })
}

// 获取Agent使用统计
export const getAgentStats = () => {
  return request({
    url: 'api/intelligent-agent/stats',
    method: 'get'
  })
}

// 获取支持的功能列表
export const getSupportedFunctions = () => {
  return request({
    url: 'api/intelligent-agent/functions',
    method: 'get'
  })
} 