<template>
    <Home>
  <div class="agent-tester-container">
    <div class="page-header">
      <h2>🤖 智能Agent测试器</h2>
      <p class="page-description">
        基于DeepSeek AI的自动化测试管理助手，支持自然语言指令进行项目统计查询、测试计划创建、失败用例分析等操作。
      </p>
    </div>

    <!-- 快速指令区域 -->
    <el-card class="quick-commands-card">
      <template #header>
        <div class="card-header">
          <span>⚡ 快速指令</span>
        </div>
      </template>
      <div class="quick-commands">
        <el-button 
          v-for="command in quickCommands" 
          :key="command.id"
          @click="sendQuickCommand(command.text)"
          class="quick-command-btn"
          :type="command.type"
        >
          {{ command.label }}
        </el-button>
      </div>
    </el-card>

    <!-- 聊天区域 -->
    <el-card class="chat-card">
      <template #header>
        <div class="card-header conversation-header">
          <div class="header-left">
            <span>💬 对话区域</span>
            <el-tag v-if="currentConversation" type="primary" size="small" class="session-tag">
              {{ currentConversation.title }}
            </el-tag>
            <el-tag v-else type="info" size="small" class="session-tag">
              智能会话
            </el-tag>
          </div>
          <div class="header-right">
            <el-button 
              @click="loadToolCallsAnalysis" 
              :icon="Setting" 
              type="info" 
              plain 
              size="small"
              :loading="isLoadingToolCalls"
              :disabled="!currentSessionId"
            >
              {{ isLoadingToolCalls ? '分析中...' : '工具分析' }}
            </el-button>
            <el-button @click="createNewConversation" :icon="Plus" type="success" plain size="small">
              新增对话
            </el-button>
            <el-button @click="openConversationManager" :icon="FolderOpened" type="primary" plain size="small">
              会话管理
            </el-button>
            <el-button @click="clearCurrentConversation" :icon="Delete" type="danger" plain size="small">
              清空当前会话
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 消息列表 -->
      <div class="chat-messages" ref="messagesContainer">
        <div v-if="messages.length === 0" class="empty-chat">
          <el-empty description="开始与智能Agent对话吧！">
            <template #image>
              <div class="empty-icon">🤖</div>
            </template>
          </el-empty>
        </div>
        
        <div 
          v-for="(message, index) in messages" 
          :key="index" 
          :class="['message-wrapper', message.role, `message-${index}`]"
        >
          <div class="message-container">
            <div class="message-avatar">
              <el-avatar 
                :icon="message.role === 'user' ? 'User' : 'Service'"
                :size="36"
                :class="['avatar', message.role]"
              />
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="sender">
                  {{ message.role === 'user' ? '用户' : 
                     message.role === 'system' ? '系统分析' : 'AI助手' }}
                </span>
                <span class="timestamp">{{ message.timestamp }}</span>
              </div>
              <div class="message-bubble">
                <div v-if="message.role === 'user'" class="user-message">
                  {{ message.content }}
                </div>
                <div v-else-if="message.role === 'system'" class="system-message">
                  <div 
                    class="analysis-content" 
                    :class="{ 'error-message-content': isErrorMessage(message.content) }"
                    v-html="formatMessage(message.content)"
                  ></div>
                </div>
                <div v-else class="agent-message">
                  <div 
                    class="response-content" 
                    :class="{ 'error-message-content': isErrorMessage(message.content) }"
                    v-html="formatMessage(message.content)"
                  ></div>
                  
                  <!-- 函数调用结果展示 -->
                  <div v-if="message.functionCalls && message.functionCalls.length > 0" class="function-calls">
                    <div class="function-calls-header">
                      <el-icon><Setting /></el-icon>
                      <span>执行的操作</span>
                    </div>
                    <div 
                      v-for="(call, callIndex) in message.functionCalls" 
                      :key="callIndex"
                      class="function-call"
                    >
                      <div class="function-call-card">
                        <div class="function-header">
                          <div class="function-info">
                            <el-icon class="function-icon"><Setting /></el-icon>
                            <span class="function-name">{{ getFunctionDisplayName(call.function) }}</span>
                          </div>
                          <el-tag 
                            :type="call.result?.success ? 'success' : 'danger'" 
                            size="small"
                            effect="dark"
                          >
                            {{ call.result?.success ? '✓ 成功' : '✗ 失败' }}
                          </el-tag>
                        </div>
                        <div class="function-body">
                          <div class="function-section">
                            <div class="section-title">调用参数</div>
                            <div class="function-args">{{ JSON.stringify(call.arguments, null, 2) }}</div>
                          </div>
                          <div class="function-section">
                            <div class="section-title">执行结果</div>
                            <div class="function-result-content">
                              <!-- 针对特定函数类型显示格式化结果 -->
                              <div v-if="call.function === 'get_failed_test_cases'" class="formatted-result">
                                <div class="result-summary">
                                  <el-icon :class="call.result?.data?.total_count > 0 ? 'warning-icon' : 'success-icon'">
                                    {{ call.result?.data?.total_count > 0 ? '⚠️' : '✅' }}
                                  </el-icon>
                                  <span class="summary-text">
                                    找到 <strong>{{ call.result?.data?.total_count || 0 }}</strong> 个失败的测试用例
                                  </span>
                                </div>
                                <div v-if="call.result?.data?.failed_cases && call.result.data.failed_cases.length > 0" class="failed-cases-list">
                                  <div class="cases-title">失败用例详情</div>
                                  <el-table :data="call.result.data.failed_cases" size="small" class="cases-table">
                                    <el-table-column prop="id" label="用例ID" width="80" />
                                    <el-table-column prop="name" label="用例名称" min-width="150" />
                                    <el-table-column prop="status" label="状态" width="80">
                                      <template #default="scope">
                                        <el-tag type="danger" size="small">{{ scope.row.status }}</el-tag>
                                      </template>
                                    </el-table-column>
                                    <el-table-column prop="error_message" label="错误信息" min-width="200" show-overflow-tooltip />
                                    <el-table-column prop="execution_time" label="执行时间" width="120" />
                                  </el-table>
                                </div>
                              </div>
                              <div v-else class="raw-result">
                                <div class="function-result">{{ JSON.stringify(call.result, null, 2) }}</div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 流式消息显示 -->
        <div v-if="isStreaming" class="message-wrapper agent streaming-message" id="streaming-message">
          <div class="message-container">
            <div class="message-avatar">
              <el-avatar icon="Service" :size="36" class="avatar agent streaming" />
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="sender">AI助手</span>
                <span class="timestamp">实时回复中...</span>
              </div>
              <div class="message-bubble streaming-bubble">
                <div class="streaming-content">
                  <div v-if="currentStreamMessage" class="stream-text" v-html="formatStreamMessage(currentStreamMessage)"></div>
                  <div v-else class="stream-placeholder">
                    <div class="loading-dots">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                    <span class="loading-text">正在分析您的请求...</span>
                  </div>
                  <span class="typing-cursor">▋</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="isLoading" class="message-wrapper agent loading-message" id="loading-message">
          <div class="message-container">
            <div class="message-avatar">
              <el-avatar icon="Service" :size="36" class="avatar agent loading" />
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="sender">AI助手</span>
                <span class="timestamp">正在思考...</span>
              </div>
              <div class="message-bubble loading-bubble">
                <div class="loading-content">
                  <div class="loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                  </div>
                  <span class="loading-text">正在分析您的请求...</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <el-input
          v-model="currentInput"
          type="textarea"
          :rows="3"
          :placeholder="isStreaming ? 'AI正在实时回复中，请稍候...' : '请输入您的指令，例如：查看项目1的统计信息'"
          @keydown.ctrl.enter="sendMessage"
          :disabled="isLoading || isStreaming"
        />
        <div class="input-actions">
          <div class="input-tip">
            <el-icon><InfoFilled /></el-icon>
            <span>支持的指令类型：项目统计查询、测试计划创建、失败用例分析</span>
          </div>
          <el-button 
            type="primary" 
            @click="sendMessage"
            :loading="isLoading || isStreaming"
            :disabled="!currentInput.trim() || isStreaming"
          >
            <el-icon><Promotion /></el-icon>
            {{ isStreaming ? '实时回复中...' : '发送 (Ctrl+Enter)' }}
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 使用说明卡片 -->
    <el-card class="help-card">
      <template #header>
        <div class="card-header">
          <span>📖 使用说明</span>
          <el-button @click="showHelp = !showHelp" type="text">
            {{ showHelp ? '收起' : '展开' }}
          </el-button>
        </div>
      </template>
      <div v-show="showHelp">
        <el-row :gutter="20">
          <el-col :span="12">
            <h4>🎯 支持的指令类型</h4>
            <ul class="help-list">
              <li><strong>项目统计查询</strong> - "查看项目X的统计信息"</li>
              <li><strong>缺陷分析</strong> - "分析项目X的缺陷情况"</li>
              <li><strong>测试计划创建</strong> - "为项目X创建测试计划"</li>
              <li><strong>失败用例分析</strong> - "查看项目X的失败用例"</li>
              <li><strong>健康度报告</strong> - "生成项目X的健康度报告"</li>
            </ul>
          </el-col>
          <el-col :span="12">
            <h4>💡 使用技巧</h4>
            <ul class="help-list">
              <li>使用具体的项目ID（如项目5）获得更准确的结果</li>
              <li>可以指定时间范围，如"最近一周"、"最近24小时"</li>
              <li>询问具体的分析维度，如"缺陷严重程度分布"</li>
              <li>可以要求详细的数据展示和图表分析</li>
              <li>支持批量操作和对比分析</li>
            </ul>
          </el-col>
        </el-row>
        
        <el-divider />
        
        <h4>🔧 API响应示例</h4>
        <el-alert
          title="上次测试结果显示项目5在最近24小时内没有失败的测试用例，这是一个好消息！"
          type="success"
          :closable="false"
          class="example-alert"
        />
        <p class="help-text">
          Agent会自动调用相应的API函数获取数据，并以结构化的方式展示结果。
          如果需要更详细的缺陷分析，可以尝试询问："分析项目5的所有缺陷情况"
        </p>
      </div>
    </el-card>

    <!-- 会话管理对话框 -->
    <el-dialog 
      v-model="showConversations" 
      title="会话管理" 
      width="800px"
      :before-close="() => { showConversations = false }"
    >
            <div class="conversations-header">
        <div class="header-left">
          <el-button @click="createNewConversation" :icon="Plus" type="primary" size="small">
            新增对话
          </el-button>
          <span v-if="conversationsPagination && !isLoadingConversations" class="header-info">
            第 {{ currentPage }} 页 / 共 {{ Math.ceil(conversationsPagination.total / pageSize) }} 页
          </span>
        </div>
        <div class="header-right">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索会话标题..."
            size="small"
            style="width: 150px"
            clearable
            @keyup.enter="handleSearch"
            @clear="handleSearch"
            :disabled="isLoadingConversations"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select 
            v-model="pageSize" 
            size="small" 
            style="width: 80px"
            @change="handlePageSizeChange"
            :disabled="isLoadingConversations"
          >
            <el-option label="10" :value="10" />
            <el-option label="20" :value="20" />
            <el-option label="50" :value="50" />
          </el-select>
          <el-button @click="loadConversations(currentPage)" :loading="isLoadingConversations" size="small" plain>
            {{ isLoadingConversations ? '加载中...' : '刷新' }}
          </el-button>
        </div>
      </div>
      <div class="conversations-list">
        <!-- 加载状态 -->
        <div v-if="isLoadingConversations" class="loading-conversations">
          <el-skeleton :rows="3" animated />
          <p style="text-align: center; color: #909399; margin-top: 16px;">
            正在加载会话列表...
          </p>
        </div>
        
        <!-- 空状态 -->
        <el-empty v-else-if="conversations.length === 0" description="暂无会话记录">
          <p style="color: #909399; font-size: 14px; margin-top: 16px;">
            发送第一条消息时会自动创建会话
          </p>
          <p style="color: #909399; font-size: 12px; margin-top: 8px;">
            请检查网络连接或后端服务状态
          </p>
        </el-empty>
        <div v-else class="conversation-items">
          <div 
            v-for="conversation in conversations" 
            :key="conversation.id"
            class="conversation-item"
            :class="{ active: currentSessionId === conversation.id }"
            @click="switchConversation(conversation)"
          >
            <div class="conversation-info">
              <div class="conversation-title">
                <el-icon><ChatDotRound /></el-icon>
                {{ conversation.title }}
              </div>
              <div class="conversation-meta">
                <span class="conversation-time">
                  {{ new Date(conversation.updated_at || conversation.create_time).toLocaleString() }}
                </span>
                <span class="conversation-stats">
                  {{ conversation.message_count || 0 }} 条消息
                </span>
                <span v-if="conversation.project_name" class="conversation-project">
                  📁 {{ conversation.project_name }}
                </span>
              </div>
              <div v-if="conversation.latest_message" class="conversation-preview">
                {{ formatConversationPreview(conversation.latest_message) }}
              </div>
            </div>
            <div class="conversation-actions">
              <el-button 
                type="danger" 
                size="small" 
                @click.stop="deleteConversation(conversation)"
                :icon="Delete"
                circle
              />
            </div>
          </div>
        </div>
        
        <!-- 分页信息和控制 -->
        <div v-if="conversationsPagination && !isLoadingConversations" class="conversations-pagination">
          <el-divider />
          
          <!-- 分页信息 -->
          <div class="pagination-info">
            <span class="pagination-text">
              显示第 {{ conversationsPagination.page }} 页，共 {{ conversationsPagination.total }} 条会话
            </span>
            <span v-if="conversationsPagination.has_more" class="pagination-more">
              还有更多会话...
            </span>
          </div>
          
          <!-- 分页控制 -->
          <div class="pagination-controls">
            <div class="quick-nav">
              <el-button 
                size="small" 
                plain 
                :disabled="currentPage === 1"
                @click="handlePageChange(1)"
              >
                首页
              </el-button>
              <el-button 
                size="small" 
                plain 
                :disabled="!conversationsPagination.has_more"
                @click="handlePageChange(Math.ceil(conversationsPagination.total / pageSize))"
              >
                末页
              </el-button>
            </div>
            
            <el-pagination
              v-model:current-page="currentPage"
              :page-size="pageSize"
              :total="conversationsPagination.total"
              :page-count="Math.ceil(conversationsPagination.total / pageSize)"
              layout="prev, pager, next, jumper"
              :small="true"
              :hide-on-single-page="false"
              @current-change="handlePageChange"
              class="conversations-paginator"
            />
          </div>
        </div>
      </div>
    </el-dialog>


  </div>
  </Home>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Delete, InfoFilled, Promotion, Setting, ChatDotRound, FolderOpened, Plus, Search } from '@element-plus/icons-vue'
import request from '@/utils/request'
import conversationApi, { getSessionToolCalls, getToolCallsStatistics } from '@/api/conversation'
import Home from '@/components/HomePage.vue'
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'

// 响应式数据
const isLoading = ref(false)
const isStreaming = ref(false)
const currentStreamMessage = ref('')
const currentInput = ref('')
const messages = ref([])
const messagesContainer = ref(null)
const showHelp = ref(false)

// 会话管理相关
const conversations = ref([])
const currentSessionId = ref(null)
const currentConversation = ref(null)
const showConversations = ref(false)

// 工具调用分析相关
const toolCallsAnalysis = ref(null)
const showToolCallsAnalysis = ref(false)
const isLoadingToolCalls = ref(false)

// 会话管理加载状态
const isLoadingConversations = ref(false)
const conversationsPagination = ref(null)
const currentPage = ref(1)
const pageSize = ref(20)
const searchKeyword = ref('')

// API基础配置
const apiBaseURL = import.meta.env.VITE_API_BASE_URL || 'http://127.0.0.1:8081/api'

// 快速指令
const quickCommands = ref([
  { id: 1, label: '项目5统计信息', text: '查看项目5的统计信息，请以结构化的方式展示', type: 'primary' },
  { id: 2, label: '项目5缺陷分析', text: '分析项目5的缺陷情况，包括缺陷数量、严重程度分布和解决状态，请以markdown格式详细展示', type: 'success' },
  { id: 3, label: '创建回归测试计划', text: '为项目5创建一个测试计划，名称为回归测试', type: 'warning' },
  { id: 4, label: '查看失败用例', text: '查看项目5最近一周的失败测试用例，分析失败原因，请提供详细的分析报告', type: 'danger' },
  { id: 5, label: '项目健康度报告', text: '生成项目5的健康度报告，包括测试覆盖率、通过率和缺陷趋势，请以markdown格式制作详细报告', type: 'info' },
  { id: 6, label: '智能分析失败用例', text: '帮我分析测试用例123为什么失败了，并给出详细的修复建议', type: 'warning' },
  { id: 7, label: '查看测试用例建议', text: '显示测试用例456的所有AI编辑建议，包括待处理和已应用的', type: 'primary' },
  { id: 8, label: '批量分析失败用例', text: '分析项目5所有失败的测试用例，生成智能修复建议报告', type: 'danger' }
])



// 监听消息变化，自动滚动
watch(messages, (newMessages, oldMessages) => {
  if (newMessages.length > oldMessages.length) {
    // 有新消息时，延迟滚动以确保DOM更新
    setTimeout(() => {
      const lastMessage = newMessages[newMessages.length - 1]
      // 如果是用户消息，快速滚动到底部
      if (lastMessage.role === 'user') {
        scrollToBottom(false)
      } 
      // 如果是AI消息，使用智能滚动
      else if (lastMessage.role === 'agent') {
        smartScroll()
      }
    }, 100)
  }
}, { deep: true })

// 监听loading状态变化
watch(isLoading, (newLoading, oldLoading) => {
  if (newLoading && !oldLoading) {
    // 开始loading时，滚动到loading元素
    setTimeout(() => {
      scrollToLoading()
    }, 100)
  } else if (!newLoading && oldLoading) {
    // loading结束时，使用智能滚动到最新回复
    setTimeout(() => {
      smartScroll()
    }, 300)
  }
})

// 监听流式传输状态变化
watch(isStreaming, (newStreaming, oldStreaming) => {
  if (newStreaming && !oldStreaming) {
    // 开始流式传输时，滚动到流式消息元素
    setTimeout(() => {
      const streamingElement = document.getElementById('streaming-message')
      if (streamingElement) {
        streamingElement.scrollIntoView({
          behavior: 'smooth',
          block: 'end',
          inline: 'nearest'
        })
      }
    }, 100)
  }
})

// 监听流式消息内容变化，实时滚动
watch(currentStreamMessage, () => {
  if (isStreaming.value) {
    nextTick(() => {
      scrollToBottom(true)
    })
  }
})

// 初始化
onMounted(async () => {
  // 测试markdown渲染
  const testMarkdown = `# 测试标题
  
**粗体文本** 和 *斜体文本*

| 指标 | 数量 | 占比 |
|------|------|------|
| 总数 | 14 | 100% |
| 通过 | 10 | 71.4% |
| 失败 | 2 | 14.3% |

\`\`\`javascript
console.log('test');
\`\`\`

- 列表项1
- 列表项2`

  // 测试包裹在markdown代码块中的内容
  const wrappedMarkdown = `\`\`\`markdown
${testMarkdown}
\`\`\``

  console.log('=== Markdown渲染测试开始 ===')
  console.log('原始内容:', testMarkdown)
  
  // 测试1: 普通markdown渲染
  try {
    const rendered = marked.parse(testMarkdown)
    console.log('✅ 普通markdown渲染成功:', rendered.substring(0, 500) + (rendered.length > 500 ? '...' : ''))
    
    // 检查表格是否正确渲染
    if (rendered.includes('<table') && rendered.includes('<thead>') && rendered.includes('<tbody>')) {
      console.log('✅ 表格渲染正常')
      
      // 进一步检查表格内容
      const tableMatch = rendered.match(/<table[^>]*>(.*?)<\/table>/s)
      if (tableMatch) {
        const tableContent = tableMatch[1]
        const hasHeaders = tableContent.includes('<th>')
        const hasCells = tableContent.includes('<td>')
        const hasCorrectData = tableContent.includes('71.4%') || tableContent.includes('14.3%')
        
        console.log('表格详细检查:', {
          hasHeaders,
          hasCells,
          hasCorrectData,
          tablePreview: tableContent.substring(0, 200) + '...'
        })
        
        if (hasHeaders && hasCells && hasCorrectData) {
          console.log('🎉 表格内容验证通过')
        } else {
          console.warn('⚠️ 表格内容可能有问题')
        }
      }
    } else {
      console.error('❌ 表格渲染异常')
      console.error('渲染结果:', rendered)
    }
  } catch (error) {
    console.error('❌ 普通markdown渲染失败:', error)
  }
  
  // 测试2: 包裹在代码块中的markdown渲染
  console.log('=== 测试包裹在代码块中的markdown ===')
  console.log('包裹的内容:', wrappedMarkdown)
  try {
    const processedByFormatMessage = formatMessage(wrappedMarkdown)
    console.log('✅ formatMessage处理结果:', processedByFormatMessage.substring(0, 500) + (processedByFormatMessage.length > 500 ? '...' : ''))
    
    const processedByFormatStreamMessage = formatStreamMessage(wrappedMarkdown)
    console.log('✅ formatStreamMessage处理结果:', processedByFormatStreamMessage.substring(0, 500) + (processedByFormatStreamMessage.length > 500 ? '...' : ''))
    
    if (processedByFormatMessage.includes('<table') && processedByFormatStreamMessage.includes('<table')) {
      console.log('🎉 包裹测试通过：两个函数都能正确提取并渲染markdown')
    } else {
      console.warn('⚠️ 包裹测试失败：函数无法正确处理包裹的markdown')
    }
  } catch (error) {
    console.error('❌ 包裹markdown测试失败:', error)
  }
  
  console.log('=== Markdown渲染测试完成 ===')

  // 加载会话列表
  await loadConversations()
  
  // 页面刷新后，总是显示欢迎消息，不自动加载历史会话
  console.log('页面初始化，当前会话状态:', {
    sessionId: currentSessionId.value,
    conversation: currentConversation.value
  })
  
  // 确保页面刷新后消息区域是空白的
  messages.value = []
  
  // 添加欢迎消息，提示用户可以开始新对话或选择历史会话
  messages.value.push({
    role: 'agent',
    content: '您好！我是智能Agent助手，可以帮助您进行项目统计查询、测试计划创建、失败用例分析等操作。\n\n💡 **智能会话**: 现在支持多轮对话上下文管理！\n- 直接发送消息，系统会自动创建新会话\n- 点击"会话管理"可以查看和切换到历史会话\n- 在同一会话中进行连续对话，我会记住之前的内容\n- 使用"刚才"、"前面提到的"等词语引用之前的对话\n\n🔧 **Markdown支持**: 现在支持完整的Markdown渲染，包括表格、代码块、列表等格式！\n\n直接开始对话吧！',
    timestamp: new Date().toLocaleTimeString()
  })
  
  // 重置会话状态，确保刷新后是全新状态
  currentSessionId.value = null
  currentConversation.value = null
  
  console.log('页面初始化完成，会话状态已重置为空')
})

// 发送快速指令
const sendQuickCommand = async (command) => {
  currentInput.value = command
  sendMessage()
}

// 自动创建会话函数
const autoCreateConversation = async (userInput) => {
  try {
    // 根据用户输入智能生成会话标题
    let title = '智能对话'
    if (userInput.includes('项目') && userInput.match(/\d+/)) {
      const projectId = userInput.match(/\d+/)[0]
      title = `项目${projectId}相关查询`
    } else if (userInput.includes('缺陷') || userInput.includes('bug')) {
      title = '缺陷分析会话'
    } else if (userInput.includes('测试') && userInput.includes('计划')) {
      title = '测试计划会话'
    } else if (userInput.includes('失败') || userInput.includes('错误')) {
      title = '失败用例分析'
    } else if (userInput.includes('统计') || userInput.includes('数据')) {
      title = '数据统计分析'
    } else {
      // 使用用户输入的前15个字符作为标题
      title = userInput.length > 15 ? userInput.substring(0, 15) + '...' : userInput
    }

    const response = await conversationApi.createConversation({
      title: title,
      project_id: 1
    })

    console.log('自动创建会话响应:', response.data)
    console.log('响应状态:', response.status)
    console.log('完整响应对象:', response)

    // 更宽泛的成功判断条件
    const isSuccess = response.status === 200 || response.status === 201 || 
                     response.data.code === 200 || response.data.success === true ||
                     (response.data && (response.data.id || response.data.session_id)) // 支持id或session_id字段

    if (isSuccess) {
      const rawData = response.data.data || response.data
      
      console.log('解析的会话数据:', rawData)
      
      // 标准化会话数据格式，将session_id映射为id
      const newConversation = {
        id: rawData.session_id || rawData.id, // 优先使用session_id，回退到id
        session_id: rawData.session_id || rawData.id,
        title: rawData.title || '新会话',
        create_time: rawData.create_time || rawData.created_at,
        project_id: rawData.project_id,
        project_name: rawData.project_name
      }
      
      console.log('标准化后的会话数据:', newConversation)
      
      // 确保会话数据有id字段
      if (newConversation && newConversation.id) {
        conversations.value.unshift(newConversation)
        currentSessionId.value = newConversation.id
        currentConversation.value = newConversation
        console.log('自动创建会话成功，ID:', newConversation.id)
        return true
      } else {
        console.error('会话数据缺少ID字段:', newConversation)
        return false
      }
    } else {
      console.error('自动创建会话失败:', response.data.message || response.data.error || '未知错误')
      console.error('失败的响应数据:', response.data)
      return false
    }
  } catch (error) {
    console.error('自动创建会话异常:', error)
    return false
  }
}

// 流式传输处理函数
const sendStreamMessage = async () => {
  if (!currentInput.value.trim() || isStreaming.value) {
    return
  }
  
  // 如果没有会话ID，自动创建会话
  if (!currentSessionId.value) {
    console.log('没有会话ID，自动创建会话...')
    try {
      const created = await autoCreateConversation(currentInput.value.trim())
      if (!created) {
        console.error('自动创建会话失败')
        ElMessage.error('创建会话失败，请稍后重试')
        return
      }
      console.log('会话创建成功，继续发送消息')
    } catch (error) {
      console.error('创建会话时发生错误:', error)
      ElMessage.error('创建会话失败：' + (error.message || '未知错误'))
      return
    }
  }
  
  const userMessage = {
    role: 'user',
    content: currentInput.value.trim(),
    timestamp: new Date().toLocaleTimeString()
  }
  
  messages.value.push(userMessage)
  const userInput = currentInput.value.trim()
  currentInput.value = ''
  
  isStreaming.value = true
  currentStreamMessage.value = ''
  
  try {
    // 获取token
    const token = localStorage.getItem('token') || sessionStorage.getItem('token')
    
    const response = await fetch('http://127.0.0.1:8081/api/intelligent-agent/chat', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
      },
      body: JSON.stringify({
        input: userInput,
        stream: true, // 启用流式传输
        session_id: currentSessionId.value // 添加会话ID
      })
    })

    console.log('发送请求:', {
      url: 'http://127.0.0.1:8081/api/intelligent-agent/chat',
      input: userInput,
      session_id: currentSessionId.value,
      stream: true
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let accumulatedMessage = ''
    let functionCalls = []
    let originalMessage = ''

    while (true) {
      const { done, value } = await reader.read()
      if (done) break

      const chunk = decoder.decode(value, { stream: true })
      const lines = chunk.split('\n')

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const dataStr = line.slice(6).trim()
          if (dataStr && dataStr !== '[DONE]') {
            try {
              const data = JSON.parse(dataStr)
              
              if (data.type === 'content') {
                // 确保 data.content 是字符串
                const contentStr = typeof data.content === 'string' ? data.content : String(data.content || '')
                
                // 检查content是否为空或无效
                if (contentStr) {
                  accumulatedMessage += contentStr
                  currentStreamMessage.value = accumulatedMessage
                  
                  // 调试日志
                  console.log('流式内容片段:', JSON.stringify(contentStr))
                  console.log('累积内容长度:', accumulatedMessage.length)
                  
                  // 检查关键标记
                  if (contentStr.includes('markdown') || contentStr.includes('```')) {
                    console.log('🔍 检测到关键标记:', contentStr)
                  }
                  
                  if (accumulatedMessage.length % 1000 === 0) {
                    console.log('累积内容预览:', accumulatedMessage.substring(0, 200) + '...')
                  }
                  
                  // 在累积到一定长度时检查markdown结构
                  if (accumulatedMessage.length > 100 && accumulatedMessage.includes('```markdown')) {
                    console.log('🎯 检测到markdown代码块结构，当前累积内容:', accumulatedMessage.substring(0, 300) + '...')
                  }
                } else {
                  console.warn('收到空的或无效的内容片段:', data.content, typeof data.content)
                }
              } else if (data.type === 'function_call') {
                functionCalls.push(data.function_call)
              } else if (data.type === 'original_message') {
                originalMessage = data.content
              } else if (data.type === 'done') {
                // 流式传输完成，添加完整消息
                // 确保最终内容是字符串
                const finalContent = typeof accumulatedMessage === 'string' ? accumulatedMessage : String(accumulatedMessage || '')
                
                console.log('=== 流式传输完成 ===')
                console.log('累积的原始内容:', finalContent.substring(0, 500) + (finalContent.length > 500 ? '...' : ''))
                console.log('累积内容长度:', finalContent.length)
                console.log('是否包含markdown代码块:', finalContent.includes('```markdown'))
                
                const agentMessage = {
                  role: 'agent',
                  content: finalContent || '处理完成',
                  functionCalls: functionCalls,
                  originalMessage: originalMessage,
                  timestamp: new Date().toLocaleTimeString()
                }
                messages.value.push(agentMessage)
                currentStreamMessage.value = ''
                isStreaming.value = false
                
                console.log('保存的消息内容:', agentMessage.content.substring(0, 300) + (agentMessage.content.length > 300 ? '...' : ''))
                
                // 对话完成后，获取并展示本次工具调用分析
                await loadCurrentRoundToolCalls()
                return
              } else if (data.type === 'error') {
                console.error('AI错误:', data.content)
                currentStreamMessage.value += `\n❌ 错误: ${data.content}`
                setTimeout(() => {
                  const errorMessage = {
                    role: 'agent',
                    content: `❌ 处理错误: ${data.content}`,
                    timestamp: new Date().toLocaleTimeString()
                  }
                  messages.value.push(errorMessage)
                  currentStreamMessage.value = ''
                  isStreaming.value = false
                }, 1000)
                return
              }
            } catch (e) {
              console.warn('解析SSE数据失败:', dataStr, e)
            }
          }
        }
      }
    }
  } catch (error) {
    console.error('流式传输失败:', error)
    const errorMessage = {
      role: 'agent',
      content: `❌ 网络错误: ${error.message}`,
      timestamp: new Date().toLocaleTimeString()
    }
    messages.value.push(errorMessage)
  } finally {
    currentStreamMessage.value = ''
    isStreaming.value = false
  }
}

// 发送消息 - 支持流式和非流式
const sendMessage = async () => {
  // 优先使用流式传输
  await sendStreamMessage()
}

// 会话管理方法
const loadConversations = async (page = 1) => {
  try {
    console.log(`开始加载会话列表... 第${page}页`)
    isLoadingConversations.value = true
    
    // 更新当前页码
    currentPage.value = page
    
    const params = {
      page: page,
      page_size: pageSize.value
    }
    
    // 如果有搜索关键词，添加到参数中
    if (searchKeyword.value.trim()) {
      params.search = searchKeyword.value.trim()
    }
    
    const response = await conversationApi.getConversations(params)
    console.log('加载会话列表完整响应:', response)
    console.log('会话列表响应状态:', response.status)
    console.log('会话列表响应数据:', response.data)
    
    // 更宽泛的成功判断
    const isSuccess = response.status === 200 || response.status === 201 || 
                     (response.data && (
                       response.data.code === 200 || response.data.success === true || 
                       Array.isArray(response.data) || Array.isArray(response.data.data)
                     ))
    
    if (isSuccess) {
      // 尝试多种数据结构
      let rawConversations = []
      
      if (Array.isArray(response.data)) {
        rawConversations = response.data
      } else if (response.data.data && Array.isArray(response.data.data.sessions)) {
        // 新的API格式：data.sessions 数组
        rawConversations = response.data.data.sessions
      } else if (Array.isArray(response.data.data)) {
        rawConversations = response.data.data
      } else if (response.data.conversations && Array.isArray(response.data.conversations)) {
        rawConversations = response.data.conversations
      } else if (response.data.sessions && Array.isArray(response.data.sessions)) {
        // 直接在 data 下的 sessions 字段
        rawConversations = response.data.sessions
      } else if (response.data.code === 200 && response.data.data) {
        rawConversations = response.data.data
      } else {
        rawConversations = []
      }
      
      console.log('解析的原始会话数据:', rawConversations)
      
      // 确保是数组并过滤无效数据
      if (!Array.isArray(rawConversations)) {
        console.warn('会话数据不是数组格式:', rawConversations)
        conversations.value = []
      } else {
        // 标准化会话数据格式，过滤掉无效的会话
        conversations.value = rawConversations
          .filter(conv => {
            // 确保会话有有效的ID
            const hasValidId = conv && (conv.session_id || conv.id)
            if (!hasValidId) {
              console.warn('过滤掉无效会话（缺少ID）:', conv)
            }
            return hasValidId
          })
          .map(conv => {
            const standardizedConv = {
              id: conv.session_id || conv.id, // 优先使用session_id，回退到id
              session_id: conv.session_id || conv.id,
              title: conv.title || conv.name || '新会话',
              create_time: conv.create_time || conv.created_at || conv.createdAt,
              updated_at: conv.update_time || conv.updated_at || conv.updatedAt || conv.last_message_time,
              project_id: conv.project_id || conv.projectId,
              project_name: conv.project_name || conv.projectName,
              message_count: conv.message_count || conv.messageCount || 0,
              latest_message: conv.latest_message || conv.lastMessage,
              total_tokens: conv.total_tokens || conv.totalTokens || 0,
              last_message_time: conv.last_message_time || conv.lastMessageTime
            }
            
            // 确保ID字段存在
            if (!standardizedConv.id) {
              console.error('会话标准化后仍然缺少ID:', conv, standardizedConv)
            }
            
            return standardizedConv
          })
      }
      
      // 保存分页信息
      if (response.data.data && response.data.data.pagination) {
        conversationsPagination.value = response.data.data.pagination
      }
      
      console.log('加载的会话数量:', conversations.value.length)
      
      // 验证会话数据的完整性
      const invalidConversations = conversations.value.filter(conv => !conv.id)
      if (invalidConversations.length > 0) {
        console.error('发现无效会话（缺少ID）:', invalidConversations)
        // 移除无效会话
        conversations.value = conversations.value.filter(conv => conv.id)
      }
      
      // 页面刷新后不自动选择历史会话，让用户主动选择或创建新会话
      // 这样确保刷新后是全新的状态，避免session_id混乱
      console.log('页面刷新后保持空白状态，不自动选择历史会话')
      console.log('当前会话状态:', {
        conversationsCount: conversations.value.length,
        currentSessionId: currentSessionId.value,
        currentConversation: currentConversation.value
      })
      
      if (conversations.value.length === 0) {
        console.log('没有找到任何会话')
      }
      
    } else {
      console.error('加载会话列表失败 - 响应格式不符合预期')
      console.error('失败的响应状态:', response.status)
      console.error('失败的响应数据:', response.data)
      
      // 提取错误信息
      const errorMsg = response.data?.message || response.data?.error || 
                      (response.status ? `HTTP ${response.status}` : '未知错误')
      
      ElMessage.error(`加载会话列表失败: ${errorMsg}`)
    }
    
  } catch (error) {
    console.error('加载会话列表异常详情:', error)
    console.error('错误堆栈:', error.stack)
    
    // 提供更详细的错误信息
    let errorMsg = '加载会话列表失败'
    if (error.response) {
      // HTTP错误
      const status = error.response.status
      const statusText = error.response.statusText
      errorMsg = `加载会话列表失败: HTTP ${status} ${statusText}`
      
      if (error.response.data && error.response.data.message) {
        errorMsg += ` - ${error.response.data.message}`
      }
    } else if (error.message) {
      errorMsg = `加载会话列表失败: ${error.message}`
    }
    
    ElMessage.error(errorMsg)
    
    // 网络错误时设置空列表
    conversations.value = []
  } finally {
    isLoadingConversations.value = false
  }
}

const switchConversation = async (conversation) => {
  try {
    console.log('=== 开始切换会话 ===')
    console.log('传入的会话对象:', conversation)
    // 验证会话对象和ID
    if (!conversation || (!conversation.id && !conversation.session_id)) {
      console.error('会话对象无效:', conversation)
      ElMessage.error('无效的会话对象')
      return
    }
    
    // 使用合适的ID字段
    const sessionId = conversation.id || conversation.session_id
    console.log('切换到会话ID:', sessionId, '标题:', conversation.title)
    
    currentSessionId.value = sessionId
    currentConversation.value = conversation
    
    console.log('开始加载会话消息历史...')
    
    // 加载会话消息历史
    const response = await conversationApi.getMessages(sessionId)
    console.log('加载消息历史完整响应:', response)
    console.log('响应状态码:', response.status)
    console.log('响应数据:', response.data)
    
    // 更宽泛的成功判断
    const isSuccess = response.status === 200 || response.status === 201 || 
                     (response.data && (
                       response.data.code === 200 || response.data.success === true ||
                       Array.isArray(response.data) || Array.isArray(response.data.data)
                     ))
    
    if (isSuccess) {
      // 尝试多种数据结构
      let messageHistory = []
      
      console.log('开始解析响应数据结构...')
      
      if (Array.isArray(response.data)) {
        console.log('响应数据本身就是数组')
        messageHistory = response.data
      } else if (response.data && typeof response.data === 'object') {
        console.log('响应数据是对象，尝试提取消息数组...')
        
        // 尝试多种可能的字段名
        const possibleFields = ['data', 'messages', 'items', 'list', 'results']
        
        for (const field of possibleFields) {
          if (Array.isArray(response.data[field])) {
            console.log(`找到消息数组字段: ${field}`)
            messageHistory = response.data[field]
            break
          }
        }
        
        // 如果还没找到，检查是否有嵌套结构
        if (messageHistory.length === 0 && response.data.data && typeof response.data.data === 'object') {
          console.log('检查嵌套的data对象...')
          for (const field of possibleFields) {
            if (Array.isArray(response.data.data[field])) {
              console.log(`在嵌套对象中找到消息数组字段: data.${field}`)
              messageHistory = response.data.data[field]
              break
            }
          }
        }
        
        // 最后的兜底处理
        if (messageHistory.length === 0) {
          console.log('未找到有效的消息数组，使用空数组')
          messageHistory = []
        }
      } else {
        console.log('响应数据格式异常，使用空数组')
        messageHistory = []
      }
      
      console.log('解析的消息历史:', messageHistory)
      console.log('messageHistory的类型:', typeof messageHistory)
      console.log('messageHistory是否为数组:', Array.isArray(messageHistory))
      
      // 确保messageHistory是数组
      if (!Array.isArray(messageHistory)) {
        console.warn('messageHistory不是数组，尝试转换:', messageHistory)
        // 如果是对象，尝试提取数组
        if (messageHistory && typeof messageHistory === 'object') {
          // 尝试常见的数组字段
          if (Array.isArray(messageHistory.messages)) {
            messageHistory = messageHistory.messages
          } else if (Array.isArray(messageHistory.data)) {
            messageHistory = messageHistory.data
          } else if (Array.isArray(messageHistory.items)) {
            messageHistory = messageHistory.items
          } else {
            // 如果都不是，设为空数组
            console.warn('无法从对象中提取消息数组，使用空数组')
            messageHistory = []
          }
        } else {
          // 如果不是对象，设为空数组
          console.warn('messageHistory既不是数组也不是对象，使用空数组')
          messageHistory = []
        }
      }
      
      // 格式化消息历史，添加错误处理
      try {
        messages.value = messageHistory.map(msg => {
          // 确保msg是对象
          if (!msg || typeof msg !== 'object') {
            console.warn('异常的消息对象:', msg)
            return {
              role: 'user',
              content: String(msg || ''),
              functionCalls: [],
              originalMessage: '',
              timestamp: new Date().toLocaleTimeString()
            }
          }
          
          let content = msg.content || msg.message || ''
          
          // 检测并处理错误信息
          if (typeof content === 'string' && content.trim()) {
            const errorPatterns = [
              '处理失败',
              '系统错误',
              'Internal Server Error',
              'Service Unavailable',
              'Network Error',
              'Request failed',
              'API调用失败',
              '接口调用失败'
            ]
            
            const isErrorMessage = errorPatterns.some(pattern => 
              content.toLowerCase().includes(pattern.toLowerCase())
            )
            
            if (isErrorMessage) {
              console.log('检测到错误消息，进行美化处理:', content)
              // 根据错误类型提供不同的友好提示
              let friendlyMessage = ''
              if (content.includes('处理失败')) {
                friendlyMessage = `⚠️ **历史系统消息**：${content}\n\n💡 *这是之前会话中的一个系统错误记录。当前系统运行正常，您可以继续正常使用所有功能。*`
              } else if (content.includes('网络') || content.includes('Network')) {
                friendlyMessage = `🌐 **网络问题记录**：${content}\n\n✅ *这是之前的网络连接问题记录，不影响当前的功能使用。*`
              } else {
                friendlyMessage = `⚠️ **系统提示**：${content}\n\n*这是之前会话中的一个系统错误信息，不影响当前对话功能。*`
              }
              content = friendlyMessage
            }
          }
          
          return {
            role: msg.role || 'user',
            content: content,
            functionCalls: msg.function_calls || msg.functionCalls || [],
            originalMessage: msg.original_message || msg.originalMessage || '',
            timestamp: msg.created_at 
              ? new Date(msg.created_at).toLocaleTimeString() 
              : msg.timestamp || new Date().toLocaleTimeString()
          }
        }).filter(msg => {
          // 过滤掉完全空白的消息
          return msg.content && msg.content.trim().length > 0
        })
      } catch (mapError) {
        console.error('格式化消息历史时出错:', mapError)
        console.error('problematic messageHistory:', messageHistory)
        messages.value = []
        ElMessage.warning('消息历史格式异常，已清空显示')
      }
      
      console.log('格式化后的消息数量:', messages.value.length)
      
      showConversations.value = false
      ElMessage.success(`已切换到会话: ${conversation.title}`)
      
      // 滚动到底部
      setTimeout(() => {
        scrollToBottom(false)
      }, 100)
      
    } else {
      console.error('加载消息历史失败 - 响应格式不符合预期')
      console.error('响应数据:', response.data)
      
      // 即使加载历史失败，也允许切换到会话
      messages.value = []
      showConversations.value = false
      
      ElMessage.warning(`已切换到会话: ${conversation.title}，但历史消息加载失败`)
    }
    
  } catch (error) {
    console.error('切换会话异常详情:', error)
    console.error('错误堆栈:', error.stack)
    console.error('会话对象:', conversation)
    
    // 提供更详细的错误信息
    let errorMsg = '切换会话失败'
    if (error.response) {
      // HTTP错误
      const status = error.response.status
      const statusText = error.response.statusText
      errorMsg = `切换会话失败: HTTP ${status} ${statusText}`
      
      if (error.response.data && error.response.data.message) {
        errorMsg += ` - ${error.response.data.message}`
      }
    } else if (error.message) {
      errorMsg = `切换会话失败: ${error.message}`
    }
    
    ElMessage.error(errorMsg)
    
    // 如果是网络错误，仍然允许切换到会话（但清空消息）
    if (!error.response || error.response.status >= 500) {
      currentSessionId.value = conversation.id || conversation.session_id
      currentConversation.value = conversation
      messages.value = []
      showConversations.value = false
      
      ElMessage.warning(`已切换到会话: ${conversation.title}，但无法加载历史消息`)
    }
  }
}

const deleteConversation = async (conversation) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除会话 "${conversation.title}" 吗？此操作不可撤销。`,
      '删除会话',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const response = await conversationApi.deleteConversation(conversation.id)
    if (response.data.code === 200) {
      conversations.value = conversations.value.filter(c => c.id !== conversation.id)
      
      if (currentSessionId.value === conversation.id) {
        currentSessionId.value = null
        currentConversation.value = null
        messages.value = []
      }
      
      ElMessage.success('删除会话成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除会话失败:', error)
      ElMessage.error('删除会话失败')
    }
  }
}

// 创建新对话
const createNewConversation = async () => {
  try {
    console.log('手动创建新对话...')
    
    // 询问用户输入对话标题
    const { value: title } = await ElMessageBox.prompt(
      '请输入新对话的标题',
      '创建新对话',
      {
        confirmButtonText: '创建',
        cancelButtonText: '取消',
        inputPattern: /\S+/,
        inputErrorMessage: '对话标题不能为空',
        inputPlaceholder: '例如：项目分析、测试计划讨论等'
      }
    )
    
    if (!title || !title.trim()) {
      ElMessage.warning('对话标题不能为空')
      return
    }
    
    // 调用API创建新会话
    const response = await conversationApi.createConversation({
      title: title.trim(),
      project_id: 1 // 默认项目ID，可以根据需要修改
    })
    
    console.log('手动创建会话响应:', response.data)
    
    // 检查创建是否成功
    const isSuccess = response.status === 200 || response.status === 201 || 
                     response.data.code === 200 || response.data.success === true ||
                     (response.data && (response.data.id || response.data.session_id))
    
    if (isSuccess) {
      const rawData = response.data.data || response.data
      
      // 标准化会话数据格式
      const newConversation = {
        id: rawData.session_id || rawData.id,
        session_id: rawData.session_id || rawData.id,
        title: rawData.title || title.trim(),
        create_time: rawData.create_time || rawData.created_at,
        project_id: rawData.project_id,
        project_name: rawData.project_name
      }
      
      console.log('手动创建的新会话:', newConversation)
      
      // 添加到会话列表
      conversations.value.unshift(newConversation)
      
      // 切换到新会话
      currentSessionId.value = newConversation.id
      currentConversation.value = newConversation
      
      // 清空当前消息
      messages.value = []
      
      // 添加欢迎消息
      messages.value.push({
        role: 'agent',
        content: `欢迎使用新对话 **${newConversation.title}**！我是您的智能助手，可以帮您进行项目分析、测试管理等工作。请随时向我提问！`,
        timestamp: new Date().toLocaleTimeString()
      })
      
      ElMessage.success(`创建对话 "${newConversation.title}" 成功`)
      
      // 关闭会话管理对话框
      showConversations.value = false
      
      console.log('切换到新对话:', newConversation.id)
      
    } else {
      console.error('创建会话失败:', response.data)
      ElMessage.error('创建对话失败：' + (response.data.message || response.data.error || '未知错误'))
    }
    
  } catch (error) {
    if (error !== 'cancel') {
      console.error('创建新对话失败:', error)
      ElMessage.error('创建对话失败：' + (error.message || '未知错误'))
    }
  }
}

const clearCurrentConversation = async () => {
  if (!currentSessionId.value) {
    ElMessage.warning('请先选择一个会话')
    return
  }
  
  try {
    await ElMessageBox.confirm(
      '确定要清空当前会话的所有消息吗？此操作不可撤销。',
      '清空会话',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    const response = await conversationApi.clearMessages(currentSessionId.value)
    if (response.data.code === 200) {
      messages.value = []
      ElMessage.success('清空会话成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清空会话失败:', error)
      ElMessage.error('清空会话失败')
    }
  }
}

// 兼容旧的清空对话功能（重定向到清空当前会话）
const clearChat = () => {
  clearCurrentConversation()
}

// 打开会话管理器
const openConversationManager = async () => {
  try {
    // 显示加载状态
    showConversations.value = true
    
    // 重置到第一页
    currentPage.value = 1
    
    // 加载会话列表
    await loadConversations(1)
    
  } catch (error) {
    console.error('打开会话管理器失败:', error)
    ElMessage.error('加载会话列表失败：' + (error.message || '未知错误'))
    
    // 即使加载失败也打开对话框，让用户可以创建新会话
    showConversations.value = true
  }
}

// 平滑滚动到底部
const scrollToBottom = (smooth = true) => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTo({
      top: messagesContainer.value.scrollHeight,
      behavior: smooth ? 'smooth' : 'auto'
    })
  }
}

// 滚动到指定消息
const scrollToMessage = (messageIndex, smooth = true) => {
  if (messagesContainer.value && messageIndex >= 0) {
    nextTick(() => {
      const messageElements = messagesContainer.value.querySelectorAll('.message-wrapper')
      if (messageElements[messageIndex]) {
        messageElements[messageIndex].scrollIntoView({
          behavior: smooth ? 'smooth' : 'auto',
          block: 'end',
          inline: 'nearest'
        })
      }
    })
  }
}

// 滚动到loading元素
const scrollToLoading = () => {
  const loadingElement = document.getElementById('loading-message')
  if (loadingElement) {
    loadingElement.scrollIntoView({
      behavior: 'smooth',
      block: 'end',
      inline: 'nearest'
    })
  }
}

// 智能滚动 - 根据内容长度决定滚动策略
const smartScroll = () => {
  nextTick(() => {
    if (messages.value.length > 0) {
      const lastMessage = messages.value[messages.value.length - 1]
      const isLongMessage = lastMessage.content && lastMessage.content.length > 500
      
      if (isLongMessage) {
        // 长消息：滚动到消息开始位置
        scrollToMessage(messages.value.length - 1, true)
      } else {
        // 短消息：直接滚动到底部
        scrollToBottom(true)
      }
    }
  })
}

// 配置marked选项 - 使用新版本API
marked.use({
  gfm: true, // 启用GitHub风格的Markdown
  breaks: true, // 将换行符转换为<br>
  tables: true, // 明确启用表格功能
  headerIds: false, // 禁用标题ID生成，避免冲突
  mangle: false, // 禁用邮箱地址混淆
  renderer: {
    // 自定义代码块渲染 - 适配新版本marked (15.x)
    code(codeTokenOrString, infostring, escaped) {
      console.log('代码块渲染 - code:', typeof codeTokenOrString, codeTokenOrString, 'infostring:', infostring)
      
      // 处理新版本API：code参数可能是token对象或字符串
      let codeText = ''
      let language = ''
      
      if (typeof codeTokenOrString === 'string') {
        // 旧版本API：直接是字符串
        codeText = codeTokenOrString
        language = (infostring || '').match(/\S*/)?.[0] || ''
      } else if (codeTokenOrString && typeof codeTokenOrString === 'object') {
        // 新版本API：token对象
        codeText = codeTokenOrString.text || codeTokenOrString.raw || ''
        language = codeTokenOrString.lang || (infostring || '').match(/\S*/)?.[0] || ''
        
        console.log('从token对象提取代码:', {
          text: codeTokenOrString.text ? codeTokenOrString.text.substring(0, 100) + '...' : '无',
          raw: codeTokenOrString.raw ? codeTokenOrString.raw.substring(0, 100) + '...' : '无',
          lang: codeTokenOrString.lang,
          type: codeTokenOrString.type
        })
      } else {
        console.warn('代码块内容类型异常:', typeof codeTokenOrString, codeTokenOrString)
        codeText = String(codeTokenOrString || '')
        language = (infostring || '').match(/\S*/)?.[0] || ''
      }
      
      // 确保codeText是字符串
      if (typeof codeText !== 'string') {
        console.warn('提取的代码内容不是字符串:', typeof codeText, codeText)
        codeText = String(codeText || '')
      }
      
      console.log('最终代码内容:', codeText.substring(0, 200) + (codeText.length > 200 ? '...' : ''))
      console.log('检测到的语言:', language)
      
      // 代码高亮处理
      let highlightedCode = codeText
      if (language && hljs.getLanguage(language)) {
        try {
          const result = hljs.highlight(codeText, { language: language })
          highlightedCode = result.value || result
          console.log('语言特定高亮成功:', language)
        } catch (err) {
          console.warn('语言特定代码高亮失败:', err)
          highlightedCode = codeText
        }
      } else {
        try {
          const result = hljs.highlightAuto(codeText)
          if (result && typeof result === 'object' && result.value) {
            highlightedCode = result.value
            console.log('自动高亮成功，检测语言:', result.language)
          } else if (typeof result === 'string') {
            highlightedCode = result
            console.log('自动高亮返回字符串')
          } else {
            console.warn('自动高亮返回异常结果:', typeof result, result)
            highlightedCode = codeText
          }
        } catch (err) {
          console.warn('自动代码高亮失败:', err)
          highlightedCode = codeText
        }
      }
      
      // 确保highlightedCode是字符串
      if (typeof highlightedCode !== 'string') {
        console.error('高亮结果不是字符串:', typeof highlightedCode, highlightedCode)
        highlightedCode = String(codeText || '')
      }
      
      return `<pre><code class="hljs${language ? ` language-${language}` : ''}">${highlightedCode}</code></pre>`
    },
    
    // 自定义内联代码渲染 - 适配新版本marked (15.x)
    codespan(codeTokenOrString) {
      console.log('内联代码渲染 - codespan:', typeof codeTokenOrString, codeTokenOrString)
      
      let codeText = ''
      if (typeof codeTokenOrString === 'string') {
        codeText = codeTokenOrString
      } else if (codeTokenOrString && typeof codeTokenOrString === 'object') {
        codeText = codeTokenOrString.text || codeTokenOrString.raw || ''
      } else {
        codeText = String(codeTokenOrString || '')
      }
      
      return `<code>${codeText}</code>`
    },
    
    // 自定义表格渲染，适配新版本marked (15.x)
    table(token) {
      console.log('新版表格渲染器 - token:', token)
      
      // 辅助函数：从token中提取文本内容
      const extractText = (tokenOrString) => {
        if (typeof tokenOrString === 'string') {
          return tokenOrString
        }
        if (tokenOrString && typeof tokenOrString === 'object') {
          // 如果是token对象，尝试提取文本
          if (tokenOrString.text) {
            return tokenOrString.text
          }
          if (tokenOrString.raw) {
            return tokenOrString.raw
          }
          if (tokenOrString.tokens && Array.isArray(tokenOrString.tokens)) {
            // 如果有子token数组，递归提取文本
            return tokenOrString.tokens.map(extractText).join('')
          }
          // 如果是简单的内容对象
          if (tokenOrString.content) {
            return tokenOrString.content
          }
        }
        return String(tokenOrString || '')
      }
      
      try {
        // 在新版本中，token 是一个包含完整表格数据的对象
        if (!token || !token.header || !token.rows) {
          console.error('表格token数据无效:', token)
          return '<div class="table-error">表格渲染失败：数据结构无效</div>'
        }
        
        console.log('表格header详情:', token.header)
        console.log('表格rows详情:', token.rows)
        
        // 构建表头
        let headerHtml = '<tr>'
        token.header.forEach((cell, index) => {
          const cellText = extractText(cell)
          const align = token.align && token.align[index] ? ` style="text-align: ${token.align[index]}"` : ''
          headerHtml += `<th${align}>${cellText}</th>`
          console.log(`表头单元格 ${index}:`, cell, '提取文本:', cellText)
        })
        headerHtml += '</tr>'
        
        // 构建表体
        let bodyHtml = ''
        if (token.rows && Array.isArray(token.rows)) {
          token.rows.forEach((row, rowIndex) => {
            if (Array.isArray(row)) {
              bodyHtml += '<tr>'
              row.forEach((cell, cellIndex) => {
                const cellText = extractText(cell)
                const align = token.align && token.align[cellIndex] ? ` style="text-align: ${token.align[cellIndex]}"` : ''
                bodyHtml += `<td${align}>${cellText}</td>`
                console.log(`表格单元格 [${rowIndex}][${cellIndex}]:`, cell, '提取文本:', cellText)
              })
              bodyHtml += '</tr>'
            }
          })
        }
        
        const result = `<table class="markdown-table">
          <thead>${headerHtml}</thead>
          <tbody>${bodyHtml}</tbody>
        </table>`
        
        console.log('表格渲染成功，行数:', token.rows.length)
        console.log('最终表格HTML:', result.substring(0, 300) + '...')
        return result
        
      } catch (error) {
        console.error('表格渲染过程中出错:', error)
        console.error('错误详情:', error.stack)
        return '<div class="table-error">表格渲染失败：处理异常</div>'
      }
    },
    
    // 自定义链接渲染，增加安全属性
    link(href, title, text) {
      const titleAttr = title ? ` title="${title}"` : ''
      return `<a href="${href}"${titleAttr} target="_blank" rel="noopener noreferrer">${text}</a>`
    }
  }
})

// 格式化消息内容 - 支持Markdown渲染
const formatMessage = (content) => {
  // 严格的类型检查
  if (!content) return ''
  
  // 确保content是字符串
  let contentStr = content
  if (typeof content !== 'string') {
    console.warn('formatMessage接收到非字符串内容:', typeof content, content)
    contentStr = String(content || '')
  }
  
  // 如果转换后仍然为空，返回空字符串
  if (!contentStr.trim()) {
    return ''
  }
  
  try {
    let processedContent = contentStr
    
    console.log('formatMessage: 处理内容:', contentStr.substring(0, 200) + (contentStr.length > 200 ? '...' : ''))
    console.log('formatMessage: 是否包含markdown代码块:', contentStr.includes('```markdown'))
    
    // 与流式处理保持一致的markdown代码块提取逻辑
    let isMarkdownBlockProcessed = false
    
    // 1. 检查是否包含```markdown标记
    if (processedContent.includes('```markdown')) {
      console.log('formatMessage: 🎯 检测到markdown代码块标记，开始提取处理')
      
      // 2. 尝试匹配完整的markdown代码块
      const completeMarkdownBlockRegex = /^(.*)```markdown\s*\n([\s\S]*?)\n```(.*)$/s
      const completeMatch = processedContent.match(completeMarkdownBlockRegex)
      
      if (completeMatch) {
        // 完整的markdown代码块：前缀 + ```markdown\n内容\n``` + 后缀
        const prefix = completeMatch[1] || ''
        const markdownContent = completeMatch[2] || ''
        const suffix = completeMatch[3] || ''
        
        processedContent = prefix + markdownContent + suffix
        isMarkdownBlockProcessed = true
        console.log('formatMessage: ✅ 提取完整markdown代码块成功')
      } else {
        // 3. 处理不完整的markdown代码块
        const incompleteMarkdownRegex = /^(.*)```markdown\s*\n?([\s\S]*)$/s
        const incompleteMatch = processedContent.match(incompleteMarkdownRegex)
        
        if (incompleteMatch) {
          const prefix = incompleteMatch[1] || ''
          let markdownContent = incompleteMatch[2] || ''
          
          // 移除可能的结束标记
          if (markdownContent.endsWith('\n```') || markdownContent.endsWith('```')) {
            markdownContent = markdownContent.replace(/\n?```\s*$/, '')
          }
          
          processedContent = prefix + markdownContent
          isMarkdownBlockProcessed = true
          console.log('formatMessage: ✅ 提取不完整markdown代码块成功')
        }
      }
    }
    
    console.log('formatMessage: 最终处理的内容:', processedContent.substring(0, 300) + (processedContent.length > 300 ? '...' : ''))
    
    // 使用marked渲染处理后的内容
    const rendered = marked.parse(processedContent)
    console.log('formatMessage: Markdown渲染成功，内容长度:', contentStr.length, '渲染后长度:', rendered.length)
    console.log('formatMessage: 渲染结果:', rendered.substring(0, 500) + (rendered.length > 500 ? '...' : ''))
    return rendered
  } catch (error) {
    console.error('formatMessage: Markdown渲染错误:', error)
    console.error('渲染失败的内容:', contentStr.substring(0, 200) + (contentStr.length > 200 ? '...' : ''))
    console.error('错误详情:', error.message)
    // 如果渲染失败，回退到普通文本处理
    return contentStr.replace(/\n/g, '<br>')
  }
}

// 检测是否为错误消息
const isErrorMessage = (content) => {
  if (!content || typeof content !== 'string') return false
  
  const errorPatterns = [
    '⚠️ **系统提示**',
    '处理失败',
    '系统错误',
    'Internal Server Error',
    'Service Unavailable',
    'Network Error',
    'Request failed',
    'API调用失败',
    '接口调用失败'
  ]
  
  return errorPatterns.some(pattern => 
    content.toLowerCase().includes(pattern.toLowerCase())
  )
}

// 格式化会话预览文本
const formatConversationPreview = (message) => {
  if (!message || typeof message !== 'string') return ''
  
  // 检测错误消息并提供友好的预览
  if (isErrorMessage(message)) {
    return '🔧 系统消息（点击查看详情）'
  }
  
  // 移除markdown格式符号
  let cleanMessage = message
    .replace(/```[\s\S]*?```/g, '[代码块]')  // 代码块
    .replace(/`([^`]+)`/g, '$1')            // 行内代码
    .replace(/\*\*([^*]+)\*\*/g, '$1')      // 粗体
    .replace(/\*([^*]+)\*/g, '$1')          // 斜体
    .replace(/#{1,6}\s+/g, '')              // 标题
    .replace(/\[([^\]]+)\]\([^)]+\)/g, '$1') // 链接
    .replace(/\n+/g, ' ')                   // 换行符
    .trim()
  
  // 截断长文本
  return cleanMessage.length > 80 ? cleanMessage.substring(0, 80) + '...' : cleanMessage
}

// 安全的流式markdown渲染函数
const formatStreamMessage = (content) => {
  // 严格的类型检查
  if (!content) return ''
  
  // 确保content是字符串
  let contentStr = content
  if (typeof content !== 'string') {
    console.warn('formatStreamMessage接收到非字符串内容:', typeof content, content)
    contentStr = String(content || '')
  }
  
  // 如果转换后仍然为空，返回空字符串
  if (!contentStr.trim()) {
    return ''
  }
  
  // 特殊处理：如果内容只是不完整的代码块标记，暂时返回空内容
  if (contentStr.trim() === '```' || contentStr.trim() === '``' || contentStr.trim() === '```mark' || contentStr.trim() === '```markdown') {
    console.log('检测到不完整的代码块标记，暂时跳过渲染:', contentStr)
    return '<div class="stream-placeholder">正在加载内容...</div>'
  }
  
  try {
    // 对于流式传输中的不完整markdown，先进行预处理
    let processedContent = contentStr
    
    console.log('流式渲染输入内容:', contentStr.substring(0, 200) + (contentStr.length > 200 ? '...' : ''))
    console.log('是否包含markdown代码块:', contentStr.includes('```markdown'))
    
    // 优先处理markdown代码块提取 - 这是最重要的处理逻辑
    let isMarkdownBlockProcessed = false
    
    // 1. 检查是否包含```markdown标记
    if (processedContent.includes('```markdown')) {
      console.log('🎯 检测到markdown代码块标记，开始提取处理')
      
      // 2. 尝试匹配完整的markdown代码块
      const completeMarkdownBlockRegex = /^(.*)```markdown\s*\n([\s\S]*?)\n```(.*)$/s
      const completeMatch = processedContent.match(completeMarkdownBlockRegex)
      
      if (completeMatch) {
        // 完整的markdown代码块：前缀 + ```markdown\n内容\n``` + 后缀
        const prefix = completeMatch[1] || ''
        const markdownContent = completeMatch[2] || ''
        const suffix = completeMatch[3] || ''
        
        processedContent = prefix + markdownContent + suffix
        isMarkdownBlockProcessed = true
        console.log('✅ 提取完整markdown代码块成功')
        console.log('前缀:', prefix.substring(0, 50))
        console.log('提取的内容:', markdownContent.substring(0, 200) + '...')
        console.log('后缀:', suffix.substring(0, 50))
      } else {
        // 3. 处理不完整的markdown代码块（流式传输中）
        const incompleteMarkdownRegex = /^(.*)```markdown\s*\n?([\s\S]*)$/s
        const incompleteMatch = processedContent.match(incompleteMarkdownRegex)
        
        if (incompleteMatch) {
          const prefix = incompleteMatch[1] || ''
          let markdownContent = incompleteMatch[2] || ''
          
          // 移除可能的结束标记
          if (markdownContent.endsWith('\n```') || markdownContent.endsWith('```')) {
            markdownContent = markdownContent.replace(/\n?```\s*$/, '')
            console.log('移除末尾的```标记')
          }
          
          processedContent = prefix + markdownContent
          isMarkdownBlockProcessed = true
          console.log('✅ 提取不完整markdown代码块成功')
          console.log('前缀:', prefix.substring(0, 50))
          console.log('提取的内容:', markdownContent.substring(0, 200) + '...')
        }
      }
    }
    
    // 4. 只有在没有处理markdown代码块的情况下，才处理普通代码块问题
    if (!isMarkdownBlockProcessed) {
      console.log('💡 未检测到markdown代码块，检查普通代码块')
      
      // 检查是否有未闭合的代码块
      const codeBlockMatches = processedContent.match(/```/g)
      if (codeBlockMatches && codeBlockMatches.length % 2 !== 0) {
        console.log('检测到未闭合的代码块，添加临时闭合标记')
        processedContent += '\n```'
      }
    } else {
      console.log('✅ 已处理markdown代码块，跳过普通代码块检查')
    }
    
    // 检查是否有未闭合的表格
    const lines = processedContent.split('\n')
    let inTable = false
    let hasTableHeader = false
    let tableLines = []
    
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      if (line.startsWith('|') && line.endsWith('|')) {
        if (!inTable) {
          inTable = true
          hasTableHeader = true
          tableLines = [line]
        } else {
          tableLines.push(line)
          if (hasTableHeader && line.match(/^\|[\s\-\|:]+\|$/)) {
            // 这是表格分隔行
            hasTableHeader = false
          }
        }
      } else if (inTable && line === '') {
        // 表格结束
        inTable = false
        hasTableHeader = false
        tableLines = []
      } else if (inTable && !line.startsWith('|')) {
        // 表格被中断，但可能是流式传输导致的不完整
        // 保持表格状态，等待更多内容
      }
    }
    
    // 如果有不完整的表格，记录调试信息
    if (inTable && tableLines.length > 0) {
      console.log('检测到不完整表格:', tableLines)
    }
    
    console.log('最终处理的内容:', processedContent.substring(0, 300) + (processedContent.length > 300 ? '...' : ''))
    
    // 使用marked渲染处理后的内容
    const rendered = marked.parse(processedContent)
    
    console.log('流式渲染结果:', rendered.substring(0, 300) + (rendered.length > 300 ? '...' : ''))
    
    return rendered
  } catch (error) {
    console.error('流式Markdown渲染失败:', error)
    console.error('错误堆栈:', error.stack)
    console.error('失败内容:', contentStr.substring(0, 200) + (contentStr.length > 200 ? '...' : ''))
    // 如果渲染失败，回退到普通文本处理，保持基本格式
    return contentStr
      .replace(/\n/g, '<br>')
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/`(.*?)`/g, '<code>$1</code>')
  }
}

// 获取函数显示名称
const getFunctionDisplayName = (functionName) => {
  const displayNames = {
    'get_project_statistics': '获取项目统计',
    'create_test_plan': '创建测试计划',
    'get_failed_test_cases': '获取失败用例',
    'analyze_project_data': '分析项目数据',
    'get_defect_statistics': '获取缺陷统计',
    'get_project_overview': '获取项目概览',
    'get_test_execution_report': '获取测试执行报告',
    'create_defect': '创建缺陷',
    'update_defect': '更新缺陷',
    'get_test_cases': '获取测试用例',
    'execute_test_case': '执行测试用例',
    'get_project_health': '获取项目健康度',
    'analyze_defect_trends': '分析缺陷趋势'
  }
  return displayNames[functionName] || functionName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
}

// 加载本次对话的工具调用分析
const loadCurrentRoundToolCalls = async () => {
  if (!currentSessionId.value) {
    console.log('没有当前会话ID，跳过本次工具调用分析')
    return
  }
  
  try {
    console.log('开始加载本次对话的工具调用分析，会话ID:', currentSessionId.value)
    
    // 获取最近30秒内的工具调用记录
    const recentTime = new Date()
    recentTime.setSeconds(recentTime.getSeconds() - 30)
    
    const toolCallsResponse = await getSessionToolCalls(currentSessionId.value, {
      limit: 20, // 获取最近20条工具调用
      ordering: '-created_at', // 按创建时间倒序
      start_time: recentTime.toISOString() // 只获取最近30秒的记录
    })
    
    console.log('本次工具调用记录响应:', toolCallsResponse)
    
    // 更宽泛的成功判断
    const isSuccess = toolCallsResponse.status === 200 || 
                     (toolCallsResponse.data && (toolCallsResponse.data.code === 200 || toolCallsResponse.data.success))
    
    if (isSuccess) {
      const responseData = toolCallsResponse.data?.data || toolCallsResponse.data
      
      // 根据实际返回结构解析数据
      const toolCalls = responseData?.tool_calls || []
      
      console.log('解析的本次工具调用数据:', toolCalls)
      
      if (Array.isArray(toolCalls) && toolCalls.length > 0) {
        // 进一步过滤，只保留真正最近的工具调用（比如最近1分钟内的）
        const now = new Date()
        const recentToolCalls = toolCalls.filter(call => {
          const callTime = new Date(call.call_time)
          const timeDiff = (now - callTime) / 1000 // 秒
          return timeDiff <= 60 // 最近1分钟内
        })
        
        if (recentToolCalls.length > 0) {
          console.log('过滤后的本次工具调用:', recentToolCalls)
          
          // 添加本次工具调用分析消息
          addToolCallsAnalysisMessage(recentToolCalls, null, true)
          
          console.log(`展示本次对话的 ${recentToolCalls.length} 条工具调用`)
        } else {
          console.log('没有找到最近的工具调用记录')
        }
      } else {
        console.log('本次对话没有工具调用记录')
      }
    } else {
      console.error('获取本次工具调用记录失败:', toolCallsResponse.data)
    }
  } catch (error) {
    console.error('加载本次工具调用分析失败:', error)
    // 静默处理错误，不显示错误消息，因为这不是用户主动触发的
  }
}

// 加载工具调用分析
const loadToolCallsAnalysis = async () => {
  if (!currentSessionId.value) {
    console.log('没有当前会话ID，跳过工具调用分析')
    ElMessage.warning('请先选择一个会话')
    return
  }
  
  try {
    isLoadingToolCalls.value = true
    console.log('开始加载工具调用分析，会话ID:', currentSessionId.value)
    
    // 获取当前会话的工具调用记录
    const toolCallsResponse = await getSessionToolCalls(currentSessionId.value, {
      limit: 10, // 获取最近10条工具调用
      ordering: '-created_at' // 按创建时间倒序
    })
    
    console.log('工具调用记录响应:', toolCallsResponse)
    
    // 更宽泛的成功判断
    const isSuccess = toolCallsResponse.status === 200 || 
                     (toolCallsResponse.data && (toolCallsResponse.data.code === 200 || toolCallsResponse.data.success))
    
    if (isSuccess) {
      const responseData = toolCallsResponse.data?.data || toolCallsResponse.data
      
      // 根据实际返回结构解析数据
      const toolCalls = responseData?.tool_calls || []
      const stats = responseData?.statistics || null
      
      console.log('解析的工具调用数据:', toolCalls)
      console.log('解析的统计数据:', stats)
      
      if (Array.isArray(toolCalls) && toolCalls.length > 0) {
        toolCallsAnalysis.value = {
          toolCalls: toolCalls,
          statistics: stats,
          pagination: responseData?.pagination,
          filters: responseData?.filters,
          lastUpdated: new Date().toLocaleTimeString()
        }
        
        console.log('工具调用分析数据:', toolCallsAnalysis.value)
        
        // 添加新的分析消息
        addToolCallsAnalysisMessage(toolCalls, stats)
        
        ElMessage.success(`找到 ${toolCalls.length} 条工具调用记录`)
      } else {
        console.log('没有找到工具调用记录')
        ElMessage.info('当前会话暂无工具调用记录')
      }
    } else {
      console.error('获取工具调用记录失败:', toolCallsResponse.data)
      ElMessage.error('获取工具调用记录失败')
    }
  } catch (error) {
    console.error('加载工具调用分析失败:', error)
    ElMessage.error('加载工具调用分析失败：' + (error.message || '未知错误'))
  } finally {
    isLoadingToolCalls.value = false
  }
}

// 添加新的工具调用分析消息
const addToolCallsAnalysisMessage = (toolCalls, stats, isCurrentRound = false) => {
  const analysisMessage = {
    role: 'system',
    content: generateToolCallsAnalysisMessage(toolCalls, stats, isCurrentRound),
    isToolCallsAnalysis: true,
    timestamp: new Date().toLocaleTimeString()
  }
  
  // 每次都添加新的分析消息，不更新已有的
  messages.value.push(analysisMessage)
  console.log('添加新的工具调用分析消息')
  
  // 滚动到新消息
  setTimeout(() => {
    smartScroll()
  }, 100)
}





// 生成工具调用分析消息
const generateToolCallsAnalysisMessage = (toolCalls, stats, isCurrentRound = false) => {
  let content = isCurrentRound ? '## 🔧 本次工具调用分析\n\n' : '## 🔧 工具调用分析\n\n'
  
  if (stats && !isCurrentRound) {
    content += `### 📊 统计概览\n`
    content += `- **总调用次数**: ${stats.total_calls || 0}\n`
    content += `- **成功调用**: ${stats.success_count || 0} 次\n`
    content += `- **失败调用**: ${stats.failed_count || 0} 次\n`
    content += `- **成功率**: ${stats.success_rate || 0}%\n`
    content += `- **最常用工具**: ${getFunctionDisplayName(stats.most_used_function) || '无'}\n`
    content += `- **函数类型数**: ${stats.unique_functions || 0} 种\n\n`
  }
  
  content += isCurrentRound ? `### 🛠️ 本次调用的工具\n\n` : `### 🛠️ 最近调用记录\n\n`
  
  toolCalls.slice(0, 5).forEach((call, index) => {
    const status = call.success ? '✅ 成功' : '❌ 失败'
    const functionName = getFunctionDisplayName(call.function_name)
    const callTime = new Date(call.call_time).toLocaleString()
    
    content += `**${index + 1}. ${functionName}** ${status}\n`
    content += `   - 调用时间: ${callTime}\n`
    content += `   - 用户输入: "${call.user_input}"\n`
    
    if (call.arguments) {
      const params = typeof call.arguments === 'string' 
        ? call.arguments 
        : JSON.stringify(call.arguments)
      content += `   - 调用参数: \`${params.substring(0, 100)}${params.length > 100 ? '...' : ''}\`\n`
    }
    
    if (call.result && call.result.data) {
      // 根据不同工具类型显示简化的结果信息
      const resultSummary = generateResultSummary(call.function_name, call.result.data)
      if (resultSummary) {
        content += `   - 执行结果: ${resultSummary}\n`
      }
    }
    
    if (!call.success && call.error_message) {
      content += `   - 错误信息: ${call.error_message}\n`
    }
    
    if (call.tokens_used > 0) {
      content += `   - Token消耗: ${call.tokens_used}\n`
    }
    
    content += '\n'
  })
  
  if (toolCalls.length > 5) {
    content += `*还有 ${toolCalls.length - 5} 条调用记录...*\n\n`
  }
  
  content += '---\n'
  if (isCurrentRound) {
    content += '*此分析基于本次对话的工具调用记录自动生成*'
  } else {
    content += '*此分析基于当前会话的工具调用记录自动生成*'
  }
  
  return content
}

// 生成结果摘要
const generateResultSummary = (functionName, resultData) => {
  switch (functionName) {
    case 'get_project_statistics':
      if (resultData.project_info && resultData.test_case_stats) {
        const project = resultData.project_info
        const testStats = resultData.test_case_stats
        const defectStats = resultData.defect_stats
        return `项目 "${project.name}" - 测试用例: ${testStats.total}个(通过${testStats.passed}个), 缺陷: ${defectStats.total}个`
      }
      break
    case 'get_failed_test_cases':
      if (resultData.failed_cases) {
        return `找到 ${resultData.failed_cases.length} 个失败用例`
      }
      break
    case 'create_test_plan':
      if (resultData.plan_name) {
        return `创建测试计划: ${resultData.plan_name}`
      }
      break
    case 'get_defect_statistics':
      if (resultData.defects) {
        return `缺陷统计: ${resultData.defects.length} 个缺陷`
      }
      break
    default:
      return '执行成功'
  }
  return '执行成功'
}





// 处理分页变化
const handlePageChange = async (page) => {
  await loadConversations(page)
}

// 处理页面大小变化
const handlePageSizeChange = async (newPageSize) => {
  pageSize.value = newPageSize
  // 重新从第一页开始加载
  await loadConversations(1)
}

// 处理搜索
const handleSearch = async () => {
  // 搜索时重置到第一页
  currentPage.value = 1
  await loadConversations(1)
}
</script>

<style scoped>
.agent-tester-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 24px;
}

.page-description {
  color: #666;
  margin: 0;
  line-height: 1.6;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 会话管理头部样式 */
.conversation-header {
  align-items: flex-start;
  gap: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.header-right {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.session-tag {
  font-size: 11px;
  padding: 2px 8px;
  border-radius: 12px;
}

/* 会话管理头部样式 */
.conversations-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

/* 会话列表样式 */
.conversations-list {
  max-height: 500px;
  overflow-y: auto;
}

.conversation-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.conversation-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #ffffff;
}

.conversation-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  transform: translateY(-1px);
}

.conversation-item.active {
  border-color: #409eff;
  background: #f0f9ff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  font-size: 14px;
}

.conversation-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #909399;
}

.conversation-time,
.conversation-stats,
.conversation-project {
  display: flex;
  align-items: center;
  gap: 4px;
}

.conversation-project {
  background: #f0f9ff;
  color: #1e40af;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 11px;
}

.conversation-preview {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  line-height: 1.4;
  background: #f8f9fa;
  padding: 6px 8px;
  border-radius: 4px;
  border-left: 3px solid #e4e7ed;
}

.conversation-actions {
  margin-left: 12px;
}

/* 对话框样式 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.quick-commands-card,
.chat-card,
.help-card {
  margin-bottom: 20px;
}

.quick-commands {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.quick-command-btn {
  margin: 0;
}

.chat-messages {
  height: 500px;
  overflow-y: auto;
  padding: 20px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
  position: relative;
  scroll-behavior: smooth;
  scroll-padding-bottom: 40px;
}

.chat-messages::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to bottom, rgba(248, 249, 250, 0.8), transparent);
  pointer-events: none;
  border-radius: 12px 12px 0 0;
}

.chat-messages::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 20px;
  background: linear-gradient(to top, rgba(248, 249, 250, 0.8), transparent);
  pointer-events: none;
  border-radius: 0 0 12px 12px;
}

.empty-chat {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

/* 消息容器样式 */
.message-wrapper {
  margin-bottom: 24px;
  animation: messageSlideIn 0.3s ease-out;
}

.message-wrapper.user {
  display: flex;
  justify-content: flex-end;
}

.message-container {
  display: flex;
  align-items: flex-start;
  max-width: 80%;
  gap: 12px;
}

.message-wrapper.user .message-container {
  flex-direction: row-reverse;
}

/* 头像样式 */
.message-avatar {
  flex-shrink: 0;
}

.avatar {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.avatar.user {
  background: linear-gradient(135deg, #409eff, #67c23a);
}

.avatar.agent {
  background: linear-gradient(135deg, #909399, #c0c4cc);
}

.avatar.loading {
  animation: avatarPulse 2s infinite;
}

@keyframes avatarPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 消息内容区域 */
.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
  color: #909399;
}

.message-wrapper.user .message-header {
  flex-direction: row-reverse;
  text-align: right;
}

.sender {
  font-weight: 500;
  color: #606266;
}

.timestamp {
  font-size: 11px;
  color: #c0c4cc;
}

/* 消息气泡样式 */
.message-bubble {
  position: relative;
  padding: 16px 20px;
  border-radius: 16px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  line-height: 1.6;
}

.message-wrapper.user .message-bubble {
  background: linear-gradient(135deg, #409eff, #67c23a);
  color: white;
  border-bottom-right-radius: 4px;
}

.message-wrapper.agent .message-bubble {
  background: #ffffff;
  color: #303133;
  border: 1px solid #e4e7ed;
  border-bottom-left-radius: 4px;
}

/* 消息气泡箭头 */
.message-bubble::before {
  content: '';
  position: absolute;
  top: 12px;
  width: 0;
  height: 0;
  border: 6px solid transparent;
}

.message-wrapper.user .message-bubble::before {
  right: -12px;
  border-left-color: #409eff;
}

.message-wrapper.agent .message-bubble::before {
  left: -12px;
  border-right-color: #ffffff;
}

/* 用户消息样式 */
.user-message {
  font-size: 14px;
  line-height: 1.6;
}

/* AI消息样式 */
.agent-message {
  font-size: 14px;
  line-height: 1.6;
}

/* 系统分析消息样式 */
.system-message {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 12px;
  padding: 16px;
  margin: 8px 0;
  position: relative;
  font-size: 14px;
}

.system-message::before {
  content: '🔧 系统分析';
  position: absolute;
  top: -10px;
  left: 16px;
  background: #f8f9fa;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  border: 1px solid #dee2e6;
  color: #6c757d;
  font-weight: 500;
}

.analysis-content {
  color: #495057;
  line-height: 1.6;
}

.analysis-content :deep(h2) {
  color: #343a40;
  font-size: 16px;
  margin: 0 0 12px 0;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 6px;
}

.analysis-content :deep(h3) {
  color: #495057;
  font-size: 14px;
  margin: 12px 0 6px 0;
  font-weight: 600;
}

.analysis-content :deep(ul), 
.analysis-content :deep(ol) {
  margin: 6px 0;
  padding-left: 16px;
}

.analysis-content :deep(li) {
  margin: 2px 0;
}

.analysis-content :deep(strong) {
  color: #343a40;
  font-weight: 600;
}

.analysis-content :deep(code) {
  background: #f1f3f4;
  border: 1px solid #dadce0;
  border-radius: 3px;
  padding: 1px 4px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
  color: #d73a49;
}

.analysis-content :deep(hr) {
  border: none;
  border-top: 1px solid #dee2e6;
  margin: 12px 0;
}

/* 响应内容样式 */
.response-content {
  margin-bottom: 12px;
}

.response-content:last-child {
  margin-bottom: 0;
}

/* Markdown样式 */
.response-content :deep(h1),
.response-content :deep(h2),
.response-content :deep(h3),
.response-content :deep(h4),
.response-content :deep(h5),
.response-content :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.4;
  color: #303133;
}

.response-content :deep(h1) {
  font-size: 20px;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 8px;
}

.response-content :deep(h2) {
  font-size: 18px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 6px;
}

.response-content :deep(h3) {
  font-size: 16px;
  color: #409eff;
}

.response-content :deep(h4) {
  font-size: 15px;
  color: #606266;
}

.response-content :deep(h5),
.response-content :deep(h6) {
  font-size: 14px;
  color: #909399;
}

.response-content :deep(p) {
  margin: 8px 0;
  line-height: 1.6;
  color: #303133;
}

.response-content :deep(strong) {
  font-weight: 600;
  color: #303133;
}

.response-content :deep(em) {
  font-style: italic;
  color: #606266;
}

.response-content :deep(code) {
  background: #f5f7fa;
  color: #e6a23c;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  border: 1px solid #ebeef5;
}

.response-content :deep(pre) {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
  margin: 12px 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  position: relative;
}

.response-content :deep(pre code) {
  background: none !important;
  color: inherit;
  padding: 0;
  border: none;
  font-size: inherit;
  display: block;
  overflow-x: auto;
}

/* 代码高亮样式覆盖 */
.response-content :deep(pre .hljs) {
  background: transparent !important;
  padding: 0 !important;
}

/* 为代码块添加复制按钮样式准备 */
.response-content :deep(pre::before) {
  content: 'Code';
  position: absolute;
  top: 8px;
  right: 12px;
  font-size: 11px;
  color: #909399;
  background: rgba(255, 255, 255, 0.8);
  padding: 2px 6px;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 优化highlight.js的颜色主题 */
.response-content :deep(.hljs) {
  color: #333 !important;
}

.response-content :deep(.hljs-comment),
.response-content :deep(.hljs-quote) {
  color: #998 !important;
  font-style: italic;
}

.response-content :deep(.hljs-keyword),
.response-content :deep(.hljs-selector-tag),
.response-content :deep(.hljs-subst) {
  color: #d73a49 !important;
  font-weight: bold;
}

.response-content :deep(.hljs-number),
.response-content :deep(.hljs-literal),
.response-content :deep(.hljs-variable),
.response-content :deep(.hljs-template-variable),
.response-content :deep(.hljs-tag .hljs-attr) {
  color: #e36209 !important;
}

.response-content :deep(.hljs-string),
.response-content :deep(.hljs-doctag) {
  color: #032f62 !important;
}

.response-content :deep(.hljs-title),
.response-content :deep(.hljs-section),
.response-content :deep(.hljs-selector-id) {
  color: #6f42c1 !important;
  font-weight: bold;
}

.response-content :deep(.hljs-type),
.response-content :deep(.hljs-class .hljs-title) {
  color: #d73a49 !important;
  font-weight: bold;
}

.response-content :deep(.hljs-tag),
.response-content :deep(.hljs-name),
.response-content :deep(.hljs-attribute) {
  color: #22863a !important;
  font-weight: normal;
}

.response-content :deep(.hljs-regexp),
.response-content :deep(.hljs-link) {
  color: #032f62 !important;
}

.response-content :deep(.hljs-symbol),
.response-content :deep(.hljs-bullet) {
  color: #e36209 !important;
}

.response-content :deep(.hljs-built_in),
.response-content :deep(.hljs-builtin-name) {
  color: #005cc5 !important;
}

.response-content :deep(.hljs-meta) {
  color: #999 !important;
}

.response-content :deep(.hljs-deletion) {
  background: #ffeef0 !important;
}

.response-content :deep(.hljs-addition) {
  background: #f0fff4 !important;
}

.response-content :deep(.hljs-emphasis) {
  font-style: italic;
}

.response-content :deep(.hljs-strong) {
  font-weight: bold;
}

.response-content :deep(ul),
.response-content :deep(ol) {
  margin: 8px 0;
  padding-left: 24px;
}

.response-content :deep(li) {
  margin: 4px 0;
  line-height: 1.6;
  color: #303133;
}

.response-content :deep(ul li) {
  list-style-type: disc;
}

.response-content :deep(ol li) {
  list-style-type: decimal;
}

.response-content :deep(blockquote) {
  margin: 12px 0;
  padding: 12px 16px;
  background: #f0f9ff;
  border-left: 4px solid #409eff;
  border-radius: 0 6px 6px 0;
  color: #606266;
  font-style: italic;
}

.response-content :deep(blockquote p) {
  margin: 0;
}

.response-content :deep(hr) {
  margin: 20px 0;
  border: none;
  border-top: 2px solid #e4e7ed;
  border-radius: 1px;
}

.response-content :deep(table),
.response-content :deep(.markdown-table) {
  width: 100%;
  border-collapse: collapse;
  margin: 12px 0;
  background: #ffffff;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  overflow: hidden;
}

.stream-text :deep(table),
.stream-text :deep(.markdown-table) {
  width: 100%;
  border-collapse: collapse;
  margin: 12px 0;
  background: #ffffff;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  overflow: hidden;
}

.response-content :deep(th),
.response-content :deep(td) {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #ebeef5;
}

.response-content :deep(th) {
  background: #f5f7fa;
  font-weight: 600;
  color: #303133;
}

.response-content :deep(td) {
  color: #606266;
}

.response-content :deep(tr:last-child td) {
  border-bottom: none;
}

.response-content :deep(a) {
  color: #409eff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.response-content :deep(a:hover) {
  color: #67c23a;
  text-decoration: underline;
}

/* 特殊标记样式 */
.response-content :deep(.highlight) {
  background: linear-gradient(135deg, #fff9e6, #fef7e0);
  padding: 2px 6px;
  border-radius: 4px;
  border-left: 3px solid #e6a23c;
}

/* 响应式Markdown */
@media (max-width: 768px) {
  .response-content :deep(h1) {
    font-size: 18px;
  }
  
  .response-content :deep(h2) {
    font-size: 16px;
  }
  
  .response-content :deep(h3) {
    font-size: 15px;
  }
  
  .response-content :deep(pre) {
    padding: 12px;
    font-size: 12px;
  }
  
  .response-content :deep(table) {
    font-size: 12px;
  }
  
  .response-content :deep(th),
  .response-content :deep(td) {
    padding: 6px 8px;
  }
}

.response-content {
  line-height: 1.6;
}

/* 函数调用样式 */
.function-calls {
  margin-top: 20px;
  border-top: 1px solid #e4e7ed;
  padding-top: 16px;
}

.function-calls-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
  color: #606266;
}

.function-call {
  margin-bottom: 16px;
}

.function-call-card {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
}

.function-call-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.function-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e4e7ed;
}

.function-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.function-icon {
  color: #409eff;
}

.function-name {
  font-weight: 500;
  color: #303133;
}

.function-body {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.function-section {
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #ebeef5;
}

.section-title {
  font-size: 12px;
  font-weight: 500;
  color: #909399;
  margin-bottom: 8px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.function-args,
.function-result {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 6px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
  line-height: 1.4;
  white-space: pre-wrap;
  word-wrap: break-word;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
}

.function-result-content {
  border-radius: 6px;
}

/* 格式化结果样式 */
.formatted-result {
  background: #ffffff;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.result-summary {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
  border-radius: 8px;
  margin-bottom: 16px;
  border-left: 4px solid #409eff;
}

.result-summary .summary-text {
  font-size: 14px;
  color: #303133;
}

.warning-icon {
  color: #e6a23c;
  font-size: 18px;
}

.success-icon {
  color: #67c23a;
  font-size: 18px;
}

.failed-cases-list {
  margin-top: 16px;
}

.cases-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e4e7ed;
}

.cases-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.raw-result {
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e4e7ed;
}

/* 流式传输样式 */
.streaming-message {
  animation: messageSlideIn 0.3s ease-out;
}

.streaming-bubble {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe) !important;
  border: 1px solid #b3d8ff !important;
  position: relative;
  overflow: hidden;
}

.streaming-content {
  position: relative;
  color: #0369a1;
}

.stream-text {
  line-height: 1.6;
  margin-bottom: 8px;
  min-height: 20px;
}

/* 流式传输markdown样式 */
.stream-text :deep(h1),
.stream-text :deep(h2),
.stream-text :deep(h3),
.stream-text :deep(h4),
.stream-text :deep(h5),
.stream-text :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: 600;
  line-height: 1.4;
  color: #303133;
}

.stream-text :deep(h1) {
  font-size: 20px;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 8px;
}

.stream-text :deep(h2) {
  font-size: 18px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 6px;
}

.stream-text :deep(h3) {
  font-size: 16px;
  color: #409eff;
}

.stream-text :deep(p) {
  margin: 8px 0;
  line-height: 1.6;
  color: #303133;
}

.stream-text :deep(strong) {
  font-weight: 600;
  color: #303133;
}

.stream-text :deep(em) {
  font-style: italic;
  color: #606266;
}

.stream-text :deep(code) {
  background: #f5f7fa;
  color: #e6a23c;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  border: 1px solid #ebeef5;
}

.stream-text :deep(pre) {
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  overflow-x: auto;
  margin: 12px 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.stream-text :deep(pre code) {
  background: none !important;
  color: inherit;
  padding: 0;
  border: none;
  font-size: inherit;
}

.stream-text :deep(ul),
.stream-text :deep(ol) {
  margin: 8px 0;
  padding-left: 24px;
}

.stream-text :deep(li) {
  margin: 4px 0;
  line-height: 1.6;
  color: #303133;
}

.stream-text :deep(th),
.stream-text :deep(td) {
  padding: 8px 12px;
  text-align: left;
  border-bottom: 1px solid #ebeef5;
}

.stream-text :deep(th) {
  background: #f5f7fa;
  font-weight: 600;
  color: #303133;
}

.stream-text :deep(td) {
  color: #606266;
}

.stream-text :deep(tr:last-child td) {
  border-bottom: none;
}

.stream-text :deep(thead th) {
  background: #f5f7fa;
  font-weight: 600;
  color: #303133;
}

.stream-text :deep(tbody td) {
  color: #606266;
}

.stream-placeholder {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.typing-cursor {
  display: inline-block;
  background: #0369a1;
  color: #0369a1;
  animation: typingCursor 1s infinite;
  margin-left: 2px;
  font-size: 14px;
  line-height: 1;
  width: 2px;
  height: 18px;
}

@keyframes typingCursor {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

.avatar.streaming {
  animation: streamingPulse 2s infinite;
  background: linear-gradient(135deg, #409eff, #67c23a) !important;
}

@keyframes streamingPulse {
  0%, 100% { 
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(3, 105, 161, 0.3);
  }
  50% { 
    transform: scale(1.05);
    box-shadow: 0 4px 16px rgba(3, 105, 161, 0.5);
  }
}

/* 加载状态样式 */
.loading-message {
  opacity: 0.9;
}

.loading-bubble {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe) !important;
  border: 1px solid #b3d8ff !important;
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.loading-dots {
  display: flex;
  gap: 4px;
  align-items: center;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #409eff;
  animation: loadingDots 1.6s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

.loading-text {
  font-size: 13px;
  color: #606266;
  font-style: italic;
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

/* 消息动画 */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 错误消息样式 */
.error-message-content {
  background: linear-gradient(135deg, #fff5f5 0%, #fef2f2 100%);
  border: 1px solid #fecaca;
  border-radius: 8px;
  padding: 16px;
  margin: 8px 0;
  position: relative;
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.1);
}

.error-message-content::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  border-radius: 4px 0 0 4px;
}

.error-message-content h1,
.error-message-content h2,
.error-message-content h3,
.error-message-content h4,
.error-message-content h5,
.error-message-content h6 {
  color: #c53030 !important;
  margin-top: 0;
}

.error-message-content p {
  color: #744210;
  margin-bottom: 8px;
}

.error-message-content strong {
  color: #c53030;
  font-weight: 600;
}

.error-message-content em {
  color: #9ca3af;
  font-style: italic;
  font-size: 13px;
  opacity: 0.8;
}

/* 为错误消息添加特殊图标 */
.error-message-content p:first-child {
  position: relative;
  padding-left: 0;
}

/* 错误消息的代码块样式调整 */
.error-message-content pre {
  background: #fef2f2 !important;
  border: 1px solid #fecaca !important;
  color: #991b1b !important;
}

.error-message-content code {
  background: rgba(239, 68, 68, 0.1) !important;
  color: #dc2626 !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
}

.chat-input {
  background: #ffffff;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.chat-input :deep(.el-textarea__inner) {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  line-height: 1.6;
  resize: none;
  transition: all 0.3s ease;
}

.chat-input :deep(.el-textarea__inner):focus {
  border-color: #409eff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f2f5;
}

.input-tip {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #909399;
  font-size: 12px;
  background: #f8f9fa;
  padding: 6px 12px;
  border-radius: 16px;
}

.input-actions .el-button {
  padding: 10px 20px;
  font-weight: 500;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.input-actions .el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.clear-btn {
  color: #909399;
}

.clear-btn:hover {
  color: #f56c6c;
}

.help-list {
  list-style-type: disc;
  padding-left: 20px;
  line-height: 1.8;
}

.help-list li {
  margin-bottom: 8px;
  color: #666;
}

.help-list strong {
  color: #333;
}

.help-text {
  color: #666;
  line-height: 1.6;
  margin: 12px 0;
}

.example-alert {
  margin: 12px 0;
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 8px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #409eff, #67c23a);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #67c23a, #409eff);
}

/* 加载状态样式 */
.loading-conversations {
  padding: 20px;
  border-radius: 8px;
  background: #fafafa;
  border: 1px solid #ebeef5;
}

.conversations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conversations-header .header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.conversations-header .header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-info {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 分页信息样式 */
.conversations-pagination {
  margin-top: 16px;
}

.pagination-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
  padding: 0 16px;
}

.pagination-text {
  color: #606266;
}

.pagination-more {
  color: #409eff;
  font-style: italic;
}

.pagination-controls {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.quick-nav {
  display: flex;
  gap: 8px;
}

.conversations-paginator {
  background: transparent;
}

.conversations-paginator :deep(.el-pagination__total),
.conversations-paginator :deep(.el-pagination__sizes),
.conversations-paginator :deep(.el-pagination__jump) {
  color: #606266;
  font-size: 12px;
}

.conversations-paginator :deep(.el-pager li) {
  min-width: 28px;
  height: 28px;
  line-height: 28px;
  font-size: 12px;
}

.conversations-paginator :deep(.btn-prev),
.conversations-paginator :deep(.btn-next) {
  min-width: 28px;
  height: 28px;
  line-height: 28px;
}

/* 响应式分页 */
@media (max-width: 768px) {
  .conversations-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .conversations-header .header-left,
  .conversations-header .header-right {
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
  }
  
  .conversations-header .header-right .el-input {
    flex: 1;
    min-width: 120px !important;
  }
  
  .pagination-controls {
    gap: 8px;
  }
  
  .quick-nav {
    order: 2;
  }
  
  .conversations-paginator {
    order: 1;
  }
  
  .conversations-paginator :deep(.el-pagination__jump) {
    display: none;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-container {
    max-width: 95%;
  }
  
  .message-bubble {
    padding: 12px 16px;
    font-size: 13px;
  }
  
  .message-avatar {
    display: none;
  }
  
  .function-call-card {
    padding: 12px;
  }
  
  .function-section {
    padding: 8px;
  }
  
  .chat-input {
    padding: 16px;
  }
  
  .input-actions {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .input-tip {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .chat-messages {
    height: 400px;
    padding: 16px 12px;
  }
  
  .message-bubble {
    padding: 10px 14px;
    font-size: 12px;
  }
  
  .function-args,
  .function-result {
    font-size: 11px;
    padding: 8px;
  }
}

.stream-text :deep(tbody td) {
  color: #606266;
}

/* 表格错误处理样式 */
.response-content :deep(.table-error),
.stream-text :deep(.table-error) {
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  color: #f56c6c;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  margin: 8px 0;
}
</style> 