<template>
  <Home>
    <PageContainer title="接口自动化测试脑图">
      <!-- 环境选择器 -->
      <div class="environment-selector">
        <span class="env-label">当前环境套：</span>
        <el-select
          v-model="currentEnvId"
          placeholder="请选择环境套"
          style="width: 200px"
          @change="handleEnvChange"
          size="small"
        >
          <el-option
            v-for="item in envList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>
        <el-button
          type="primary"
          size="small"
          @click="fetchEnvList"
          :loading="envLoading"
          style="margin-left: 8px"
        >
          刷新
        </el-button>
      </div>

      <!-- 工具栏组件 -->
      <MindMapToolbar
        :selected-node="selectedNode"
        :graph="graph"
        :can-undo="canUndo.value"
        :can-redo="canRedo.value"
        :has-clipboard="hasClipboard.value"
        :is-executing="globalLoading"
        :zoom-level="zoomLevel"
        @save-map="handleSaveMap"
        @export-map="handleExportMap"
        @add-node="handleAddNode"
        @add-parent-node="handleAddParentNode"
        @remove-node="handleRemoveNode"
        @execute-selected="handleExecuteSelected"
        @execute-all="handleExecuteAll"
        @zoom-in="handleZoomIn"
        @zoom-out="handleZoomOut"
        @reset-zoom="handleResetZoom"
        @undo="handleUndo"
        @redo="handleRedo"
        @copy-node="handleCopyNode"
        @paste-node="handlePasteNode"
        @import-map="handleImportMap"
        @show-templates="handleShowTemplates"
        @show-settings="handleShowSettings"
        @show-help="handleShowHelp"
      />

      <!-- 主要内容区域 -->
      <div class="content-container" :class="{ 'has-selected-node': selectedNode, 'no-selected-node': !selectedNode }">
        <!-- 左侧测试用例列表 -->
        <TestCaseList
          :test-cases="testCases"
          :loading="testCasesLoading"
          @case-dragged="handleCaseDragged"
          @refresh="fetchTestCases"
        />

        <!-- 中间脑图画布 -->
        <div class="canvas-container">
          <MindMapCanvas
            ref="mindMapCanvasRef"
            :project-id="projectId"
            @node-selected="handleNodeSelected"
            @node-updated="handleNodeUpdated"
            @graph-ready="handleGraphReady"
            @case-dragged="handleCaseDragged"
          />
          
          <!-- 布局状态指示器 -->
          <div class="layout-status-indicator">
            <div class="status-badge" :class="{ 'expanded': selectedNode }">
              <span class="status-text">{{ selectedNode ? '详情模式' : '全屏模式' }}</span>
              <div class="status-dot" :class="{ 'active': selectedNode }"></div>
            </div>
          </div>
          
          <!-- 无选中节点时的提示 -->
          <div v-if="!selectedNode" class="no-selection-hint">
            <div class="hint-content">
              <i class="hint-icon">🎯</i>
              <p class="hint-text">点击节点查看详细信息</p>
            </div>
          </div>
        </div>

        <!-- 右侧属性面板 -->
        <transition name="property-panel" mode="out-in">
          <PropertyPanel
            v-if="selectedNode"
            :key="selectedNode.id"
            :selected-node="selectedNode"
            :test-cases="testCases"
            :available-variables="availableVariables"
            @node-updated="handleNodeUpdated"
            @close="handleClosePropertyPanel"
          />
        </transition>
      </div>

      <!-- 快捷键提示 -->
      <KeyboardShortcuts v-if="showShortcuts" @close="showShortcuts = false" />
      
      <!-- 加载遮罩 -->
      <div v-if="globalLoading" class="global-loading">
        <el-loading :loading="true" text="处理中..." />
      </div>
    </PageContainer>
  </Home>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, provide, nextTick } from 'vue';
import { ElMessage, ElLoading } from 'element-plus';
import { useRoute } from 'vue-router';
import axios from 'axios';
import Home from '@/components/HomePage.vue';
import PageContainer from '@/components/PageContainer.vue';
import MindMapToolbar from './components/MindMapToolbar.vue';
import TestCaseList from './components/TestCaseList.vue';
import MindMapCanvas from './components/MindMapCanvas.vue';
import PropertyPanel from './components/PropertyPanel.vue';
import KeyboardShortcuts from './components/KeyboardShortcuts.vue';
import { useMindMapStore } from './composables/useMindMapStore';
import { useMindMapOperations } from './composables/useMindMapOperations';
import { useTestCases } from './composables/useTestCases';
import { useKeyboardShortcuts } from './composables/useKeyboardShortcuts';
import request from '@/utils/request';

const route = useRoute();

// 核心状态
const projectId = ref(route.params.projectId || localStorage.getItem('currentProjectId'));
const mindMapCanvasRef = ref(null);
const graph = ref(null);
const selectedNode = ref(null);
const globalLoading = ref(false);
const showShortcuts = ref(false);
const zoomLevel = ref(1);

// 环境相关状态
const currentEnvId = ref('');
const envList = ref([]);
const envLoading = ref(false);

// 使用组合式函数管理状态和操作
const mindMapStore = useMindMapStore();
const canUndo = computed(() => mindMapStore.canUndo.value);
const canRedo = computed(() => mindMapStore.canRedo.value);
const hasClipboard = computed(() => mindMapStore.hasClipboard.value);

// 使用测试用例管理
const { 
  testCases, 
  testCasesLoading, 
  fetchTestCases 
} = useTestCases(projectId);

// 使用脑图操作组合式函数
const {
  executeNode,
  executeAllNodes,
  saveMap,
  exportMap,
  addNode,
  addParentNode,
  removeNode,
  copyNode,
  pasteNode,
  undo,
  redo
} = useMindMapOperations(graph, selectedNode, mindMapStore);

// 快捷键支持
useKeyboardShortcuts({
  onSave: () => handleSaveMap(),
  onUndo: () => handleUndo(),
  onRedo: () => handleRedo(),
  onCopy: () => handleCopyNode(),
  onPaste: () => handlePasteNode(),
  onDelete: () => handleRemoveNode(),
  onHelp: () => showShortcuts.value = true,
  onEscape: () => {
    selectedNode.value = null;
    showShortcuts.value = false;
  }
});

// 计算可用变量
const availableVariables = computed(() => {
  if (!selectedNode.value || !graph.value) return [];
  return mindMapStore.getAvailableVariables(selectedNode.value.id);
});

// 事件处理函数
const handleGraphReady = (graphInstance) => {
  graph.value = graphInstance;
  mindMapStore.setGraph(graphInstance);
};

const handleNodeSelected = (node) => {
  const wasSelected = !!selectedNode.value;
  const isSelected = !!node;
  
  selectedNode.value = node;
  
  // 当节点选择状态改变时，延迟调整画布大小
  if (wasSelected !== isSelected) {
    nextTick(() => {
      setTimeout(() => {
        if (mindMapCanvasRef.value && mindMapCanvasRef.value.handleResize) {
          mindMapCanvasRef.value.handleResize();
        }
      }, 350); // 等待CSS过渡完成
    });
  }
};

const handleNodeUpdated = (nodeData) => {
  console.log('ApiTestMindMap: handleNodeUpdated called', nodeData);
  
  try {
    // 更新存储中的节点数据
    mindMapStore.updateNode(nodeData);
    
    // 触发图更新
    if (graph.value) {
      const nodeItem = graph.value.findById(nodeData.id);
      if (nodeItem) {
        graph.value.refreshItem(nodeItem);
      }
    }
    
    // 同步更新选中节点的数据
    if (selectedNode.value && selectedNode.value.id === nodeData.id) {
      selectedNode.value = { ...nodeData };
    }
  } catch (error) {
    console.error('ApiTestMindMap: Error updating node:', error);
  }
};

const handleCaseDragged = (testCase, dropTarget) => {
  try {
    // 处理字段映射，确保创建者信息正确
    const normalizedTestCase = {
      ...testCase,
      creator: testCase.creator?.username || testCase.creator || '未知',
      created_at: testCase.created_at || testCase.create_time,
      updated_at: testCase.updated_at || testCase.update_time
    };
    
    let newNode;
    
    // 确保dropTarget存在且有效
    if (!dropTarget) {
      // 如果没有dropTarget，使用默认值
      dropTarget = selectedNode.value?.id || 'root';
    }
    
    if (typeof dropTarget === 'string') {
      // 兼容旧格式：直接传入目标节点ID
      newNode = mindMapStore.createNodeFromTestCase(normalizedTestCase, dropTarget);
    } else if (dropTarget && typeof dropTarget === 'object' && dropTarget.type) {
      // 新格式：传入拖拽目标对象
      newNode = mindMapStore.createNodeFromTestCaseWithTarget(normalizedTestCase, dropTarget);
    } else {
      // 无效的dropTarget，使用默认值
      console.warn('无效的dropTarget，使用默认值:', dropTarget);
      newNode = mindMapStore.createNodeFromTestCase(normalizedTestCase, 'root');
    }
    
    ElMessage.success('添加测试用例成功');
    
    // 选中新创建的节点
    setTimeout(() => {
      if (graph.value) {
        const nodeItem = graph.value.findById(newNode.id);
        if (nodeItem) {
          handleNodeSelected(nodeItem.getModel());
        }
      }
    }, 100);
  } catch (error) {
    console.error('添加测试用例失败:', error);
    ElMessage.error('添加测试用例失败: ' + error.message);
  }
};

// 工具栏操作处理
const handleSaveMap = async () => {
  try {
    globalLoading.value = true;
    await saveMap(projectId.value);
    ElMessage.success('保存成功');
  } catch (error) {
    console.error('保存失败:', error);
    ElMessage.error('保存失败');
  } finally {
    globalLoading.value = false;
  }
};

const handleExportMap = () => {
  try {
    exportMap();
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    ElMessage.error('导出失败');
  }
};

const handleAddNode = () => {
  if (!selectedNode.value) {
    ElMessage.warning('请先选择一个节点');
    return;
  }
  
  try {
    const newNode = addNode(selectedNode.value.id);
    handleNodeSelected(newNode);
    ElMessage.success('添加节点成功');
  } catch (error) {
    console.error('添加节点失败:', error);
    ElMessage.error('添加节点失败');
  }
};

const handleAddParentNode = () => {
  if (!selectedNode.value || selectedNode.value.id === 'root') {
    ElMessage.warning('根节点不能添加父节点');
    return;
  }
  
  try {
    const newNode = addParentNode(selectedNode.value.id);
    handleNodeSelected(newNode);
    ElMessage.success('添加父节点成功');
  } catch (error) {
    console.error('添加父节点失败:', error);
    ElMessage.error('添加父节点失败');
  }
};

const handleRemoveNode = () => {
  if (!selectedNode.value || selectedNode.value.id === 'root') {
    ElMessage.warning('无法删除该节点');
    return;
  }
  
  try {
    removeNode(selectedNode.value.id);
    selectedNode.value = null;
    ElMessage.success('删除节点成功');
  } catch (error) {
    console.error('删除节点失败:', error);
    ElMessage.error('删除节点失败');
  }
};

const handleExecuteSelected = async () => {
  if (!selectedNode.value || !selectedNode.value.data?.testCase) {
    ElMessage.warning('请先选择含测试用例的节点');
    return;
  }
  
  try {
    globalLoading.value = true;
    const result = await executeNode(selectedNode.value.id);
    
    if (result.success) {
      ElMessage.success('测试用例执行成功');
    } else {
      ElMessage.warning('测试用例执行完成，但未通过测试');
    }
  } catch (error) {
    console.error('执行失败:', error);
    ElMessage.error('执行失败');
  } finally {
    globalLoading.value = false;
  }
};

const handleExecuteAll = async () => {
  try {
    globalLoading.value = true;
    const results = await executeAllNodes();
    
    const successCount = results.filter(r => r.success).length;
    const totalCount = results.length;
    
    if (successCount === totalCount) {
      ElMessage.success(`所有测试用例执行成功 (${successCount}/${totalCount})`);
    } else {
      ElMessage.warning(`批量执行完成 (${successCount}/${totalCount} 成功)`);
    }
  } catch (error) {
    console.error('批量执行失败:', error);
    ElMessage.error('批量执行失败');
  } finally {
    globalLoading.value = false;
  }
};

const handleZoomIn = () => {
  if (graph.value) {
    graph.value.zoom(1.2);
    zoomLevel.value = graph.value.getZoom();
  }
};

const handleZoomOut = () => {
  if (graph.value) {
    graph.value.zoom(0.8);
    zoomLevel.value = graph.value.getZoom();
  }
};

const handleResetZoom = () => {
  if (graph.value) {
    // 设置为100%缩放而不是fitView
    graph.value.zoomTo(1.0);
    zoomLevel.value = 1.0;
  }
};

const handleCopyNode = () => {
  if (!selectedNode.value) {
    ElMessage.warning('请先选择要复制的节点');
    return;
  }
  
  copyNode(selectedNode.value.id);
  ElMessage.success('节点已复制');
};

const handlePasteNode = () => {
  if (!selectedNode.value) {
    ElMessage.warning('请先选择目标位置');
    return;
  }
  
  try {
    const newNode = pasteNode(selectedNode.value.id);
    if (newNode) {
      handleNodeSelected(newNode);
      ElMessage.success('节点已粘贴');
    } else {
      ElMessage.warning('剪贴板为空');
    }
  } catch (error) {
    console.error('粘贴失败:', error);
    ElMessage.error('粘贴失败');
  }
};

const handleUndo = () => {
  if (undo()) {
    ElMessage.success('撤销成功');
  } else {
    ElMessage.warning('没有可撤销的操作');
  }
};

const handleRedo = () => {
  if (redo()) {
    ElMessage.success('重做成功');
  } else {
    ElMessage.warning('没有可重做的操作');
  }
};

// 更多菜单事件处理
const handleImportMap = () => {
  // 创建文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';
  input.onchange = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const importData = JSON.parse(e.target.result);
          if (importData.type === 'mindmap' && importData.data) {
            // 导入脑图数据
            if (graph.value) {
              graph.value.data(importData.data);
              graph.value.render();
              // 保持100%缩放，不使用fitView
              graph.value.zoomTo(1.0);
              ElMessage.success('导入脑图成功');
            }
          } else {
            ElMessage.error('无效的脑图文件格式');
          }
        } catch (error) {
          console.error('导入失败:', error);
          ElMessage.error('导入脑图失败：文件格式错误');
        }
      };
      reader.readAsText(file);
    }
  };
  input.click();
};

const handleShowTemplates = () => {
  ElMessage.info('模板管理功能开发中...');
  // TODO: 实现模板管理功能
};

const handleShowSettings = () => {
  ElMessage.info('设置功能开发中...');
  // TODO: 实现设置功能
};

const handleShowHelp = () => {
  showShortcuts.value = true;
};

const handleClosePropertyPanel = () => {
  selectedNode.value = null;
  
  // 立即调整一次（开始过渡时）
  nextTick(() => {
    if (mindMapCanvasRef.value && mindMapCanvasRef.value.handleResize) {
      mindMapCanvasRef.value.handleResize();
    }
  });
  
  // 延迟调整画布大小，等待CSS过渡完成
  nextTick(() => {
    setTimeout(() => {
      if (mindMapCanvasRef.value && mindMapCanvasRef.value.handleResize) {
        mindMapCanvasRef.value.handleResize();
      }
    }, 350); // 等待CSS过渡完成
    
    // 使用强制刷新确保完全填充
    setTimeout(() => {
      if (mindMapCanvasRef.value && mindMapCanvasRef.value.forceRefresh) {
        mindMapCanvasRef.value.forceRefresh();
      }
    }, 500);
  });
};

// 环境相关方法
const fetchEnvList = async () => {
  try {
    envLoading.value = true;
    const response = await axios.get(
      `http://localhost:8081/api/env-suite/list/${projectId.value}`,
      {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      }
    );

    if (response.data.code === 200) {
      envList.value = response.data.data.items || [];
      
      // 如果有默认环境套，自动选中
      if (envList.value.length > 0 && !currentEnvId.value) {
        currentEnvId.value = envList.value[0].id;
        handleEnvChange(currentEnvId.value);
      }
    } else {
      ElMessage.error(response.data.message || '获取环境套列表失败');
    }
  } catch (error) {
    console.error('获取环境套列表失败:', error);
    ElMessage.error('获取环境套列表失败，请检查网络连接');
  } finally {
    envLoading.value = false;
  }
};

const handleEnvChange = async (value) => {
  currentEnvId.value = value;
  
  // 保存到localStorage供executeTestCase使用
  localStorage.setItem('currentEnvId', value);
  
  try {
    await axios.put('http://localhost:8081/api/environment/current/', {
      env_id: value
    }, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    ElMessage.success('环境套切换成功');
  } catch (error) {
    console.error('切换环境套失败:', error);
    ElMessage.warning('环境套已选择，但服务器更新失败');
  }
};

// 提供数据给子组件
provide('projectId', projectId);
provide('mindMapStore', mindMapStore);

// 生命周期
onMounted(async () => {
  if (projectId.value) {
    try {
      // 并行加载测试用例和环境列表
      await Promise.all([
        fetchTestCases(),
        fetchEnvList()
      ]);
    } catch (error) {
      console.error('ApiTestMindMap: Failed to load data:', error);
    }
  }
});

onUnmounted(() => {
  mindMapStore.cleanup();
});
</script>

<style scoped>
.environment-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 12px;
  box-shadow: 
    0 4px 12px rgba(0, 0, 0, 0.05),
    0 2px 4px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.env-label {
  font-size: 14px;
  font-weight: 500;
  color: #4a5568;
  white-space: nowrap;
}

.content-container {
  display: grid;
  gap: 20px;
  height: calc(100vh - 240px);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 16px;
  padding: 24px;
  box-shadow: 
    0 20px 40px rgba(0,0,0,0.1),
    0 8px 16px rgba(0,0,0,0.08),
    inset 0 1px 0 rgba(255,255,255,0.6);
  overflow: hidden;
  position: relative;
  transition: grid-template-columns 0.3s ease;
}

/* 动态布局：根据是否有选中节点调整列布局 */
.content-container.has-selected-node {
  grid-template-columns: 320px 1fr 380px;
}

.content-container.no-selected-node {
  grid-template-columns: 320px 1fr;
}

/* 属性面板过渡动画 */
.property-panel-enter-active,
.property-panel-leave-active {
  transition: all 0.3s ease;
  transform-origin: right center;
}

.property-panel-enter-from {
  opacity: 0;
  transform: translateX(100%) scale(0.9);
}

.property-panel-leave-to {
  opacity: 0;
  transform: translateX(100%) scale(0.9);
}

.property-panel-enter-to,
.property-panel-leave-from {
  opacity: 1;
  transform: translateX(0) scale(1);
}

/* 画布容器样式 */
.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* 布局状态指示器 */
.layout-status-indicator {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 10;
  pointer-events: none;
}

.status-badge {
  background: rgba(102, 126, 234, 0.9);
  border-radius: 20px;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  transform: scale(0.9);
  opacity: 0.8;
}

.status-badge.expanded {
  background: rgba(82, 196, 26, 0.9);
  transform: scale(1);
  opacity: 1;
}

.status-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
}

.status-dot.active {
  background: #fff;
  box-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
}

/* 无选中节点提示 */
.no-selection-hint {
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 10;
  pointer-events: none;
}

.hint-content {
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(102, 126, 234, 0.3);
  border-radius: 12px;
  padding: 16px 20px;
  box-shadow: 
    0 8px 24px rgba(0, 0, 0, 0.1),
    0 4px 8px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  gap: 12px;
  animation: hint-float 3s ease-in-out infinite;
}

.hint-icon {
  font-size: 20px;
  animation: hint-pulse 2s ease-in-out infinite;
}

.hint-text {
  margin: 0;
  font-size: 14px;
  color: #667eea;
  font-weight: 500;
  white-space: nowrap;
}

@keyframes hint-float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes hint-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

.content-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255,255,255,0.1) 0%, 
    rgba(255,255,255,0.05) 50%, 
    rgba(0,0,0,0.02) 100%);
  border-radius: 16px;
  pointer-events: none;
}

.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (max-width: 1600px) {
  .content-container.has-selected-node {
    grid-template-columns: 300px 1fr 360px;
    gap: 18px;
  }
  
  .content-container.no-selected-node {
    grid-template-columns: 300px 1fr;
    gap: 18px;
  }
}

@media (max-width: 1400px) {
  .content-container.has-selected-node {
    grid-template-columns: 280px 1fr 340px;
    gap: 16px;
    padding: 20px;
  }
  
  .content-container.no-selected-node {
    grid-template-columns: 280px 1fr;
    gap: 16px;
    padding: 20px;
  }
}

@media (max-width: 1200px) {
  .content-container.has-selected-node {
    grid-template-columns: 1fr 320px;
    gap: 16px;
    padding: 16px;
  }
  
  .content-container.no-selected-node {
    grid-template-columns: 1fr;
    gap: 16px;
    padding: 16px;
  }
}

@media (max-width: 992px) {
  .content-container.has-selected-node,
  .content-container.no-selected-node {
    grid-template-columns: 1fr;
    height: auto;
    min-height: calc(100vh - 180px);
    gap: 16px;
    padding: 16px;
  }
  
  .no-selection-hint {
    top: 10px;
    right: 10px;
  }
  
  .hint-content {
    padding: 12px 16px;
  }
  
  .hint-text {
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .content-container.has-selected-node,
  .content-container.no-selected-node {
    grid-template-columns: 1fr;
    height: auto;
    min-height: calc(100vh - 160px);
    gap: 12px;
    padding: 12px;
    margin: 8px;
    border-radius: 12px;
  }
  
  .no-selection-hint,
  .layout-status-indicator {
    display: none;
  }
}

@media (max-width: 480px) {
  .content-container.has-selected-node,
  .content-container.no-selected-node {
    height: auto;
    min-height: calc(100vh - 140px);
    gap: 8px;
    padding: 8px;
    margin: 4px;
    border-radius: 8px;
  }
}


</style> 