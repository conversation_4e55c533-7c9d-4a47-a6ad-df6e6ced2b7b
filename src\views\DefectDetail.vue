<template>
  <Home>
    <div class="defect-detail-container" ref="detailContainer">
      <div class="page-header">
        <div class="header-left">
          <el-button @click="goBack">返回</el-button>
          <h2>缺陷详情</h2>
          <el-tag :type="getStatusType(getStatusName(defect.status))">{{ getStatusName(defect.status) }}</el-tag>
        </div>
        <div class="header-actions">
          <el-dropdown @command="handleStatusChange" class="status-dropdown">
            <el-button type="info">
              更改状态 <el-icon><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item v-for="item in statusOptions" :key="item.code" :command="item.code">
                  设为{{ item.name }}
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button type="primary" @click="editDefect">编辑</el-button>
        </div>
      </div>

      <el-row :gutter="20" v-loading="loading">
        <el-col :span="16">
          <!-- 主要信息卡片 -->
          <el-card class="detail-card">
            <template #header>
              <div class="card-header">
                <h3>{{ defect.title }}</h3>
                <div class="meta-info">
                  <span>缺陷ID: {{ defect.id }}</span>
                  <span>创建人: {{ getUsername(defect.creator) }}</span>
                  <span>创建时间: {{ defect.createTime }}</span>
                </div>
              </div>
            </template>
            <div class="detail-content">
              <el-descriptions :column="2" border size="default">
                <el-descriptions-item label="所属项目">{{ defect.project }}</el-descriptions-item>
                <el-descriptions-item label="所属版本">{{ defect.version }}</el-descriptions-item>
                <el-descriptions-item label="严重程度">{{ getSeverityName(defect.severity) }}</el-descriptions-item>
                <el-descriptions-item label="优先级">{{ getPriorityName(defect.priority) }}</el-descriptions-item>
                <el-descriptions-item label="当前状态">{{ getStatusName(defect.status) }}</el-descriptions-item>
                <el-descriptions-item label="当前指派">{{ getUsername(defect.assignee) }}</el-descriptions-item>
                <el-descriptions-item label="解决结果" :span="2">{{ defect.resolution || '未解决' }}</el-descriptions-item>
              </el-descriptions>
              
              <h4>描述</h4>
              <div v-html="defect.description" class="rich-content description"></div>
              
              <h4>重现步骤</h4>
              <div v-html="defect.steps" class="rich-content steps"></div>

              <!-- 附件部分 -->
              <div class="attachments" v-if="defect.attachments && defect.attachments.length > 0">
                <h4>附件</h4>
                <div class="attachment-list">
                  <div v-for="(file, index) in defect.attachments" :key="index" class="attachment-item">
                    <el-image 
                      v-if="isImage(file.url)" 
                      :src="file.url" 
                      :preview-src-list="getImageSrcList()" 
                      fit="cover" 
                      class="attachment-image"
                    ></el-image>
                    <div v-else class="file-item">
                      <el-icon><document /></el-icon>
                      <span>{{ file.name }}</span>
                      <el-button type="primary" size="small" @click="downloadFile(file)">下载</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-card>

          <!-- 评论及历史记录卡片 -->
          <el-card class="detail-card">
            <template #header>
              <div class="card-header">
                <el-tabs v-model="activeTab">
                  <el-tab-pane label="评论" name="comments"></el-tab-pane>
                  <el-tab-pane label="历史记录" name="history"></el-tab-pane>
                </el-tabs>
              </div>
            </template>
            
            <!-- 评论列表 -->
            <div v-if="activeTab === 'comments'">
              <div class="comment-list">
                <div v-for="(comment, index) in comments" :key="index" class="comment-item">
                  <div class="comment-header">
                    <el-avatar :size="32">{{ getUserInitial(comment.user) }}</el-avatar>
                    <span class="comment-user">{{ getUsername(comment.user) }}</span>
                    <span class="comment-time">{{ comment.time }}</span>
                  </div>
                  <div class="comment-content">{{ comment.content }}</div>
                </div>
              </div>
              
              <!-- 添加评论 -->
              <div class="add-comment">
                <el-input
                  v-model="newComment"
                  type="textarea"
                  :rows="3"
                  placeholder="添加评论..."
                ></el-input>
                <el-button type="primary" @click="addComment">提交评论</el-button>
              </div>
            </div>
            
            <!-- 历史记录 -->
            <div v-else-if="activeTab === 'history'">
              <el-timeline v-if="activities && activities.length > 0">
                <el-timeline-item
                  v-for="(activity, index) in activities"
                  :key="index"
                  :timestamp="activity.timestamp || ''"
                  :type="activity.type || 'primary'"
                  :color="getActivityColor(activity.type)"
                >
                  <div class="activity-content">
                    <span class="activity-user" v-if="activity.user">{{ getUsername(activity.user) }}：</span>
                    <span>{{ activity.content }}</span>
                  </div>
                </el-timeline-item>
              </el-timeline>
              <el-empty v-else description="暂无历史记录" />
            </div>
          </el-card>
        </el-col>
        
        <!-- 右侧相关信息 -->
        <el-col :span="8">
          <el-card class="detail-card">
            <template #header>
              <div class="card-header">
                <h3>相关信息</h3>
              </div>
            </template>
            
            <div class="related-info">
              <div class="info-item">
                <h4>指派信息</h4>
                <div class="assignee-info">
                  <span>当前指派: {{ getUsername(defect.assignee) || '未指派' }}</span>
                  <el-button size="small" @click="showAssignDialog = true">重新指派</el-button>
                </div>
              </div>
              
              <div class="info-item">
                <h4>关联测试用例</h4>
                <div v-if="defect.relatedTestcases && defect.relatedTestcases.length > 0">
                  <div v-for="(testcase, index) in defect.relatedTestcases" :key="index" class="related-item">
                    <el-link :href="`/testcases?id=${testcase.id}`" type="primary">{{ testcase.name }}</el-link>
                  </div>
                </div>
                <div v-else class="empty-related">
                  <span>暂无关联测试用例</span>
                  <el-button size="small" @click="showRelateDialog = true">关联测试用例</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 指派对话框 -->
    <el-dialog
      title="指派缺陷"
      v-model="showAssignDialog"
      width="30%"
    >
      <el-form>
        <el-form-item label="选择指派人">
          <el-select v-model="selectedAssignee" placeholder="请选择指派人">
            <el-option
              v-for="user in userList"
              :key="user.id"
              :label="user.username"
              :value="user.id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showAssignDialog = false">取消</el-button>
          <el-button type="primary" @click="assignDefect">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 关联测试用例对话框 -->
    <el-dialog
      title="关联测试用例"
      v-model="showRelateDialog"
      width="50%"
    >
      <el-table :data="testcaseList" @selection-change="handleTestcaseSelection" border>
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="用例名称"></el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showRelateDialog = false">取消</el-button>
          <el-button type="primary" @click="relateTestcases">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </Home>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Document, ArrowDown } from '@element-plus/icons-vue';
import Home from '@/components/HomePage.vue';
import { 
  getDefectDetail, 
  changeDefectStatus, 
  addDefectComment, 
  assignDefect as assignDefectApi,
  relateTestcases as relateTestcasesApi,
  getDefectOptions,
  uploadAttachment
} from '@/api/defect';
import { getUserList } from '@/api/user';

const route = useRoute();
const router = useRouter();
const loading = ref(true);
const activeTab = ref('comments');
const newComment = ref('');
const showAssignDialog = ref(false);
const showRelateDialog = ref(false);
const selectedAssignee = ref('');
const selectedTestcases = ref([]);
const statusOptions = ref([]);
const defectId = ref('');
const detailContainer = ref(null); // 添加容器引用

// 初始化空数据
const defect = reactive({
  id: '',
  title: '',
  description: '',
  steps: '',
  status: '',
  severity: '',
  priority: '',
  creator: '',
  createTime: '',
  assignee: '',
  project: '',
  version: '',
  resolution: '',
  attachments: [],
  relatedTestcases: []
});

const comments = ref([]);
const activities = ref([]);
const userList = ref([]);
const testcaseList = ref([]);

// 获取状态类型
// 获取状态对应的标签类型
const getStatusType = (status) => {
  const statusMap = {
    '新建': 'danger',
    '处理中': 'warning',
    '已解决': 'success',
    '已关闭': 'info',
    '已拒绝': ''
  };
  return statusMap[status] || 'info';
};

// 获取状态的中文名称
const getStatusName = (statusCode) => {
  const statusMap = {
    'new': '新建',
    'processing': '处理中',
    'resolved': '已解决',
    'closed': '已关闭',
    'rejected': '已拒绝'
  };
  return statusMap[statusCode] || statusCode;
};

// 获取优先级的中文名称
const getPriorityName = (priorityCode) => {
  const priorityMap = {
    'critical': '紧急',
    'high': '高',
    'medium': '中',
    'low': '低'
  };
  return priorityMap[priorityCode] || priorityCode;
};

// 获取严重程度的中文名称（通常与优先级相同，但可能有不同的定义）
const getSeverityName = (severityCode) => {
  const severityMap = {
    'critical': '严重',
    'major': '主要',
    'normal': '一般',
    'minor': '次要',
    'trivial': '轻微'
  };
  return severityMap[severityCode] || severityCode;
};

// 获取活动类型对应的颜色
const getActivityColor = (type) => {
  const colorMap = {
    'primary': '#409EFF', // 默认蓝色
    'success': '#67C23A', // 成功绿色
    'warning': '#E6A23C', // 警告黄色
    'danger': '#F56C6C', // 危险红色
    'info': '#909399'    // 信息灰色
  };
  return colorMap[type] || colorMap.primary;
};

// 从用户对象中提取用户名
const getUsername = (user) => {
  if (!user) return '未指派';
  
  // 如果是字符串，直接返回
  if (typeof user === 'string' && !user.startsWith('{') && !user.startsWith('[')) return user;
  
  try {
    // 如果是JSON字符串，尝试解析
    if (typeof user === 'string' && (user.startsWith('{') || user.startsWith('['))) {
      const userObj = JSON.parse(user);
      return userObj.username || '未知用户';
    }
    
    // 如果已经是对象
    if (typeof user === 'object' && user !== null) {
      return user.username || '未知用户';
    }
    
    return String(user || '');
  } catch (error) {
    console.error('解析用户信息出错:', error);
    return String(user || '');
  }
};

// 获取用户名的首字母（用于头像显示）
const getUserInitial = (user) => {
  const username = getUsername(user);
  return username && username.length > 0 ? username.charAt(0) : '?';
};

// 获取缺陷详情数据
const fetchDefectDetail = async (id) => {
  try {
    loading.value = true;
    const res = await getDefectDetail(id);
    if (res.data.code === 200) {
      const data = res.data.data;
      
      // 将API返回的数据映射到组件的响应式数据
      Object.assign(defect, data);
      
      // 处理描述内容的格式化
      if (defect.description) {
        console.log('原始描述内容:', defect.description);
        
        // 处理HTML实体转义
        defect.description = defect.description
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&quot;/g, '"')
          .replace(/&#39;/g, "'")
          .replace(/&amp;/g, '&');
        
        // 如果内容不包含HTML标签，则将换行符转换为<br>
        if (!defect.description.includes('<') && !defect.description.includes('>')) {
          defect.description = defect.description.replace(/\n/g, '<br>');
        }
        
        // 处理长URL，添加换行机会
        defect.description = defect.description.replace(
          /(https?:\/\/[^\s<>]+)/g, 
          '<span class="url-text">$1</span>'
        );
        
        // 处理技术信息（包含特殊字符的长文本）
        defect.description = defect.description.replace(
          /(\w+:\s*[^\s]+(?:\s+[^\s]+)*(?:\s*[|→←→]\s*[^\s]+)*)/g,
          function(match) {
            if (match.length > 50) {
              return `<div class="tech-text">${match}</div>`;
            }
            return match;
          }
        );
        
        // 处理JSON格式的文本
        if (defect.description.includes('{') && defect.description.includes('}')) {
          defect.description = defect.description.replace(
            /(\{[^{}]*\})/g,
            '<div class="json-block">$1</div>'
          );
        }
        
        console.log('处理后的描述内容:', defect.description);
      }
      
      // 处理重现步骤内容的格式化
      if (defect.steps) {
        console.log('原始步骤内容:', defect.steps);
        
        // 处理HTML实体转义
        defect.steps = defect.steps
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&quot;/g, '"')
          .replace(/&#39;/g, "'")
          .replace(/&amp;/g, '&');
        
        // 如果内容不包含HTML标签，则将换行符转换为<br>
        if (!defect.steps.includes('<') && !defect.steps.includes('>')) {
          defect.steps = defect.steps.replace(/\n/g, '<br>');
        }
        
        // 处理长URL，添加换行机会
        defect.steps = defect.steps.replace(
          /(https?:\/\/[^\s<>]+)/g, 
          '<span class="url-text">$1</span>'
        );
        
        // 处理技术信息（包含特殊字符的长文本）
        defect.steps = defect.steps.replace(
          /(\w+:\s*[^\s]+(?:\s+[^\s]+)*(?:\s*[|→←→]\s*[^\s]+)*)/g,
          function(match) {
            if (match.length > 50) {
              return `<div class="tech-text">${match}</div>`;
            }
            return match;
          }
        );
        
        // 处理JSON格式的文本
        if (defect.steps.includes('{') && defect.steps.includes('}')) {
          defect.steps = defect.steps.replace(
            /(\{[^{}]*\})/g,
            '<div class="json-block">$1</div>'
          );
        }
        
        console.log('处理后的步骤内容:', defect.steps);
      }
      
      // 确保评论数据是数组格式
      if (Array.isArray(data.comments)) {
        comments.value = data.comments;
      } else {
        comments.value = [];
        console.warn('评论数据不是数组格式');
      }
      
      // 确保活动数据是数组格式且每项都有必要的属性
      if (Array.isArray(data.activities)) {
        activities.value = data.activities.map(activity => ({
          content: activity.content || '',
          timestamp: activity.timestamp || new Date().toLocaleString(),
          type: activity.type || 'primary',
          user: activity.user || defect.creator // 默认使用创建人作为用户
        }));
      } else {
        activities.value = [];
        console.warn('活动数据不是数组格式');
      }
      
      // 获取可选的状态和用户列表
      await fetchOptions();
    } else {
      ElMessage.error('获取缺陷详情失败');
    }
  } catch (error) {
    console.error('获取缺陷详情出错:', error);
    ElMessage.error('获取缺陷详情时发生错误');
  } finally {
    loading.value = false;
  }
};

// 获取筛选条件选项
const fetchOptions = async () => {
  try {
    const res = await getDefectOptions();
    if (res.data.code === 200) {
      const { status, testPlans } = res.data.data;
      statusOptions.value = status;
      
      // 获取用户列表数据
      const userRes = await getUserList();
      if (userRes.data.code === 200) {
        userList.value = userRes.data.data.items || [];
      } else {
        // 加载失败使用默认数据
        userList.value = [
          { id: 1, username: '李四' },
          { id: 2, username: '王五' },
          { id: 3, username: '赵六' }
        ];
      }
      
      // 模拟测试用例列表，实际项目中需要调用测试用例列表接口
      testcaseList.value = [
        { id: 'TC001', name: '登录功能测试' },
        { id: 'TC002', name: '注册功能测试' },
        { id: 'TC003', name: '移动端兼容性测试' }
      ];
    }
  } catch (error) {
    console.error('获取选项出错:', error);
    // 发生错误时使用默认数据
    userList.value = [
      { id: 1, username: '李四' },
      { id: 2, username: '王五' },
      { id: 3, username: '赵六' }
    ];
  }
};

// 处理状态变更
const handleStatusChange = async (statusCode) => {
  try {
    const res = await changeDefectStatus(defectId.value, statusCode);
    if (res.data.code === 200) {
      const status = statusOptions.value.find(item => item.code === statusCode)?.name || statusCode;
      defect.status = status;
      
      // 更新历史记录
      activities.value.unshift({
        content: `状态变更为"${status}"`,
        timestamp: new Date().toLocaleString(),
        type: 'warning',
        user: { username: 'admin' } // 当前用户，实际应从登录信息获取
      });
      
      ElMessage.success(`状态已更新为${status}`);
    } else {
      ElMessage.error('更新状态失败');
    }
  } catch (error) {
    console.error('更新状态出错:', error);
    ElMessage.error('更新状态时发生错误');
  }
};

// 添加评论
const addComment = async () => {
  if (!newComment.value.trim()) {
    ElMessage.warning('评论内容不能为空');
    return;
  }
  
  try {
    const res = await addDefectComment(defectId.value, newComment.value);
    if (res.data.code === 200) {
      const commentData = res.data.data || {
        user: '当前用户', 
        content: newComment.value,
        time: new Date().toLocaleString()
      };
      
      // 添加到评论列表
      comments.value.push(commentData);
      
      // 添加到历史记录
      activities.value.unshift({
        content: '添加了评论',
        timestamp: new Date().toLocaleString(),
        type: 'info',
        user: commentData.user // 使用评论用户作为活动用户
      });
      
      newComment.value = ''; // 清空输入框
      ElMessage.success('评论已添加');
    } else {
      ElMessage.error('添加评论失败');
    }
  } catch (error) {
    console.error('添加评论出错:', error);
    ElMessage.error('添加评论时发生错误');
  }
};

// 判断是否为图片
const isImage = (url) => {
  return /\.(jpg|jpeg|png|gif|webp)$/i.test(url);
};

// 获取图片列表
const getImageSrcList = () => {
  return defect.attachments
    .filter(file => isImage(file.url))
    .map(file => file.url);
};

// 下载文件
const downloadFile = (file) => {
  window.open(file.url, '_blank');
};

// 指派缺陷
const assignDefect = async () => {
  if (!selectedAssignee.value) {
    ElMessage.warning('请选择指派人');
    return;
  }
  
  try {
    const res = await assignDefectApi(defectId.value, selectedAssignee.value);
    if (res.data.code === 200) {
      const assignee = userList.value.find(user => user.id === selectedAssignee.value);
      if (assignee) {
        defect.assignee = assignee.username;
        
        // 更新历史记录
        activities.value.unshift({
          content: `重新指派给${assignee.username}`,
          timestamp: new Date().toLocaleString(),
          type: 'success',
          user: { username: 'admin' } // 当前用户，实际应从登录信息获取
        });
        
        ElMessage.success(`已指派给${assignee.username}`);
        showAssignDialog.value = false;
      }
    } else {
      ElMessage.error('指派缺陷失败');
    }
  } catch (error) {
    console.error('指派缺陷出错:', error);
    ElMessage.error('指派缺陷时发生错误');
  }
};

// 处理测试用例选择
const handleTestcaseSelection = (selection) => {
  selectedTestcases.value = selection;
};

// 关联测试用例
const relateTestcases = async () => {
  if (selectedTestcases.value.length === 0) {
    ElMessage.warning('请选择要关联的测试用例');
    return;
  }
  
  try {
    const testcaseIds = selectedTestcases.value.map(item => item.id);
    const res = await relateTestcasesApi(defectId.value, testcaseIds);
    
    if (res.data.code === 200) {
      // 更新测试用例列表
      defect.relatedTestcases = selectedTestcases.value.map(item => ({
        id: item.id,
        name: item.name
      }));
      
      // 更新历史记录
      activities.value.unshift({
        content: `关联了${selectedTestcases.value.length}个测试用例`,
        timestamp: new Date().toLocaleString(),
        type: 'info',
        user: { username: 'admin' } // 当前用户，实际应从登录信息获取
      });
      
      ElMessage.success('测试用例关联成功');
      showRelateDialog.value = false;
    } else {
      ElMessage.error('关联测试用例失败');
    }
  } catch (error) {
    console.error('关联测试用例出错:', error);
    ElMessage.error('关联测试用例时发生错误');
  }
};

// 返回上一页
const goBack = () => {
  router.back();
};

// 编辑缺陷
const editDefect = () => {
  router.push({
    path: '/defect-list',
    query: { edit: defectId.value }
  });
};

// 初始化滚动行为
const initScrollBehavior = () => {
  if (detailContainer.value) {
    // 确保容器可以滚动
    detailContainer.value.style.overflowY = 'auto';
    
    // 滚动到顶部
    detailContainer.value.scrollTop = 0;
  }
};

onMounted(async () => {
  // 从路由获取缺陷ID（支持路径参数和查询参数两种方式）
  const id = route.params.id || route.query.id;
  if (id) {
    defectId.value = id;
    await fetchDefectDetail(id);
    
    // 数据加载完成后初始化滚动行为
  setTimeout(() => {
      initScrollBehavior();
    }, 100);
  } else {
    ElMessage.warning('缺陷ID不存在');
    router.push('/defect-list');
  }
});
</script>

<style scoped>
.defect-detail-container {
  padding: 20px;
  height: calc(100vh - 60px); /* 减去可能的头部高度 */
  overflow-y: auto; /* 启用垂直滚动 */
  padding-bottom: 100px; /* 增加底部内边距，确保底部元素可见 */
}
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}
.header-actions {
  display: flex;
  gap: 12px;
}
.detail-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}
.card-header {
  display: flex;
  flex-direction: column;
}
.meta-info {
  display: flex;
  gap: 16px;
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}
.detail-content {
  margin-bottom: 20px;
}
.detail-content h4 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: 500;
}
/* 富文本内容样式 */
.rich-content {
  padding: 16px;
  background-color: #fafafa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  line-height: 1.8;
  font-size: 14px;
  color: #303133;
  min-height: 60px;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  max-width: 100%;
}

.rich-content.description {
  margin-bottom: 20px;
}

.rich-content.steps {
  margin-bottom: 20px;
}

/* 图片样式 */
.rich-content :deep(img) {
  max-width: 100%;
  height: auto;
  display: block;
  margin: 12px 0;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease;
}

.rich-content :deep(img:hover) {
  transform: scale(1.02);
}

/* 标题样式 */
.rich-content :deep(h1),
.rich-content :deep(h2),
.rich-content :deep(h3),
.rich-content :deep(h4),
.rich-content :deep(h5),
.rich-content :deep(h6) {
  margin: 16px 0 8px 0;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
}

.rich-content :deep(h1) { font-size: 24px; }
.rich-content :deep(h2) { font-size: 20px; }
.rich-content :deep(h3) { font-size: 18px; }
.rich-content :deep(h4) { font-size: 16px; }
.rich-content :deep(h5) { font-size: 14px; }
.rich-content :deep(h6) { font-size: 12px; }

/* 段落样式 */
.rich-content :deep(p) {
  margin: 8px 0;
  line-height: 1.7;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
}

/* 处理长文本内容 */
.rich-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* URL和技术文本样式 */
.rich-content :deep(span),
.rich-content :deep(div) {
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
}

/* 处理包含URL的文本 */
.rich-content :deep(*) {
  word-wrap: break-word;
  overflow-wrap: break-word;
}

/* 列表样式 */
.rich-content :deep(ul),
.rich-content :deep(ol) {
  margin: 12px 0;
  padding-left: 24px;
}

.rich-content :deep(li) {
  margin: 4px 0;
  line-height: 1.6;
}

.rich-content :deep(ul li) {
  list-style-type: disc;
}

.rich-content :deep(ol li) {
  list-style-type: decimal;
}

/* 嵌套列表样式 */
.rich-content :deep(ul ul li),
.rich-content :deep(ol ul li) {
  list-style-type: circle;
}

.rich-content :deep(ul ol li),
.rich-content :deep(ol ol li) {
  list-style-type: lower-alpha;
}

/* 链接样式 */
.rich-content :deep(a) {
  color: #409eff;
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.rich-content :deep(a:hover) {
  color: #66b1ff;
  border-bottom-color: #66b1ff;
}

/* 代码样式 */
.rich-content :deep(code) {
  background-color: #f1f2f6;
  color: #e74c3c;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
}

.rich-content :deep(pre) {
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 16px 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  line-height: 1.5;
}

.rich-content :deep(pre code) {
  background-color: transparent;
  color: inherit;
  padding: 0;
  border-radius: 0;
}

/* 引用样式 */
.rich-content :deep(blockquote) {
  margin: 16px 0;
  padding: 12px 16px;
  background-color: #f8f9fa;
  border-left: 4px solid #409eff;
  border-radius: 0 4px 4px 0;
  color: #5a6c7d;
  font-style: italic;
}

.rich-content :deep(blockquote p) {
  margin: 0;
}

/* 表格样式 */
.rich-content :deep(table) {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  background-color: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.rich-content :deep(table th),
.rich-content :deep(table td) {
  padding: 12px 16px;
  text-align: left;
  border-bottom: 1px solid #e4e7ed;
}

.rich-content :deep(table th) {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #1f2937;
}

.rich-content :deep(table tr:hover) {
  background-color: #f8f9fa;
}

.rich-content :deep(table tr:last-child td) {
  border-bottom: none;
}

/* 分割线样式 */
.rich-content :deep(hr) {
  margin: 24px 0;
  border: none;
  height: 1px;
  background: linear-gradient(to right, transparent, #e4e7ed, transparent);
}

/* 强调样式 */
.rich-content :deep(strong),
.rich-content :deep(b) {
  font-weight: 600;
  color: #1f2937;
}

.rich-content :deep(em),
.rich-content :deep(i) {
  font-style: italic;
  color: #6b7280;
}

/* 删除线样式 */
.rich-content :deep(del),
.rich-content :deep(s) {
  text-decoration: line-through;
  color: #9ca3af;
}

/* 下划线样式 */
.rich-content :deep(u) {
  text-decoration: underline;
  text-decoration-color: #409eff;
}

/* 标记样式 */
.rich-content :deep(mark) {
  background-color: #fef3c7;
  color: #92400e;
  padding: 2px 4px;
  border-radius: 3px;
}

/* 空内容提示 */
.rich-content:empty::before {
  content: '暂无内容';
  color: #9ca3af;
  font-style: italic;
}

/* 技术文本特殊处理 */
.rich-content :deep(.tech-text) {
  background-color: #f6f8fa;
  padding: 8px 12px;
  border-radius: 4px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
  word-break: break-all;
  white-space: pre-wrap;
  margin: 8px 0;
  border-left: 3px solid #409eff;
}

/* URL链接特殊样式 */
.rich-content :deep(.url-text) {
  color: #409eff;
  word-break: break-all;
  text-decoration: underline;
  cursor: pointer;
}

.rich-content :deep(.url-text:hover) {
  color: #66b1ff;
  background-color: rgba(64, 158, 255, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
}

/* JSON或代码块样式 */
.rich-content :deep(.json-block) {
  background-color: #2d3748;
  color: #e2e8f0;
  padding: 12px;
  border-radius: 6px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
  margin: 12px 0;
  white-space: pre;
  word-wrap: normal;
}

/* 长文本自动换行 */
.rich-content :deep(.long-text) {
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
  line-height: 1.6;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .rich-content {
    padding: 12px;
    font-size: 13px;
  }
  
  .rich-content :deep(table) {
    font-size: 12px;
  }
  
  .rich-content :deep(table th),
  .rich-content :deep(table td) {
    padding: 8px 10px;
  }
  
  .rich-content :deep(pre) {
    padding: 12px;
    font-size: 12px;
  }
  
  .rich-content :deep(.tech-text) {
    font-size: 11px;
    padding: 6px 8px;
  }
  
  .rich-content :deep(.json-block) {
    font-size: 11px;
    padding: 8px;
  }
}

.attachments {
  margin-top: 20px;
}
.attachment-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}
.attachment-image {
  width: 120px;
  height: 80px;
  border-radius: 4px;
  object-fit: cover;
}
.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f2f6fc;
  border-radius: 4px;
}
.comment-list {
  margin-bottom: 20px;
}
.comment-item {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #eee;
}
.comment-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}
.comment-user {
  font-weight: 500;
}
.comment-time {
  font-size: 12px;
  color: #999;
}
.comment-content {
  padding: 0 0 0 40px;
}
.add-comment {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 30px; /* 增加底部边距 */
}
.add-comment button {
  align-self: flex-end;
  margin-bottom: 20px; /* 确保按钮有足够的底部边距 */
}
.related-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.info-item h4 {
  margin-bottom: 12px;
}
.assignee-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.related-item {
  margin-bottom: 8px;
}
.empty-related {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #999;
}
.status-dropdown {
  margin-right: 8px;
}

/* 历史记录样式 */
.activity-content {
  display: flex;
  flex-direction: column;
}

.activity-user {
  font-weight: 600;
  margin-bottom: 4px;
  color: #409EFF;
}

/* 响应式布局调整 */
@media screen and (max-width: 768px) {
  .defect-detail-container {
    padding: 10px;
    height: calc(100vh - 50px);
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .meta-info {
    flex-direction: column;
    gap: 5px;
  }
}
</style>