import request from '@/utils/request'

/**
 * 智能测试用例编辑建议API
 * 基于后端提供的实际接口规范
 */

// 分析失败测试用例并生成建议
// POST /api/test-case/analyze-failure/
export function analyzeFailureAndSuggest(data) {
  return request({
    url: '/api/test-case/analyze-failure/',
    method: 'post',
    data: {
      test_case_id: data.test_case_id,
      execution_result_id: data.execution_result_id || null, // 可选参数
      analysis_options: data.analysis_options || {} // 分析选项
    }
  })
}

// 获取测试用例的编辑建议
// GET /api/test-case/{test_case_id}/suggestions/
export function getTestCaseSuggestions(testCaseId, params = {}) {
  return request({
    url: `/api/test-case/${testCaseId}/suggestions/`,
    method: 'get',
    params: {
      status: params.status || 'pending', // pending, applied, rejected
      page: params.page || 1,
      page_size: params.page_size || 20,
      field_name: params.field_name // 可选：筛选特定字段的建议
    }
  })
}

// 应用选中的编辑建议
// POST /api/test-case/suggestions/apply/
export function applySuggestions(data) {
  return request({
    url: '/api/test-case/suggestions/apply/',
    method: 'post',
    data: {
      suggestion_ids: data.suggestion_ids, // 数组：要应用的建议ID列表
      apply_reason: data.apply_reason || '用户手动应用', // 应用原因
      auto_save: data.auto_save !== false // 是否自动保存，默认true
    }
  })
}

// 拒绝选中的编辑建议
// POST /api/test-case/suggestions/reject/
export function rejectSuggestions(data) {
  return request({
    url: '/api/test-case/suggestions/reject/',
    method: 'post',
    data: {
      suggestion_ids: data.suggestion_ids, // 数组：要拒绝的建议ID列表
      reject_reason: data.reject_reason || '用户手动拒绝' // 拒绝原因
    }
  })
}

// 获取测试用例编辑历史
// GET /api/test-case/{test_case_id}/edit-history/
export function getTestCaseEditHistory(testCaseId, params = {}) {
  return request({
    url: `/api/test-case/${testCaseId}/edit-history/`,
    method: 'get',
    params: {
      page: params.page || 1,
      page_size: params.page_size || 20,
      is_ai_suggested: params.is_ai_suggested, // 筛选AI建议的修改
      field_name: params.field_name, // 筛选特定字段的历史
      start_time: params.start_time, // 时间范围筛选
      end_time: params.end_time
    }
  })
}

// 获取测试用例详情（用于智能分析）
export function getTestCaseForAnalysis(testCaseId) {
  return request({
    url: `/api/test-case/${testCaseId}/detail/`,
    method: 'get'
  })
}

// 获取失败测试用例列表（用于批量分析）
export function getFailedTestCases(params = {}) {
  return request({
    url: '/api/test-case/failed-list/',
    method: 'get',
    params: {
      project_id: params.project_id,
      page: params.page || 1,
      page_size: params.page_size || 20,
      time_range: params.time_range, // 如：24h, 7d, 30d
      failure_type: params.failure_type // 失败类型筛选
    }
  })
}

// 批量分析失败测试用例
export function batchAnalyzeFailures(data) {
  return request({
    url: '/api/test-case/batch-analyze-failure/',
    method: 'post',
    data: {
      test_case_ids: data.test_case_ids,
      analysis_options: data.analysis_options || {
        include_performance: true,
        include_compatibility: true,
        confidence_threshold: 0.7
      }
    }
  })
}

// 获取AI分析统计信息
export function getAnalysisStatistics(params = {}) {
  return request({
    url: '/api/test-case/analysis-statistics/',
    method: 'get',
    params: {
      project_id: params.project_id,
      time_range: params.time_range || '7d',
      group_by: params.group_by || 'day' // day, week, month
    }
  })
}

// 获取建议类型配置
export function getSuggestionTypes() {
  return request({
    url: '/api/test-case/suggestion-types/',
    method: 'get'
  })
}

// 更新AI分析配置
export function updateAnalysisConfig(data) {
  return request({
    url: '/api/test-case/analysis-config/',
    method: 'put',
    data: {
      confidence_threshold: data.confidence_threshold || 0.7,
      auto_apply_high_confidence: data.auto_apply_high_confidence || false,
      analysis_depth: data.analysis_depth || 'normal', // shallow, normal, deep
      enable_learning: data.enable_learning || true // 是否启用学习模式
    }
  })
}

export default {
  analyzeFailureAndSuggest,
  getTestCaseSuggestions,
  applySuggestions,
  rejectSuggestions,
  getTestCaseEditHistory,
  getTestCaseForAnalysis,
  getFailedTestCases,
  batchAnalyzeFailures,
  getAnalysisStatistics,
  getSuggestionTypes,
  updateAnalysisConfig
} 