import request from '@/utils/request';

/**
 * 获取项目列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getProjectList(params) {
  return request({
    url: '/api/project/',
    method: 'post',
    data: {
      page: params?.page || 1,
      page_size: params?.pageSize || 50
    }
  });
}

/**
 * 获取项目详情
 * @param {string|number} id - 项目ID
 * @returns {Promise}
 */
export function getProjectDetail(id) {
  return request({
    url: `/api/project/detail`,
    method: 'post',
    data: { id }
  });
}

/**
 * 创建项目
 * @param {Object} data - 项目数据
 * @returns {Promise}
 */
export function createProject(data) {
  return request({
    url: '/api/project/create',
    method: 'post',
    data
  });
}

/**
 * 更新项目
 * @param {Object} data - 项目数据（包含id）
 * @returns {Promise}
 */
export function updateProject(data) {
  return request({
    url: '/api/project/update',
    method: 'post',
    data
  });
}

/**
 * 删除项目
 * @param {string|number} id - 项目ID
 * @returns {Promise}
 */
export function deleteProject(id) {
  return request({
    url: '/api/project/delete',
    method: 'post',
    data: { id }
  });
} 