<template>
  <div class="case-list">
    <div class="list-header">
      <div class="header-title">
        <h3>测试用例列表</h3>
        <el-badge :value="filteredTestCases.length" class="case-count" />
      </div>
      
      <!-- 搜索框 -->
      <el-input
        v-model="searchKeyword"
        placeholder="搜索用例..."
        prefix-icon="Search"
        clearable
        @input="handleSearch"
        class="search-input"
      />
      
      <!-- 过滤器 -->
      <div class="filters">
        <el-select
          v-model="methodFilter"
          placeholder="请求方法"
          clearable
          size="small"
          @change="handleFilter"
        >
          <el-option label="全部" value="" />
          <el-option 
            v-for="method in availableMethods" 
            :key="method"
            :label="method" 
            :value="method"
          />
        </el-select>
        
        <el-select
          v-model="priorityFilter"
          placeholder="优先级"
          clearable
          size="small"
          @change="handleFilter"
        >
          <el-option label="全部" value="" />
          <el-option label="高" value="high" />
          <el-option label="中" value="medium" />
          <el-option label="低" value="low" />
        </el-select>
        
        <el-select
          v-model="statusFilter"
          placeholder="状态"
          clearable
          size="small"
          @change="handleFilter"
        >
          <el-option label="全部" value="" />
          <el-option label="通过" value="pass" />
          <el-option label="失败" value="fail" />
          <el-option label="未执行" value="未执行" />
          <el-option label="未执行" value="pending" />
          <el-option label="成功" value="success" />
          <el-option label="错误" value="error" />
        </el-select>
      </div>
      
      <!-- 操作按钮 -->
      <div class="actions">
        <el-button-group>
          <el-tooltip content="刷新列表" placement="top">
            <el-button 
              :icon="Refresh" 
              @click="$emit('refresh')"
              :loading="loading"
              size="small"
            />
          </el-tooltip>
          <el-tooltip content="批量选择" placement="top">
            <el-button 
              :icon="Select" 
              @click="toggleSelectMode"
              size="small"
              :type="selectMode ? 'primary' : ''"
            />
          </el-tooltip>
          <el-tooltip content="展开/收起分组" placement="top">
            <el-button 
              :icon="expandAll ? FolderOpened : Folder" 
              @click="toggleExpandAll"
              size="small"
            />
          </el-tooltip>
        </el-button-group>
      </div>
    </div>
    
    <div class="list-content" v-loading="loading">
      <!-- 分组视图 -->
      <div v-if="groupBy !== 'none'" class="grouped-list">
        <div 
          v-for="(group, groupKey) in groupedTestCases" 
          :key="groupKey"
          class="group-section"
        >
          <div 
            class="group-header"
            @click="toggleGroup(groupKey)"
          >
            <el-icon class="group-icon">
              <component :is="expandedGroups[groupKey] ? ArrowDown : ArrowRight" />
            </el-icon>
            <span class="group-title">{{ formatGroupTitle(groupKey) }}</span>
            <el-badge :value="group.length" class="group-count" />
          </div>
          
          <el-collapse-transition>
            <div v-show="expandedGroups[groupKey]" class="group-content">
              <TestCaseItem
                v-for="testCase in group"
                :key="testCase.id"
                :test-case="testCase"
                :selected="selectedCases.includes(testCase.id)"
                :select-mode="selectMode"
                @select="handleCaseSelect"
                @drag-start="handleDragStart"
                @drag-end="handleDragEnd"
                @double-click="handleCaseDoubleClick"
              />
            </div>
          </el-collapse-transition>
        </div>
      </div>
      
      <!-- 列表视图 -->
      <div v-else class="flat-list">
        <TestCaseItem
          v-for="testCase in filteredTestCases"
          :key="testCase.id"
          :test-case="testCase"
          :selected="selectedCases.includes(testCase.id)"
          :select-mode="selectMode"
          @select="handleCaseSelect"
          @drag-start="handleDragStart"
          @drag-end="handleDragEnd"
          @double-click="handleCaseDoubleClick"
        />
      </div>
      
      <!-- 空状态 -->
      <div v-if="filteredTestCases.length === 0 && !loading" class="empty-state">
        <el-empty 
          :image-size="120"
          description="暂无测试用例"
        >
          <el-button type="primary" @click="$emit('refresh')">
            刷新列表
          </el-button>
        </el-empty>
      </div>
    </div>
    
    <!-- 底部工具栏 -->
    <div v-if="selectMode && selectedCases.length > 0" class="bottom-toolbar">
      <div class="selected-info">
        已选择 {{ selectedCases.length }} 个用例
      </div>
      <div class="batch-actions">
        <el-button size="small" @click="batchAddToMindMap">
          批量添加到脑图
        </el-button>
        <el-button size="small" type="danger" @click="clearSelection">
          清空选择
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { 
  Search,
  Refresh,
  Select,
  Folder,
  FolderOpened,
  ArrowDown,
  ArrowRight
} from '@element-plus/icons-vue';
import TestCaseItem from './TestCaseItem.vue';

const props = defineProps({
  testCases: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits([
  'refresh',
  'case-dragged',
  'case-selected',
  'batch-operation'
]);

// 响应式数据
const searchKeyword = ref('');
const methodFilter = ref('');
const priorityFilter = ref('');
const statusFilter = ref('');
const groupBy = ref('method'); // 'none', 'method', 'priority', 'status'
const selectMode = ref(false);
const selectedCases = ref([]);
const expandAll = ref(true);
const expandedGroups = ref({});

// 计算属性
const availableMethods = computed(() => {
  const methods = new Set(props.testCases.map(tc => tc.method));
  return Array.from(methods).sort();
});

const filteredTestCases = computed(() => {
  let filtered = props.testCases;
  
  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(testCase => 
      testCase.title.toLowerCase().includes(keyword) ||
      testCase.api_path.toLowerCase().includes(keyword) ||
      testCase.method.toLowerCase().includes(keyword) ||
      (testCase.description && testCase.description.toLowerCase().includes(keyword))
    );
  }
  
  // 方法过滤
  if (methodFilter.value) {
    filtered = filtered.filter(tc => tc.method === methodFilter.value);
  }
  
  // 优先级过滤
  if (priorityFilter.value) {
    filtered = filtered.filter(tc => tc.priority === priorityFilter.value);
  }
  
  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(tc => tc.status === statusFilter.value);
  }
  
  return filtered;
});

const groupedTestCases = computed(() => {
  if (groupBy.value === 'none') return {};
  
  const groups = {};
  filteredTestCases.value.forEach(testCase => {
    const key = testCase[groupBy.value] || '未分类';
    if (!groups[key]) {
      groups[key] = [];
    }
    groups[key].push(testCase);
  });
  
  return groups;
});

// 方法
const handleSearch = () => {
  // 搜索逻辑由计算属性自动处理
};

const handleFilter = () => {
  // 过滤逻辑由计算属性自动处理
};

const toggleSelectMode = () => {
  selectMode.value = !selectMode.value;
  if (!selectMode.value) {
    selectedCases.value = [];
  }
};

const toggleExpandAll = () => {
  expandAll.value = !expandAll.value;
  Object.keys(groupedTestCases.value).forEach(key => {
    expandedGroups.value[key] = expandAll.value;
  });
};

const toggleGroup = (groupKey) => {
  expandedGroups.value[groupKey] = !expandedGroups.value[groupKey];
};

const formatGroupTitle = (groupKey) => {
  const titleMap = {
    'GET': 'GET 请求',
    'POST': 'POST 请求',
    'PUT': 'PUT 请求',
    'DELETE': 'DELETE 请求',
    'PATCH': 'PATCH 请求',
    'OPTIONS': 'OPTIONS 请求',
    'HEAD': 'HEAD 请求',
    'high': '高优先级',
    'medium': '中优先级',
    'low': '低优先级',
    'pass': '测试通过',
    'fail': '测试失败',
    'pending': '未执行',
    'success': '执行成功',
    'error': '执行错误',
    '未执行': '未执行',
    '成功': '执行成功',
    '失败': '执行失败',
    '高': '高优先级',
    '中': '中优先级',
    '低': '低优先级'
  };
  
  return titleMap[groupKey] || groupKey;
};

const handleCaseSelect = (testCaseId, selected) => {
  if (selected) {
    selectedCases.value.push(testCaseId);
  } else {
    const index = selectedCases.value.indexOf(testCaseId);
    if (index > -1) {
      selectedCases.value.splice(index, 1);
    }
  }
  
  emit('case-selected', selectedCases.value);
};

const handleDragStart = (testCase) => {
  // 拖拽开始处理
};

const handleDragEnd = (testCase, targetNodeId) => {
  // 不再 emit('case-dragged', ...) 只做UI清理
};

const handleCaseDoubleClick = (testCase) => {
  // 不再 emit('case-dragged', ...) 只做UI清理
};

const batchAddToMindMap = () => {
  const selectedTestCases = props.testCases.filter(tc => 
    selectedCases.value.includes(tc.id)
  );
  
  emit('batch-operation', 'add-to-mindmap', selectedTestCases);
  clearSelection();
};

const clearSelection = () => {
  selectedCases.value = [];
};

// 监听分组变化，自动展开所有分组
watch(groupedTestCases, (newGroups) => {
  Object.keys(newGroups).forEach(key => {
    if (!(key in expandedGroups.value)) {
      expandedGroups.value[key] = expandAll.value;
    }
  });
}, { immediate: true });
</script>

<style scoped>
.case-list {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  border-radius: 16px;
  box-shadow: 
    0 20px 40px rgba(0,0,0,0.1),
    0 8px 16px rgba(0,0,0,0.05),
    inset 0 1px 0 rgba(255,255,255,0.8);
  border: 1px solid rgba(255,255,255,0.3);
  position: relative;
  overflow: hidden;
}

.case-list::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255,255,255,0.1) 0%, 
    rgba(255,255,255,0.05) 50%, 
    rgba(0,0,0,0.02) 100%);
  pointer-events: none;
}

.list-header {
  padding: 20px;
  border-bottom: 1px solid rgba(0,0,0,0.1);
  flex-shrink: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  position: relative;
  z-index: 1;
  backdrop-filter: blur(10px);
}

.list-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(0,0,0,0.1) 50%, 
    transparent 100%);
}

.header-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.header-title h3 {
  margin: 0;
  font-size: 16px;
  color: var(--el-text-color-primary);
}

.case-count {
  margin-left: 8px;
}

.search-input {
  margin-bottom: 12px;
}

.filters {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  flex-wrap: wrap;
}

.actions {
  display: flex;
  justify-content: flex-end;
}

.list-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.grouped-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.group-section {
  border: 1px solid rgba(0,0,0,0.08);
  border-radius: 12px;
  overflow: hidden;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: 
    0 4px 12px rgba(0,0,0,0.05),
    0 2px 4px rgba(0,0,0,0.03);
  transition: all 0.3s ease;
}

.group-section:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 8px 24px rgba(0,0,0,0.12),
    0 4px 8px rgba(0,0,0,0.08);
}

.group-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.group-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255,255,255,0.1) 0%, 
    rgba(255,255,255,0.05) 50%, 
    rgba(0,0,0,0.05) 100%);
  pointer-events: none;
}

.group-header:hover {
  background: linear-gradient(135deg, #7c3aed 0%, #9333ea 100%);
  transform: translateY(-1px);
}

.group-icon {
  margin-right: 10px;
  transition: transform 0.3s;
  color: #fff;
  position: relative;
  z-index: 1;
}

.group-title {
  flex: 1;
  font-weight: 600;
  color: #fff;
  font-size: 15px;
  position: relative;
  z-index: 1;
}

.group-count {
  margin-left: 10px;
  position: relative;
  z-index: 1;
}

.group-count .el-badge__content {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.group-content {
  padding: 8px;
}

.flat-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 200px;
}

.bottom-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-top: 1px solid rgba(255,255,255,0.2);
  position: relative;
  overflow: hidden;
}

.bottom-toolbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, 
    rgba(255,255,255,0.1) 0%, 
    rgba(255,255,255,0.05) 50%, 
    rgba(0,0,0,0.05) 100%);
  pointer-events: none;
}

.selected-info {
  font-size: 14px;
  color: #fff;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

.batch-actions {
  display: flex;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.batch-actions .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #fff;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.batch-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-1px);
}

/* 滚动条样式 */
.list-content::-webkit-scrollbar {
  width: 6px;
}

.list-content::-webkit-scrollbar-track {
  background: transparent;
}

.list-content::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color-lighter);
  border-radius: 3px;
}

.list-content::-webkit-scrollbar-thumb:hover {
  background-color: var(--el-border-color);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .filters {
    flex-direction: column;
  }
  
  .actions {
    margin-top: 8px;
  }
  
  .bottom-toolbar {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }
}
</style> 