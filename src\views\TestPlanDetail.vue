<template>
  <Home>
    <PageContainer :title="'测试计划详情: ' + (planDetail.name || '')">
      <div v-loading="loading">
        <!-- 基本信息卡片 -->
        <el-card class="detail-card">
          <template #header>
            <div class="card-header">
              <h3>基本信息</h3>
              <div class="header-actions">
                <el-button type="primary" @click="executePlan" :loading="executing">
                  立即执行
                </el-button>
                <el-button @click="goBack">返回</el-button>
              </div>
            </div>
          </template>
          
          <el-descriptions :column="3" border size="small">
            <el-descriptions-item label="计划名称" :span="2">{{ planDetail.name }}</el-descriptions-item>
            <el-descriptions-item label="所属项目">{{ planDetail.projectName }}</el-descriptions-item>
            <el-descriptions-item label="执行方式">
              <el-tag :type="planDetail.scheduleType === 'cron' ? 'warning' : 'info'" size="small">
                {{ planDetail.scheduleType === 'cron' ? '定时执行' : '单次执行' }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="状态">
              <el-tag :type="getStatusType(planDetail.status)" size="small">
                {{ getStatusText(planDetail.status) }}
              </el-tag>
            </el-descriptions-item>
            <el-descriptions-item label="创建人">{{ planDetail.creator }}</el-descriptions-item>
            <el-descriptions-item label="执行时间" :span="2">{{ formatSchedule(planDetail) }}</el-descriptions-item>
            <el-descriptions-item label="重试次数">{{ planDetail.retryTimes || 0 }}次</el-descriptions-item>
            <el-descriptions-item label="最后执行时间" :span="2">
              {{ planDetail.last_run_time ? formatDate(planDetail.last_run_time) : (planDetail.last_executed_at ? formatDate(planDetail.last_executed_at) : '-') }}
            </el-descriptions-item>
            <el-descriptions-item label="通知方式" :span="3">
              <el-tag v-if="planDetail.notifyTypes && planDetail.notifyTypes.includes('email')" 
                      type="success" effect="plain" class="notify-tag" size="small">邮件</el-tag>
              <el-tag v-if="planDetail.notifyTypes && planDetail.notifyTypes.includes('dingtalk')" 
                      type="primary" effect="plain" class="notify-tag" size="small">钉钉</el-tag>
              <el-tag v-if="planDetail.notifyTypes && planDetail.notifyTypes.includes('wecom')" 
                      type="warning" effect="plain" class="notify-tag" size="small">企业微信</el-tag>
              <span v-if="!planDetail.notifyTypes || planDetail.notifyTypes.length === 0">无</span>
            </el-descriptions-item>
            <el-descriptions-item label="描述" :span="3" v-if="planDetail.description">
              {{ planDetail.description }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 测试套件卡片 -->
        <el-card class="detail-card">
          <template #header>
            <div class="card-header">
              <h3>包含的测试套件</h3>
            </div>
          </template>
          
          <el-table :data="testSuites" style="width: 100%">
            <el-table-column prop="id" label="ID" width="60" />
            <el-table-column prop="name" label="套件名称" min-width="200" show-overflow-tooltip />
            <el-table-column prop="case_count" label="用例数量" width="80" align="center" />
            <el-table-column prop="environment_name" label="环境" width="120" show-overflow-tooltip />
            <el-table-column prop="priority" label="优先级" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="getPriorityType(row.priority)" size="small">
                  {{ getPriorityText(row.priority) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="create_time" label="创建时间" width="150" />
          </el-table>
        </el-card>

        <!-- 执行历史卡片 -->
        <el-card class="detail-card">
          <template #header>
            <div class="card-header">
              <h3>执行历史</h3>
              <el-button type="primary" @click="viewAllReports" plain :loading="loadingHistory">刷新执行历史</el-button>
            </div>
          </template>
          
          <el-table :data="executionHistory" style="width: 100%" v-loading="loadingHistory">
            <el-table-column prop="execution_id" label="执行ID" width="70" align="center" />
            <el-table-column prop="start_time" label="执行时间" width="150">
              <template #default="{ row }">
                {{ formatDate(row.start_time) }}
              </template>
            </el-table-column>
            <el-table-column prop="duration" label="执行时长" width="100" align="center">
              <template #default="{ row }">
                {{ row.duration ? row.duration.toFixed(2) + 's' : '-' }}
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="success_rate" label="成功率" width="150">
              <template #default="{ row }">
                <div class="success-rate-cell">
                  <span class="rate-text">{{ row.success_rate }}%</span>
                  <el-progress 
                    :percentage="row.success_rate || 0" 
                    :status="getProgressStatus(row.success_rate || 0)"
                    :stroke-width="6"
                    :show-text="false"
                  />
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="total_cases" label="用例数" width="80" align="center" />
            <el-table-column label="操作" width="100" align="center">
              <template #default="{ row }">
                <el-button 
                  type="primary"
                  link
                  size="small"
                  @click="viewExecutionReport(row)"
                >
                  查看报告
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
    </PageContainer>

    <!-- 测试报告弹窗 -->
    <el-dialog
      v-model="showReportDialog"
      :title="'执行报告: ' + (currentExecution.execution_id || '')"
      width="90%"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      class="report-dialog"
    >
      <div v-loading="reportLoading">
        <!-- 无报告数据时显示 -->
        <div v-if="!hasReportData && !reportLoading" class="no-report-data">
          <el-empty description="暂无测试报告数据" />
        </div>
        
        <!-- 有报告数据时显示 -->
        <div v-if="hasReportData">
          <!-- 执行概览卡片 -->
          <el-card class="report-card" v-if="currentExecution">
            <template #header>
              <div class="card-header">
                <h3>执行概览</h3>
              </div>
            </template>
            
            <div class="overview-container">
              <div class="overview-metrics">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <div class="metric-card">
                      <div class="metric-title">执行状态</div>
                      <div class="metric-value">
                        <el-tag :type="getStatusType(currentExecution.status)" size="large">
                          {{ getStatusText(currentExecution.status) }}
                        </el-tag>
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="metric-card">
                      <div class="metric-title">成功率</div>
                      <div class="metric-value success-rate">
                        {{ currentExecution.success_rate || 0 }}%
                      </div>
                      <el-progress 
                        :percentage="currentExecution.success_rate || 0" 
                        :status="getProgressStatus(currentExecution.success_rate || 0)"
                      />
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="metric-card">
                      <div class="metric-title">执行时长</div>
                      <div class="metric-value execution-time">
                        {{ formatDuration(currentExecution.duration) }}
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="metric-card">
                      <div class="metric-title">用例总数</div>
                      <div class="metric-value total-cases">
                        {{ currentExecution.total_cases || 0 }}
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
              
              <div class="execution-info">
                <el-descriptions :column="2" border size="small">
                  <el-descriptions-item label="执行ID">{{ currentExecution.execution_id }}</el-descriptions-item>
                  <el-descriptions-item label="计划名称">{{ planDetail.name }}</el-descriptions-item>
                  <el-descriptions-item label="执行时间">{{ formatDate(currentExecution.start_time) }}</el-descriptions-item>
                  <el-descriptions-item label="执行时长">{{ formatDuration(currentExecution.duration) }}</el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-card>
        
          <!-- 测试套件结果 -->
          <el-card class="report-card" v-if="suiteResults.length > 0">
            <template #header>
              <div class="card-header">
                <h3>测试套件结果</h3>
              </div>
            </template>
            
            <el-collapse>
              <el-collapse-item 
                v-for="(suite, index) in suiteResults" 
                :key="index"
                :name="index"
              >
                <template #title>
                  <div class="suite-title">
                    <span>{{ suite.suite_name }}</span>
                    <div class="suite-stats">
                      <el-tag size="small" type="success" class="suite-stat">通过: {{ suite.passed_cases || 0 }}</el-tag>
                      <el-tag size="small" type="danger" class="suite-stat">失败: {{ suite.failed_cases || 0 }}</el-tag>
                      <el-tag size="small" type="warning" class="suite-stat">错误: {{ suite.error_cases || 0 }}</el-tag>
                    </div>
                  </div>
                </template>
                
                <el-table 
                  :data="getSuiteTestCases(suite)" 
                  style="width: 100%"
                >
                  <el-table-column type="expand">
                    <template #default="props">
                      <div class="case-detail">
                        <div v-if="props.row.error" class="error-detail">
                          <h4>错误信息</h4>
                          <pre>{{ props.row.error }}</pre>
                        </div>
                        <div v-if="props.row.request || props.row.response" class="request-response">
                          <h4>请求/响应详情</h4>
                          <div class="api-details">
                            <div v-if="props.row.request" class="api-request">
                              <h5>请求信息</h5>
                              <el-descriptions border size="small" :column="1">
                                <el-descriptions-item label="URL">{{ props.row.api_path }}</el-descriptions-item>
                                <el-descriptions-item label="方法">{{ props.row.method }}</el-descriptions-item>
                                <el-descriptions-item label="请求体">
                                  <pre>{{ formatJSON(props.row.request) }}</pre>
                                </el-descriptions-item>
                              </el-descriptions>
                            </div>
                            <div v-if="props.row.response" class="api-response">
                              <h5>响应信息</h5>
                              <el-descriptions border size="small" :column="1">
                                <el-descriptions-item label="响应体">
                                  <pre>{{ formatJSON(props.row.response) }}</pre>
                                </el-descriptions-item>
                              </el-descriptions>
                            </div>
                          </div>
                        </div>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column prop="case_name" label="用例名称" min-width="200" show-overflow-tooltip>
                    <template #default="{ row }">
                      {{ row.case_name || row.title || `用例${row.case_id}` }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="status" label="状态" width="80" align="center">
                    <template #default="{ row }">
                      <el-tag :type="getResultType(row.status)" size="small">
                        {{ getResultText(row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="duration" label="执行时长" width="100" align="center">
                    <template #default="{ row }">
                      {{ formatDuration(row.duration) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="api_path" label="API路径" min-width="250" show-overflow-tooltip />
                  <el-table-column prop="method" label="方法" width="80" align="center">
                    <template #default="{ row }">
                      <el-tag v-if="row.method" type="info" effect="plain" size="small">{{ row.method }}</el-tag>
                      <span v-else>-</span>
                    </template>
                  </el-table-column>
                </el-table>
              </el-collapse-item>
            </el-collapse>
          </el-card>
        </div>
      </div>
    </el-dialog>
  </Home>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import Home from '@/components/HomePage.vue';
import PageContainer from '@/components/PageContainer.vue';
import request from '@/utils/request';
import { formatDate } from '@/utils/format';

const route = useRoute();
const router = useRouter();
const loading = ref(true);
const executing = ref(false);
const loadingHistory = ref(false);
const planDetail = ref({});
const testSuites = ref([]);
const executionHistory = ref([]);

// 报告弹窗相关
const showReportDialog = ref(false);
const reportLoading = ref(false);
const currentExecution = ref({});
const testResults = ref([]);
const suiteResults = ref([]);
const hasReportData = ref(false);

// 从路由参数获取计划ID
const planId = ref(route.query.planId);

// 获取测试计划详情
const fetchPlanDetail = async () => {
  loading.value = true;
  try {
    const response = await request.get(`/api/test-plan/${planId.value}`);
    if (response.data.code === 200) {
      const data = response.data.data;
      planDetail.value = data;
      
      // 字段映射
      if (data.plan_id) {
        planDetail.value.id = data.plan_id;
      }
      if (data.project_id) {
        planDetail.value.projectId = data.project_id;
      }
      if (data.project_name) {
        planDetail.value.projectName = data.project_name;
      }
      if (data.schedule_type) {
        planDetail.value.scheduleType = data.schedule_type;
      }
      if (data.execute_time) {
        planDetail.value.executeTime = data.execute_time;
      }
      if (data.cron_expression) {
        planDetail.value.cronExpression = data.cron_expression;
      }
      if (data.retry_times) {
        planDetail.value.retryTimes = data.retry_times;
      }
      if (data.notify_types) {
        planDetail.value.notifyTypes = data.notify_types;
      }
      if (data.last_executed_at) {
        planDetail.value.last_run_time = data.last_executed_at;
      }
      
      // 处理测试套件数据
      if (data.suites && Array.isArray(data.suites)) {
        testSuites.value = data.suites.map(suite => ({
          id: suite.suite_id,
          name: suite.name,
          case_count: suite.case_count || 0,
          priority: suite.priority || 'medium',
          create_time: suite.create_time || '-',
          environment_name: suite.environment_name || suite.environment_cover_name || '-'
        }));
      }
    }
  } catch (error) {
    console.error('获取测试计划详情失败:', error);
    ElMessage.error('获取测试计划详情失败');
  } finally {
    loading.value = false;
  }
};

// 获取测试套件列表 - 不再需要单独调用，数据已在详情接口中返回
const fetchTestSuites = async () => {
  // 这个方法现在不需要了，因为数据已经在fetchPlanDetail中处理
};

// 获取完整执行历史
const fetchExecutionHistory = async () => {
  loadingHistory.value = true;
  try {
    const response = await request.get(`/api/test-plan/${planId.value}/executions`);
    if (response.data.code === 200) {
      const executions = response.data.data.executions || response.data.data || [];
      executionHistory.value = executions.map(result => {
        // 计算实际的统计数据
        let totalCases = 0;
        let passedCases = 0;
        let failedCases = 0;
        let errorCases = 0;
        
        // 优先使用新的API字段
        if (result.case_count !== undefined && result.case_statistics) {
          totalCases = result.case_count;
          passedCases = result.case_statistics.passed || 0;
          failedCases = result.case_statistics.failed || 0;
          errorCases = result.case_statistics.error || 0;
        } 
        // 从套件结果中统计
        else if (result.result_data && result.result_data.detailed_suite_results) {
          result.result_data.detailed_suite_results.forEach(suite => {
            if (suite.result_data && suite.result_data.results) {
              suite.result_data.results.forEach(testCase => {
                totalCases++;
                const status = testCase.status.toLowerCase();
                if (status === 'pass' || status === 'success') {
                  passedCases++;
                } else if (status === 'fail' || status === 'failed') {
                  failedCases++;
                } else if (status === 'error') {
                  errorCases++;
                }
              });
            }
          });
        }
        // 使用原有字段作为后备
        else {
          totalCases = result.total_cases || 0;
          passedCases = result.passed_cases || 0;
          failedCases = result.failed_cases || 0;
          errorCases = result.error_cases || 0;
        }
        
        // 计算成功率
        const successRate = totalCases > 0 ? Math.round((passedCases / totalCases) * 100) : 0;
        
        return {
          execution_id: result.result_id || result.execution_id,
          start_time: result.execution_time || result.start_time,
          end_time: result.end_time || result.execution_time,
          status: result.status,
          success_rate: successRate,
          total_cases: totalCases,
          passed_cases: passedCases,
          failed_cases: failedCases,
          error_cases: errorCases,
          duration: result.duration || 0
        };
      });
    }
  } catch (error) {
    console.error('获取执行历史失败:', error);
    ElMessage.error('获取执行历史失败');
  } finally {
    loadingHistory.value = false;
  }
};

// 执行测试计划
const executePlan = async () => {
  executing.value = true;
  try {
    const response = await request.post(`/api/test-plan/execute/${planId.value}`);
    if (response.data.code === 200) {
      ElMessage.success('已开始执行');
      
      // 重新获取测试计划详情和执行历史
      await fetchPlanDetail();
      await fetchExecutionHistory();
    }
  } catch (error) {
    console.error('执行测试计划失败:', error);
    ElMessage.error('执行失败');
  } finally {
    executing.value = false;
  }
};

// 返回测试计划列表页
const goBack = () => {
  router.push('/test-plan');
};

// 获取执行报告详细数据
const fetchExecutionReportData = async (executionId) => {
  reportLoading.value = true;
  hasReportData.value = false;
  testResults.value = [];
  suiteResults.value = [];
  
  try {
    // 调用执行历史接口获取所有执行记录
    const response = await request.get(`/api/test-plan/${planId.value}/executions`);
    if (response.data.code === 200) {
      const executions = response.data.data.executions || response.data.data || [];
      
      // 找到指定的执行记录
      const targetExecution = executions.find(exec => 
        (exec.result_id && exec.result_id.toString() === executionId.toString()) ||
        (exec.execution_id && exec.execution_id.toString() === executionId.toString())
      );
      
      if (targetExecution) {
        // 更新当前执行记录的详细信息
        
        // 计算统计数据
        let totalCases = 0;
        let passedCases = 0;
        let failedCases = 0;
        let errorCases = 0;
        
        // 从套件结果中统计用例数据
        if (targetExecution.result_data && targetExecution.result_data.detailed_suite_results) {
          targetExecution.result_data.detailed_suite_results.forEach(suite => {
            if (suite.result_data && suite.result_data.results) {
              suite.result_data.results.forEach(testCase => {
                totalCases++;
                const status = testCase.status.toLowerCase();
                if (status === 'pass' || status === 'success') {
                  passedCases++;
                } else if (status === 'fail' || status === 'failed') {
                  failedCases++;
                } else if (status === 'error') {
                  errorCases++;
                }
              });
            }
          });
        }
        
        // 计算成功率
        const successRate = totalCases > 0 ? Math.round((passedCases / totalCases) * 100) : 0;
        
        currentExecution.value = {
          execution_id: targetExecution.result_id || targetExecution.execution_id,
          start_time: targetExecution.execution_time || targetExecution.start_time,
          end_time: targetExecution.end_time || targetExecution.execution_time,
          status: targetExecution.status,
          success_rate: successRate,
          total_cases: totalCases,
          passed_cases: passedCases,
          failed_cases: failedCases,
          error_cases: errorCases,
          duration: (targetExecution.duration || 0) * 1000 // 转换为毫秒
        };
        
        // 处理套件结果数据
        if (targetExecution.result_data && targetExecution.result_data.detailed_suite_results) {
          // 为每个套件计算统计信息
          suiteResults.value = targetExecution.result_data.detailed_suite_results.map(suite => {
            let suitePassed = 0;
            let suiteFailed = 0;
            let suiteError = 0;
            let suiteTotal = 0;
            
            if (suite.result_data && suite.result_data.results) {
              suite.result_data.results.forEach(testCase => {
                suiteTotal++;
                const status = testCase.status.toLowerCase();
                if (status === 'pass' || status === 'success') {
                  suitePassed++;
                } else if (status === 'fail' || status === 'failed') {
                  suiteFailed++;
                } else if (status === 'error') {
                  suiteError++;
                }
              });
            }
            
            return {
              ...suite,
              passed_cases: suitePassed,
              failed_cases: suiteFailed,
              error_cases: suiteError,
              total_cases: suiteTotal,
              pass_rate: suiteTotal > 0 ? Math.round((suitePassed / suiteTotal) * 100) : 0
            };
          });
          
          // 解析测试用例结果
          testResults.value = parseTestResults(targetExecution.result_data);
        }
        
        hasReportData.value = true;
      } else {
        ElMessage.warning('未找到指定的执行记录');
        hasReportData.value = false;
      }
    }
  } catch (error) {
    console.error('获取执行报告数据失败:', error);
    ElMessage.error('获取执行报告数据失败');
    hasReportData.value = false;
  } finally {
    reportLoading.value = false;
  }
};

// 解析测试结果
const parseTestResults = (resultData) => {
  const results = [];
  
  // 遍历套件结果
  if (resultData.detailed_suite_results) {
    resultData.detailed_suite_results.forEach(suite => {
      if (suite.result_data && suite.result_data.results) {
        // 遍历用例结果
        suite.result_data.results.forEach(testCase => {
          results.push({
            case_id: testCase.case_id || testCase.id,
            case_name: testCase.title || testCase.name || testCase.case_name || `用例${testCase.case_id || ''}`,
            status: testCase.status.toLowerCase(),
            duration: (testCase.duration || 0) * 1000, // 转换为毫秒
            suite_name: suite.suite_name,
            error: testCase.error || null,
            api_path: testCase.api_path || testCase.url || '',
            method: testCase.method || testCase.request_method || '',
            request: testCase.request || null,
            response: testCase.response || null
          });
        });
      }
    });
  }
  
  console.log('解析后的测试结果:', results);
  return results;
};

// 查看执行报告
const viewExecutionReport = async (execution) => {
  currentExecution.value = execution;
  showReportDialog.value = true;
  await fetchExecutionReportData(execution.execution_id);
};

// 查看所有报告
const viewAllReports = async () => {
  try {
    // 获取完整的执行历史
    await fetchExecutionHistory();
    ElMessage.success('已加载完整执行历史');
  } catch (error) {
    console.error('加载执行历史失败:', error);
    ElMessage.error('加载执行历史失败');
  }
};

// 获取状态类型
const getStatusType = (status) => {
  const types = {
    pending: 'info',
    running: 'warning',
    completed: 'success',
    failed: 'danger',
    fail: 'danger'
  };
  return types[status] || 'info';
};

// 获取状态文本
const getStatusText = (status) => {
  const texts = {
    pending: '未执行',
    running: '执行中',
    completed: '已完成',
    failed: '已失败',
    fail: '失败'
  };
  return texts[status] || status;
};

// 获取优先级类型
const getPriorityType = (priority) => {
  const types = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  };
  return types[priority] || 'info';
};

// 获取优先级文本
const getPriorityText = (priority) => {
  const texts = {
    high: '高',
    medium: '中',
    low: '低'
  };
  return texts[priority] || priority;
};

// 获取进度条状态
const getProgressStatus = (rate) => {
  if (rate >= 90) return 'success';
  if (rate >= 60) return 'warning';
  return 'exception';
};

// 格式化执行时间显示
const formatSchedule = (row) => {
  if (!row) return '';
  
  if (row.scheduleType === 'once' || row.schedule_type === 'once') {
    return formatDate(row.executeTime || row.execute_time);
  }
  return row.cronExpression || row.cron_expression;
};

// 获取指定套件的测试用例
const getSuiteTestCases = (suite) => {
  if (!suite.result_data || !suite.result_data.results) return [];
  
  // 处理用例数据，确保包含所有必要字段
  return suite.result_data.results.map(testCase => ({
    ...testCase,
    case_id: testCase.case_id || testCase.id,
    case_name: testCase.title || testCase.name || testCase.case_name || `用例${testCase.case_id || ''}`,
    api_path: testCase.api_path || testCase.url || '',
    method: testCase.method || testCase.request_method || '',
    duration: (testCase.duration || 0) * 1000
  }));
};

// 格式化JSON数据
const formatJSON = (data) => {
  if (!data) return '{}';
  try {
    if (typeof data === 'string') {
      const parsedData = JSON.parse(data);
      return JSON.stringify(parsedData, null, 2);
    } else {
      return JSON.stringify(data, null, 2);
    }
  } catch (e) {
    return data;
  }
};

// 格式化持续时间
const formatDuration = (duration) => {
  if (!duration && duration !== 0) return '-';
  
  if (duration < 1000) {
    return `${duration}ms`;
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(2)}s`;
  } else {
    const minutes = Math.floor(duration / 60000);
    const seconds = ((duration % 60000) / 1000).toFixed(0);
    return `${minutes}m ${seconds}s`;
  }
};

// 获取结果类型
const getResultType = (status) => {
  const statusLower = status.toLowerCase();
  const types = {
    success: 'success',
    pass: 'success',
    failed: 'danger',
    fail: 'danger',
    error: 'error',
    skipped: 'info',
    skip: 'info'
  };
  return types[statusLower] || 'info';
};

// 获取结果文本
const getResultText = (status) => {
  const statusLower = status.toLowerCase();
  const texts = {
    success: '成功',
    pass: '成功',
    failed: '失败',
    fail: '失败',
    error: '错误',
    skipped: '跳过',
    skip: '跳过'
  };
  return texts[statusLower] || status;
};

onMounted(async () => {
  if (!planId.value) {
    ElMessage.error('计划ID不能为空');
    goBack();
    return;
  }
  
  // 获取基本计划详情和测试套件信息
  await fetchPlanDetail();
  // 获取完整的执行历史
  await fetchExecutionHistory();
});
</script>

<style scoped>
.detail-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.notify-tag {
  margin-right: 8px;
}

:deep(.el-descriptions__label) {
  font-weight: bold;
  width: 140px;
}

:deep(.el-descriptions) {
  max-width: 100%;
}

/* 页面容器宽度优化 */
:deep(.page-container) {
  max-width: 1400px;
  margin: 0 auto;
}

/* 表格宽度优化 */
:deep(.el-table) {
  max-width: 100%;
}

:deep(.el-descriptions__content) {
  line-height: 1.5;
}

:deep(.el-progress) {
  margin-right: 0;
}

/* 报告相关样式 */
.report-card {
  margin-bottom: 20px;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.overview-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.overview-metrics {
  margin-bottom: 20px;
}

.metric-card {
  background-color: #f5f7fa;
  border-radius: 8px;
  padding: 15px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.metric-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 10px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.success-rate {
  color: #67C23A;
}

.execution-time {
  color: #409EFF;
}

.total-cases {
  color: #E6A23C;
}

.case-detail {
  padding: 20px;
  background-color: #f5f7fa;
  border-radius: 8px;
}

.error-detail {
  margin-bottom: 20px;
}

.error-detail h4 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #F56C6C;
}

.error-detail pre {
  background-color: #303133;
  color: #fff;
  padding: 15px;
  border-radius: 5px;
  overflow-x: auto;
  margin: 0;
}

.request-response {
  margin-top: 20px;
}

.request-response h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #409EFF;
}

.api-details {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.api-request h5, .api-response h5 {
  margin-top: 0;
  margin-bottom: 10px;
  color: #606266;
}

.api-details pre {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 0;
  font-size: 12px;
  max-height: 200px;
  border-left: 3px solid #409EFF;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.suite-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 5px 0;
}

.suite-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.suite-stat {
  margin-right: 5px;
}

.no-report-data {
  display: flex;
  justify-content: center;
  padding: 50px 0;
}

/* 折叠面板样式 */
:deep(.el-collapse-item__header) {
  font-size: 16px;
  font-weight: 500;
  background-color: #f5f7fa;
  padding: 0 15px;
  border-radius: 4px;
}

:deep(.el-collapse-item__content) {
  padding: 15px;
}

/* 报告对话框宽度优化 */
:deep(.report-dialog) {
  max-width: 1600px;
  margin: 0 auto;
}

:deep(.report-dialog .el-dialog__body) {
  max-height: 80vh;
  overflow-y: auto;
}

/* 响应式布局优化 */
@media screen and (max-width: 1920px) {
  :deep(.page-container) {
    max-width: 1200px;
  }
  
  :deep(.report-dialog) {
    max-width: 1400px;
  }
}

@media screen and (max-width: 1600px) {
  :deep(.page-container) {
    max-width: 1000px;
  }
  
  :deep(.report-dialog) {
    max-width: 1200px;
  }
}

@media screen and (max-width: 1200px) {
  :deep(.page-container) {
    max-width: 95%;
  }
  
  :deep(.report-dialog) {
    width: 95% !important;
    max-width: none;
  }
}

@media screen and (max-width: 768px) {
  :deep(.page-container) {
    max-width: 100%;
    padding: 10px;
  }
  
  :deep(.report-dialog) {
    width: 95% !important;
    margin: 10px auto;
  }
  
  :deep(.el-descriptions__label) {
    width: 100px;
  }
}

/* 表格行高优化 */
:deep(.el-table .el-table__row) {
  height: 48px;
}

:deep(.el-table .el-table__header-wrapper .el-table__header .el-table__cell) {
  height: 44px;
  padding: 8px 0;
}

:deep(.el-table .el-table__body-wrapper .el-table__body .el-table__cell) {
  padding: 8px 0;
}

/* 紧凑模式优化 */
:deep(.el-descriptions--small .el-descriptions__body .el-descriptions__table .el-descriptions__cell) {
  padding: 8px 16px;
}

:deep(.el-card__body) {
  padding: 16px;
}

:deep(.el-card__header) {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
}
</style> 