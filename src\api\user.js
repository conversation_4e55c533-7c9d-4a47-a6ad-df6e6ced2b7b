import request from '@/utils/request';

/**
 * 获取用户列表
 * @param {Object} params - 查询参数
 * @returns {Promise}
 */
export function getUserList(params) {
  return request({
    url: '/api/users',
    method: 'get',
    params
  });
}

/**
 * 获取用户详情
 * @param {string|number} id - 用户ID
 * @returns {Promise}
 */
export function getUserDetail(id) {
  return request({
    url: `/api/users/${id}`,
    method: 'get'
  });
}

/**
 * 创建用户
 * @param {Object} data - 用户数据
 * @returns {Promise}
 */
export function createUser(data) {
  return request({
    url: '/api/users',
    method: 'post',
    data
  });
}

/**
 * 更新用户
 * @param {string|number} id - 用户ID
 * @param {Object} data - 用户数据
 * @returns {Promise}
 */
export function updateUser(id, data) {
  return request({
    url: `/api/users/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除用户
 * @param {string|number} id - 用户ID
 * @returns {Promise}
 */
export function deleteUser(id) {
  return request({
    url: `/api/users/${id}`,
    method: 'delete'
  });
}

/**
 * 批量删除用户
 * @param {Array} userIds - 用户ID数组
 * @returns {Promise}
 */
export function batchDeleteUsers(userIds) {
  return request({
    url: '/api/users/batch-delete',
    method: 'post',
    data: { userIds }
  });
}

/**
 * 更新用户状态
 * @param {string|number} id - 用户ID
 * @param {boolean} active - 状态
 * @returns {Promise}
 */
export function updateUserStatus(id, active) {
  return request({
    url: `/api/users/${id}/status`,
    method: 'put',
    data: { active }
  });
}

/**
 * 重置用户密码
 * @param {string|number} id - 用户ID
 * @param {string} password - 新密码
 * @returns {Promise}
 */
export function resetUserPassword(id, password) {
  return request({
    url: `/api/users/${id}/password`,
    method: 'put',
    data: { password }
  });
}

/**
 * 获取当前用户信息
 * @returns {Promise}
 */
export function getCurrentUser() {
  return request({
    url: '/api/user/info',
    method: 'get'
  });
}

/**
 * 更新当前用户信息
 * @param {Object} data - 用户数据
 * @returns {Promise}
 */
export function updateCurrentUser(data) {
  return request({
    url: '/api/user/info',
    method: 'put',
    data
  });
}

/**
 * 更新当前用户密码
 * @param {Object} data - 包含旧密码和新密码
 * @returns {Promise}
 */
export function updatePassword(data) {
  return request({
    url: '/api/user/password',
    method: 'put',
    data
  });
}

/**
 * 上传用户头像
 * @param {File} file - 头像文件
 * @returns {Promise}
 */
export function uploadAvatar(file) {
  const formData = new FormData();
  formData.append('file', file);
  
  return request({
    url: '/api/user/avatar',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
} 