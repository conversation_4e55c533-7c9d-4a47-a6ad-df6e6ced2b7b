import { ref, computed } from 'vue';
import request from '@/utils/request';

export function useTestCases(projectId) {
  const testCases = ref([]);
  const testCasesLoading = ref(false);
  const testCasesError = ref(null);
  const searchKeyword = ref('');
  const selectedMethod = ref('');
  const selectedPriority = ref('');
  const selectedStatus = ref('');

  // 获取测试用例列表
  const fetchTestCases = async (params = {}) => {
    if (!projectId.value) {
      console.warn('useTestCases: Project ID is not available');
      return;
    }

    try {
      testCasesLoading.value = true;
      testCasesError.value = null;
      
      console.log('useTestCases: Fetching test cases for project:', projectId.value);
      
      const queryParams = {
        project_id: projectId.value,
        page: params.page || 1,
        page_size: params.pageSize || 100,
        search: params.search || '',
        method: params.method || '',
        priority: params.priority || '',
        status: params.status || ''
      };

      // 尝试多个可能的接口路径
      let response;
      try {
        response = await request.get('/api/testcase/list', { params: queryParams });
      } catch (error) {
        // 如果主接口失败，尝试其他可能的路径
        console.warn('useTestCases: Primary API failed, trying alternative paths:', error);
        try {
          response = await request.get('/api/testcases', { params: queryParams });
        } catch (error2) {
          try {
            response = await request.get('/api/test-cases', { params: queryParams });
          } catch (error3) {
            throw error; // 抛出原始错误
          }
        }
      }
      
      if (response.data.code === 200) {
        // 处理后端返回的数据，支持不同的数据结构
        const data = response.data.data;
        let cases = [];
        
        if (data.testCases) {
          // 新的数据结构: { total: 17, testCases: [...] }
          cases = data.testCases;
        } else if (data.items) {
          // 原有的数据结构: { items: [...] }
          cases = data.items;
        } else if (Array.isArray(data)) {
          // 直接是数组
          cases = data;
        }
        
        // 字段名映射，统一转换为前端期望的格式
        testCases.value = cases.map(item => ({
          id: item.case_id || item.id,
          title: item.title,
          description: item.description || '',
          api_path: item.api_path,
          method: item.method,
          priority: item.priority || 'medium',
          status: item.status || 'active',
          headers: item.headers || {},
          parameters: item.params || item.parameters || {},
          body: item.body || {},
          expected_result: item.expected_result || {},
          assertions: item.assertions || '',
          extractors: item.extractors || [],
          tests: item.tests || [],
          execution_count: item.execution_count || 0,
          success_rate: item.success_rate || 0,
          creator: item.creator || { id: 1, username: 'unknown' },
          created_at: item.create_time || item.created_at,
          updated_at: item.update_time || item.updated_at,
          last_execution_time: item.last_execution_time
        }));
        
        console.log('useTestCases: Successfully loaded test cases:', testCases.value.length);
      } else {
        throw new Error(response.data.message || '获取测试用例失败');
      }
    } catch (error) {
      console.error('useTestCases: 获取测试用例失败:', error);
      testCasesError.value = error.message;
      
      // 使用模拟数据作为后备
      console.log('useTestCases: Using mock data as fallback');
      testCases.value = generateMockTestCases();
    } finally {
      testCasesLoading.value = false;
    }
  };

  // 搜索测试用例
  const searchTestCases = async (keyword) => {
    searchKeyword.value = keyword;
    await fetchTestCases({
      search: keyword,
      method: selectedMethod.value,
      priority: selectedPriority.value,
      status: selectedStatus.value
    });
  };

  // 过滤测试用例
  const filterTestCases = async (filters) => {
    selectedMethod.value = filters.method || '';
    selectedPriority.value = filters.priority || '';
    selectedStatus.value = filters.status || '';
    
    await fetchTestCases({
      search: searchKeyword.value,
      method: filters.method,
      priority: filters.priority,
      status: filters.status
    });
  };

  // 获取单个测试用例详情
  const getTestCaseDetail = async (caseId) => {
    try {
      const response = await request.get(`/api/testcase/${caseId}`);
      
      if (response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || '获取测试用例详情失败');
      }
    } catch (error) {
      console.error('获取测试用例详情失败:', error);
      // 返回模拟数据
      return testCases.value.find(tc => tc.id === caseId) || null;
    }
  };

  // 执行测试用例
  const executeTestCase = async (caseId, params = {}) => {
    try {
      const response = await request.post(`/api/testcase/execute/${caseId}`, {
        parameters: params.parameters || {},
        variables: params.variables || {},
        timeout: params.timeout || 30000
      });
      
      if (response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || '执行测试用例失败');
      }
    } catch (error) {
      console.error('执行测试用例失败:', error);
      throw error;
    }
  };

  // 批量执行测试用例
  const batchExecuteTestCases = async (caseIds, params = {}) => {
    try {
      const response = await request.post('/api/testcase/batch-execute', {
        case_ids: caseIds,
        parameters: params.parameters || {},
        variables: params.variables || {},
        timeout: params.timeout || 30000
      });
      
      if (response.data.code === 200) {
        return response.data.data;
      } else {
        throw new Error(response.data.message || '批量执行测试用例失败');
      }
    } catch (error) {
      console.error('批量执行测试用例失败:', error);
      throw error;
    }
  };

  // 生成模拟测试用例数据
  const generateMockTestCases = () => {
    return [
      {
        id: '1',
        title: '用户登录接口测试',
        description: '测试用户登录功能',
        api_path: '/api/user/login',
        method: 'POST',
        priority: 'high',
        status: 'active',
        headers: {
          'Content-Type': 'application/json'
        },
        parameters: {
          username: '<EMAIL>',
          password: '123456'
        },
        expected_result: {
          code: 200,
          message: '登录成功'
        },
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      },
      {
        id: '2',
        title: '获取用户信息接口测试',
        description: '测试获取用户信息功能',
        api_path: '/api/user/info',
        method: 'GET',
        priority: 'medium',
        status: 'active',
        headers: {
          'Authorization': 'Bearer {{token}}'
        },
        parameters: {},
        expected_result: {
          code: 200,
          data: {
            id: 1,
            username: '<EMAIL>'
          }
        },
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      },
      {
        id: '3',
        title: '创建项目接口测试',
        description: '测试创建项目功能',
        api_path: '/api/project/create',
        method: 'POST',
        priority: 'high',
        status: 'active',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer {{token}}'
        },
        parameters: {
          name: '测试项目',
          description: '这是一个测试项目'
        },
        expected_result: {
          code: 200,
          message: '创建成功'
        },
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      },
      {
        id: '4',
        title: '更新项目信息接口测试',
        description: '测试更新项目信息功能',
        api_path: '/api/project/update',
        method: 'PUT',
        priority: 'medium',
        status: 'active',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer {{token}}'
        },
        parameters: {
          id: '{{project_id}}',
          name: '更新后的项目名称'
        },
        expected_result: {
          code: 200,
          message: '更新成功'
        },
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      },
      {
        id: '5',
        title: '删除项目接口测试',
        description: '测试删除项目功能',
        api_path: '/api/project/delete',
        method: 'DELETE',
        priority: 'low',
        status: 'active',
        headers: {
          'Authorization': 'Bearer {{token}}'
        },
        parameters: {
          id: '{{project_id}}'
        },
        expected_result: {
          code: 200,
          message: '删除成功'
        },
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z'
      }
    ];
  };

  // 计算属性
  const filteredTestCases = computed(() => {
    let filtered = testCases.value;
    
    // 搜索过滤
    if (searchKeyword.value) {
      const keyword = searchKeyword.value.toLowerCase();
      filtered = filtered.filter(tc => 
        tc.title.toLowerCase().includes(keyword) ||
        tc.description.toLowerCase().includes(keyword) ||
        tc.api_path.toLowerCase().includes(keyword)
      );
    }
    
    // 方法过滤
    if (selectedMethod.value) {
      filtered = filtered.filter(tc => tc.method === selectedMethod.value);
    }
    
    // 优先级过滤
    if (selectedPriority.value) {
      filtered = filtered.filter(tc => tc.priority === selectedPriority.value);
    }
    
    // 状态过滤
    if (selectedStatus.value) {
      filtered = filtered.filter(tc => tc.status === selectedStatus.value);
    }
    
    return filtered;
  });

  // 按方法分组
  const testCasesByMethod = computed(() => {
    const groups = {};
    
    filteredTestCases.value.forEach(tc => {
      if (!groups[tc.method]) {
        groups[tc.method] = [];
      }
      groups[tc.method].push(tc);
    });
    
    return groups;
  });

  // 按优先级分组
  const testCasesByPriority = computed(() => {
    const groups = {};
    
    filteredTestCases.value.forEach(tc => {
      if (!groups[tc.priority]) {
        groups[tc.priority] = [];
      }
      groups[tc.priority].push(tc);
    });
    
    return groups;
  });

  // 统计信息
  const testCasesStats = computed(() => {
    const total = testCases.value.length;
    const methods = {};
    const priorities = {};
    const statuses = {};
    
    testCases.value.forEach(tc => {
      methods[tc.method] = (methods[tc.method] || 0) + 1;
      priorities[tc.priority] = (priorities[tc.priority] || 0) + 1;
      statuses[tc.status] = (statuses[tc.status] || 0) + 1;
    });
    
    return {
      total,
      methods,
      priorities,
      statuses
    };
  });

  // 获取可用的HTTP方法
  const availableMethods = computed(() => {
    return [...new Set(testCases.value.map(tc => tc.method))].sort();
  });

  // 获取可用的优先级
  const availablePriorities = computed(() => {
    return [...new Set(testCases.value.map(tc => tc.priority))].sort();
  });

  // 获取可用的状态
  const availableStatuses = computed(() => {
    return [...new Set(testCases.value.map(tc => tc.status))].sort();
  });

  return {
    // 状态
    testCases,
    testCasesLoading,
    testCasesError,
    searchKeyword,
    selectedMethod,
    selectedPriority,
    selectedStatus,
    
    // 操作方法
    fetchTestCases,
    searchTestCases,
    filterTestCases,
    getTestCaseDetail,
    executeTestCase,
    batchExecuteTestCases,
    
    // 计算属性
    filteredTestCases,
    testCasesByMethod,
    testCasesByPriority,
    testCasesStats,
    availableMethods,
    availablePriorities,
    availableStatuses
  };
} 